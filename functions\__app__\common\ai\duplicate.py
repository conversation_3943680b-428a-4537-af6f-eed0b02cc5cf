"""
Duplicate Detection
Identifies potential duplicate work items using hybrid search and similarity thresholds.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from ..models.schemas import WorkItem, SearchResult, DuplicateHit
from ..adapters.search_client import SearchClient
from ..ai.embeddings import EmbeddingService
from ..utils.config import Config
from ..utils.text import calculate_text_similarity, extract_signature
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


@dataclass
class DuplicateCandidate:
    """Represents a potential duplicate work item."""
    work_item_id: int
    title: str
    description: str
    similarity_score: float
    text_similarity: float
    signature_match: bool
    area_path_match: bool
    type_match: bool


class DuplicateDetector:
    """Service for detecting duplicate work items."""
    
    def __init__(self, search_client: SearchClient, config: Config):
        self.search_client = search_client
        self.config = config
        self.embedding_service = EmbeddingService(config)
        
        # Configuration thresholds
        self.similarity_threshold = config.get('DUPLICATE_SIMILARITY_THRESHOLD', 0.85)
        self.text_similarity_threshold = config.get('DUPLICATE_TEXT_THRESHOLD', 0.8)
        self.signature_weight = config.get('DUPLICATE_SIGNATURE_WEIGHT', 0.3)
        self.max_candidates = config.get('DUPLICATE_MAX_CANDIDATES', 10)
        
        # Weights for different similarity factors
        self.weights = {
            'semantic': getattr(config, 'DUPLICATE_WEIGHT_SEMANTIC', 0.4),
            'text': getattr(config, 'DUPLICATE_WEIGHT_TEXT', 0.3),
            'signature': getattr(config, 'DUPLICATE_WEIGHT_SIGNATURE', 0.2),
            'metadata': getattr(config, 'DUPLICATE_WEIGHT_METADATA', 0.1)
        }
    
    async def find_duplicates(self, work_item: WorkItem) -> List[DuplicateHit]:
        """
        Find potential duplicate work items for the given work item.

        ⚠️ PRODUCTION SAFETY: Only processes Bug 748404 during development.

        Args:
            work_item: The work item to check for duplicates

        Returns:
            List of potential duplicates with similarity scores
        """
        # PRODUCTION SAFETY: Only process Bug 748404
        if work_item.id != 748404:
            log_structured(
                logger,
                "warning",
                f"PRODUCTION SAFETY: Duplicate detector ignoring work item {work_item.id} - only Bug 748404 is processed",
                extra={"work_item_id": work_item.id, "allowed_id": 748404}
            )
            return []

        try:
            log_structured(
                logger,
                "info",
                "Starting duplicate detection",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title or "") > 50 else work_item.title
                }
            )
            
            # Generate embedding for the work item
            embedding = await self.embedding_service.embed_work_item(work_item)
            
            # Find candidates using hybrid search
            candidates = await self._find_candidates(work_item, embedding)
            
            # Score and rank candidates
            scored_candidates = await self._score_candidates(work_item, candidates)
            
            # Filter by threshold and convert to DuplicateHit objects
            duplicates = self._filter_and_convert_duplicates(scored_candidates)
            
            log_structured(
                logger,
                "info",
                "Duplicate detection completed",
                extra={
                    "work_item_id": work_item.id,
                    "candidates_found": len(candidates),
                    "duplicates_found": len(duplicates)
                }
            )
            
            return duplicates
            
        except Exception as e:
            logger.error(f"Error in duplicate detection for work item {work_item.id}: {e}")
            return []
    
    async def _find_candidates(
        self, 
        work_item: WorkItem, 
        embedding: List[float]
    ) -> List[SearchResult]:
        """
        Find candidate work items using hybrid search.
        """
        try:
            # Build search query from title and description
            query_parts = []
            if work_item.title:
                query_parts.append(work_item.title)
            if work_item.description:
                # Take first 200 chars of description to avoid too long queries
                desc_snippet = work_item.description[:200]
                query_parts.append(desc_snippet)
            
            query_text = " ".join(query_parts)
            
            # Build filters to exclude the same item and focus on relevant items
            filters = []
            if work_item.id:
                filters.append(f"id ne '{work_item.id}'")
            
            # Only search within same work item type or similar types
            if work_item.work_item_type:
                type_filter = self._build_type_filter(work_item.work_item_type)
                if type_filter:
                    filters.append(type_filter)
            
            # Exclude resolved/closed items unless specifically configured
            if not self.config.get('DUPLICATE_INCLUDE_CLOSED', False):
                filters.append("state ne 'Closed' and state ne 'Resolved' and state ne 'Done'")
            
            filter_expression = " and ".join(filters) if filters else None
            
            # Perform hybrid search
            search_results = await self.search_client.hybrid_search(
                query_text=query_text,
                query_vector=embedding,
                filters=filter_expression,
                top=self.max_candidates * 2  # Get more to allow for filtering
            )
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error finding duplicate candidates: {e}")
            return []
    
    async def _score_candidates(
        self, 
        work_item: WorkItem, 
        candidates: List[SearchResult]
    ) -> List[DuplicateCandidate]:
        """
        Score candidate work items for duplicate likelihood.
        """
        scored_candidates = []
        
        # Extract signature from the original work item
        work_item_signature = extract_signature(work_item.title, work_item.description)
        
        for candidate in candidates:
            try:
                # Calculate text similarity
                text_sim = calculate_text_similarity(
                    work_item.title + " " + (work_item.description or ""),
                    candidate.title + " " + (candidate.description or "")
                )
                
                # Check signature match
                candidate_signature = extract_signature(candidate.title, candidate.description)
                signature_match = work_item_signature == candidate_signature if work_item_signature and candidate_signature else False
                
                # Check metadata matches
                area_path_match = work_item.area_path == candidate.area_path if work_item.area_path and candidate.area_path else False
                type_match = work_item.work_item_type == candidate.work_item_type
                
                # Calculate composite score
                composite_score = self._calculate_composite_score(
                    semantic_score=candidate.score,
                    text_similarity=text_sim,
                    signature_match=signature_match,
                    area_path_match=area_path_match,
                    type_match=type_match
                )
                
                scored_candidate = DuplicateCandidate(
                    work_item_id=candidate.work_item_id,
                    title=candidate.title,
                    description=candidate.description,
                    similarity_score=composite_score,
                    text_similarity=text_sim,
                    signature_match=signature_match,
                    area_path_match=area_path_match,
                    type_match=type_match
                )
                
                scored_candidates.append(scored_candidate)
                
            except Exception as e:
                logger.warning(f"Error scoring candidate {candidate.work_item_id}: {e}")
                continue
        
        # Sort by similarity score (descending)
        scored_candidates.sort(key=lambda x: x.similarity_score, reverse=True)
        
        return scored_candidates
    
    def _calculate_composite_score(
        self,
        semantic_score: float,
        text_similarity: float,
        signature_match: bool,
        area_path_match: bool,
        type_match: bool
    ) -> float:
        """
        Calculate a composite similarity score using weighted factors.
        """
        # Normalize semantic score (assuming it's between 0 and some max value)
        normalized_semantic = min(semantic_score / 10.0, 1.0)  # Assuming max score around 10
        
        # Calculate metadata score
        metadata_score = 0.0
        if area_path_match:
            metadata_score += 0.5
        if type_match:
            metadata_score += 0.5
        
        # Calculate signature score
        signature_score = 1.0 if signature_match else 0.0
        
        # Weighted composite score
        composite = (
            self.weights['semantic'] * normalized_semantic +
            self.weights['text'] * text_similarity +
            self.weights['signature'] * signature_score +
            self.weights['metadata'] * metadata_score
        )
        
        return composite
    
    def _filter_and_convert_duplicates(
        self, 
        candidates: List[DuplicateCandidate]
    ) -> List[DuplicateHit]:
        """
        Filter candidates by threshold and convert to DuplicateHit objects.
        """
        duplicates = []
        
        for candidate in candidates:
            if candidate.similarity_score >= self.similarity_threshold:
                duplicate_hit = DuplicateHit(
                    work_item_id=candidate.work_item_id,
                    title=candidate.title,
                    similarity_score=candidate.similarity_score,
                    match_reasons=self._build_match_reasons(candidate)
                )
                duplicates.append(duplicate_hit)
        
        # Limit to max results
        max_results = self.config.get('DUPLICATE_MAX_RESULTS', 5)
        return duplicates[:max_results]
    
    def _build_match_reasons(self, candidate: DuplicateCandidate) -> List[str]:
        """
        Build a list of reasons why this candidate is considered a duplicate.
        """
        reasons = []
        
        if candidate.text_similarity >= self.text_similarity_threshold:
            reasons.append(f"High text similarity ({candidate.text_similarity:.2%})")
        
        if candidate.signature_match:
            reasons.append("Identical error signature")
        
        if candidate.area_path_match:
            reasons.append("Same area path")
        
        if candidate.type_match:
            reasons.append("Same work item type")
        
        if candidate.similarity_score >= 0.9:
            reasons.append("Very high semantic similarity")
        elif candidate.similarity_score >= 0.8:
            reasons.append("High semantic similarity")
        
        return reasons
    
    def _build_type_filter(self, work_item_type: str) -> Optional[str]:
        """
        Build a filter for similar work item types.
        """
        # Define type groups that should be searched together
        type_groups = {
            'Bug': ['Bug', 'Defect', 'Issue'],
            'Task': ['Task', 'User Story', 'Feature'],
            'User Story': ['User Story', 'Task', 'Feature'],
            'Feature': ['Feature', 'Epic', 'User Story']
        }
        
        similar_types = type_groups.get(work_item_type, [work_item_type])
        
        if len(similar_types) == 1:
            return f"work_item_type eq '{similar_types[0]}'"
        else:
            type_conditions = [f"work_item_type eq '{t}'" for t in similar_types]
            return f"({' or '.join(type_conditions)})"
