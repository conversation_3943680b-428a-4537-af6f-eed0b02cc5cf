{"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "1.0.0.0", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}}, "triggers": {"When_a_HTTP_request_is_received": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"To": {"type": "string"}, "Subject": {"type": "string"}, "Body": {"type": "string"}, "Attachments": {"type": "string"}, "attachmentName": {"type": "string"}, "work_item_id": {"type": "integer"}, "adaptive_card": {"type": "object"}}}}}}, "actions": {"Initialize_work_item_id": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "work_item_id", "type": "integer", "value": "@triggerBody()?['work_item_id']"}]}, "runAfter": {}}, "Check_if_adaptive_card_provided": {"type": "If", "expression": {"and": [{"not": {"equals": ["@triggerBody()?['adaptive_card']", "@null"]}}]}, "actions": {"Post_adaptive_card_async": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "method": "post", "path": "/v1.0/teams/@{encodeURIComponent('your-team-id')}/channels/@{encodeURIComponent('your-channel-id')}/messages", "body": {"body": {"contentType": "html", "content": "@triggerBody()?['adaptive_card']"}}}, "runAfter": {}}, "Store_notification_tracking": {"type": "Http", "inputs": {"method": "POST", "uri": "@{parameters('function_app_url')}/api/store_notification_tracking", "headers": {"Content-Type": "application/json", "x-functions-key": "@parameters('function_app_key')"}, "body": {"work_item_id": "@variables('work_item_id')", "notification_id": "@triggerBody()?['notification_id']", "teams_message_id": "@body('Post_adaptive_card_async')?['id']", "sent_timestamp": "@utcNow()", "status": "sent"}}, "runAfter": {"Post_adaptive_card_async": ["Succeeded"]}}}, "else": {"actions": {"Send_simple_teams_message": {"type": "Http", "inputs": {"method": "POST", "uri": "@parameters('teams_webhook_url')", "headers": {"Content-Type": "application/json"}, "body": {"text": "@triggerBody()?['Body']", "title": "@triggerBody()?['Subject']"}}}}}, "runAfter": {"Initialize_work_item_id": ["Succeeded"]}}, "Response": {"type": "Response", "kind": "Http", "inputs": {"statusCode": 200, "body": {"status": "success", "message": "Teams notification accepted for processing", "work_item_id": "@variables('work_item_id')", "timestamp": "@utcNow()"}}, "runAfter": {"Initialize_work_item_id": ["Succeeded"]}}}}