#!/usr/bin/env python3
"""
Script to fetch all defects from Air4 Channels Testing\\AI Testing - Defect Management area path.
"""

import asyncio
import sys
import os
from datetime import datetime
from typing import List, Dict, Any
import json

# Add the functions directory to the path so we can import modules
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment variables from local.settings.json
def load_local_settings():
    """Load environment variables from local.settings.json"""
    settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded settings from {settings_path}")
    else:
        print(f"❌ Settings file not found: {settings_path}")

# Load settings before importing config
load_local_settings()

from __app__.common.utils.config import get_config
from __app__.common.adapters.ado_client import AdoClient


async def get_defects_from_ai_testing() -> List[Dict[str, Any]]:
    """
    Fetch all defects from the AI Testing - Defect Management area path.
    """
    try:
        # Initialize configuration and ADO client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        ado_client = AdoClient(config)
        
        # First, let's try a simple query to see what area paths exist
        print("🔍 First, let's explore available area paths...")
        area_query = """
        SELECT [System.Id], [System.AreaPath], [System.WorkItemType]
        FROM WorkItems
        WHERE [System.TeamProject] = @project
        ORDER BY [System.Id] DESC
        """

        print("🔍 Executing area path exploration query...")
        area_items = await ado_client.query_work_items(area_query)

        # Get unique area paths
        area_paths = set()
        for item in area_items[:50]:  # Look at first 50 items
            fields = item.get("fields", {})
            area_path = fields.get("System.AreaPath", "")
            if area_path:
                area_paths.add(area_path)

        print(f"📍 Found {len(area_paths)} unique area paths:")
        for path in sorted(area_paths):
            print(f"   - {path}")

        # Now try a more specific query for defects
        # Let's try different variations of the area path
        possible_area_paths = [
            "Air4 Channels Testing\\AI Testing - Defect Management",
            "Air4 Channels Testing\\AI Testing",
            "AI Testing - Defect Management",
            "AI Testing"
        ]

        work_items = []
        for area_path in possible_area_paths:
            print(f"\n🔍 Trying area path: '{area_path}'")

            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
                   [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
                   [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
                   [Microsoft.VSTS.Common.Severity], [Microsoft.VSTS.TCM.ReproSteps],
                   [Microsoft.VSTS.TCM.SystemInfo], [System.CreatedBy], [System.ChangedBy],
                   [Microsoft.VSTS.Common.ResolvedDate], [Microsoft.VSTS.Common.ClosedDate]
            FROM WorkItems
            WHERE [System.TeamProject] = @project
              AND [System.AreaPath] UNDER '{area_path}'
              AND [System.WorkItemType] IN ('Bug', 'Defect')
              AND [System.State] <> 'Removed'
            ORDER BY [System.CreatedDate] DESC
            """

            try:
                items = await ado_client.query_work_items(wiql_query)
                if items:
                    print(f"✅ Found {len(items)} items in area path: '{area_path}'")
                    work_items.extend(items)
                    break
                else:
                    print(f"   No items found in area path: '{area_path}'")
            except Exception as e:
                print(f"   Error with area path '{area_path}': {e}")

        # If no specific area path worked, try a broader search
        if not work_items:
            print(f"\n🔍 Trying broader search for Bug/Defect work items...")
            wiql_query = """
            SELECT [System.Id], [System.Title], [System.Description], [System.WorkItemType],
                   [System.State], [System.AreaPath], [System.AssignedTo], [System.CreatedDate],
                   [System.ChangedDate], [Microsoft.VSTS.Common.Priority], [System.Tags],
                   [Microsoft.VSTS.Common.Severity], [Microsoft.VSTS.TCM.ReproSteps],
                   [Microsoft.VSTS.TCM.SystemInfo], [System.CreatedBy], [System.ChangedBy],
                   [Microsoft.VSTS.Common.ResolvedDate], [Microsoft.VSTS.Common.ClosedDate]
            FROM WorkItems
            WHERE [System.TeamProject] = @project
              AND [System.WorkItemType] IN ('Bug', 'Defect')
              AND [System.State] <> 'Removed'
            ORDER BY [System.CreatedDate] DESC
            """

            work_items = await ado_client.query_work_items(wiql_query)

        print(f"✅ Found {len(work_items)} total Bug/Defect work items")

        return work_items
        
    except Exception as e:
        print(f"❌ Error fetching defects: {e}")
        raise


def format_work_item_summary(work_item: Dict[str, Any]) -> str:
    """Format a work item for display."""
    fields = work_item.get("fields", {})
    
    work_item_id = work_item.get("id", "Unknown")
    title = fields.get("System.Title", "No Title")
    work_item_type = fields.get("System.WorkItemType", "Unknown")
    state = fields.get("System.State", "Unknown")
    priority = fields.get("Microsoft.VSTS.Common.Priority", "Unknown")
    severity = fields.get("Microsoft.VSTS.Common.Severity", "Unknown")
    assigned_to = fields.get("System.AssignedTo", {}).get("displayName", "Unassigned")
    created_date = fields.get("System.CreatedDate", "Unknown")
    area_path = fields.get("System.AreaPath", "Unknown")
    
    # Parse and format the created date
    try:
        if created_date != "Unknown":
            created_dt = datetime.fromisoformat(created_date.replace('Z', '+00:00'))
            created_formatted = created_dt.strftime("%Y-%m-%d %H:%M")
        else:
            created_formatted = "Unknown"
    except:
        created_formatted = created_date
    
    return f"""
ID: {work_item_id}
Title: {title}
Type: {work_item_type}
State: {state}
Priority: {priority}
Severity: {severity}
Assigned To: {assigned_to}
Created: {created_formatted}
Area Path: {area_path}
{'='*80}"""


def export_to_json(work_items: List[Dict[str, Any]], filename: str = None) -> str:
    """Export work items to JSON file."""
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"ai_testing_defects_{timestamp}.json"
    
    # Create a simplified export format
    export_data = []
    for item in work_items:
        fields = item.get("fields", {})
        export_item = {
            "id": item.get("id"),
            "title": fields.get("System.Title"),
            "type": fields.get("System.WorkItemType"),
            "state": fields.get("System.State"),
            "priority": fields.get("Microsoft.VSTS.Common.Priority"),
            "severity": fields.get("Microsoft.VSTS.Common.Severity"),
            "assigned_to": fields.get("System.AssignedTo", {}).get("displayName"),
            "created_date": fields.get("System.CreatedDate"),
            "changed_date": fields.get("System.ChangedDate"),
            "area_path": fields.get("System.AreaPath"),
            "description": fields.get("System.Description"),
            "repro_steps": fields.get("Microsoft.VSTS.TCM.ReproSteps"),
            "system_info": fields.get("Microsoft.VSTS.TCM.SystemInfo"),
            "tags": fields.get("System.Tags"),
            "created_by": fields.get("System.CreatedBy", {}).get("displayName"),
            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
        }
        export_data.append(export_item)
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    return filename


async def main():
    """Main function to fetch and display defects."""
    try:
        print("🚀 Fetching defects from Air4 Channels Testing\\AI Testing - Defect Management")
        print("=" * 80)
        
        # Fetch defects
        defects = await get_defects_from_ai_testing()
        
        if not defects:
            print("📭 No defects found in the AI Testing - Defect Management area.")
            return
        
        # Display summary statistics
        print(f"\n📊 SUMMARY STATISTICS")
        print("=" * 80)
        
        # Group by state
        states = {}
        priorities = {}
        severities = {}
        assignees = {}
        
        for defect in defects:
            fields = defect.get("fields", {})
            
            # Count by state
            state = fields.get("System.State", "Unknown")
            states[state] = states.get(state, 0) + 1
            
            # Count by priority
            priority = fields.get("Microsoft.VSTS.Common.Priority", "Unknown")
            priorities[priority] = priorities.get(priority, 0) + 1
            
            # Count by severity
            severity = fields.get("Microsoft.VSTS.Common.Severity", "Unknown")
            severities[severity] = severities.get(severity, 0) + 1
            
            # Count by assignee
            assignee = fields.get("System.AssignedTo", {}).get("displayName", "Unassigned")
            assignees[assignee] = assignees.get(assignee, 0) + 1
        
        print(f"Total Defects: {len(defects)}")
        print(f"\nBy State: {dict(sorted(states.items()))}")
        print(f"By Priority: {dict(sorted(priorities.items()))}")
        print(f"By Severity: {dict(sorted(severities.items()))}")
        print(f"By Assignee: {dict(sorted(assignees.items(), key=lambda x: x[1], reverse=True))}")
        
        # Display first 10 defects in detail
        print(f"\n📋 DEFECT DETAILS (First 10 of {len(defects)})")
        print("=" * 80)
        
        for i, defect in enumerate(defects[:10]):
            print(format_work_item_summary(defect))
            if i < 9 and i < len(defects) - 1:
                print()
        
        if len(defects) > 10:
            print(f"\n... and {len(defects) - 10} more defects")
        
        # Export to JSON
        print(f"\n💾 EXPORTING DATA")
        print("=" * 80)
        filename = export_to_json(defects)
        print(f"✅ Exported {len(defects)} defects to: {filename}")
        
        # Show Azure DevOps URLs for quick access
        print(f"\n🔗 QUICK LINKS")
        print("=" * 80)
        print(f"Azure DevOps Project: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing")
        print(f"Work Items Query: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
