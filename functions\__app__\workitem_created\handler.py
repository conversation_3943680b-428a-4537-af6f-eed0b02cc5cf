"""
Azure Function: WorkItem Created Handler
HTTP trigger for Azure DevOps Service Hook when work items are created/updated.
"""

import json
import logging
from typing import Dict, Any, Optional, List
import azure.functions as func
from azure.functions import HttpRequest, HttpResponse

from ..common.models.schemas import WorkItem, TriageResult
from ..common.adapters.ado_client import AdoClient
from ..common.adapters.search_client import SearchClient
from ..common.adapters.teams_client import TeamsClient
from ..common.ai.duplicate import DuplicateDetector
from ..common.services.triage import TriageService
from ..common.services.history import HistoryService
from ..common.vectorstore.azure_search import AzureSearchClient
from ..common.ai.priority import PriorityEngine
from ..common.utils.config import get_config
from ..common.utils.logging import setup_logging, log_structured

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)

# Initialize clients (will be lazy-loaded)
_ado_client: Optional[AdoClient] = None
_search_client: Optional[SearchClient] = None
_teams_client: Optional[TeamsClient] = None
_duplicate_detector: Optional[DuplicateDetector] = None
_triage_service: Optional[TriageService] = None
_history_service: Optional[HistoryService] = None
_vector_store: Optional[AzureSearchClient] = None
_priority_engine: Optional[PriorityEngine] = None


def get_clients():
    """Lazy initialization of clients."""
    global _ado_client, _search_client, _teams_client
    global _duplicate_detector, _triage_service, _history_service, _vector_store, _priority_engine

    if _ado_client is None:
        config = get_config()
        _ado_client = AdoClient(config)
        _search_client = SearchClient(config)
        _teams_client = TeamsClient(config)
        _duplicate_detector = DuplicateDetector(_search_client, config)
        _vector_store = AzureSearchClient(config)
        _history_service = HistoryService(_vector_store, _ado_client, config)
        _triage_service = TriageService(config)
        _priority_engine = PriorityEngine(config)

    return {
        'ado': _ado_client,
        'search': _search_client,
        'teams': _teams_client,
        'duplicate': _duplicate_detector,
        'triage': _triage_service,
        'history': _history_service,
        'priority': _priority_engine
    }


def extract_work_item_from_webhook(webhook_data: Dict[str, Any]) -> Optional[WorkItem]:
    """
    Extract work item data from ADO webhook payload and convert to WorkItem model.
    """
    try:
        # ADO webhook structure: resource.fields contains the work item data
        resource = webhook_data.get('resource', {})
        fields = resource.get('fields', {})
        
        if not fields:
            logger.warning("No fields found in webhook payload")
            return None
        
        # Extract and map fields to WorkItem model
        work_item_data = {
            'id': resource.get('id') or resource.get('workItemId'),
            'title': fields.get('System.Title', ''),
            'description': fields.get('System.Description', ''),
            'work_item_type': fields.get('System.WorkItemType', ''),
            'state': fields.get('System.State', ''),
            'area_path': fields.get('System.AreaPath', ''),
            'iteration_path': fields.get('System.IterationPath', ''),
            'assigned_to': _extract_user_display_name(fields.get('System.AssignedTo')),
            'created_by': _extract_user_display_name(fields.get('System.CreatedBy')),
            'created_date': fields.get('System.CreatedDate', ''),
            'changed_date': fields.get('System.ChangedDate', ''),
            'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
            'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
            'tags': fields.get('System.Tags', ''),
            'repro_steps': fields.get('Microsoft.VSTS.TCM.ReproSteps', ''),
            'system_info': fields.get('Microsoft.VSTS.TCM.SystemInfo', ''),
        }
        
        # Create WorkItem instance
        work_item = WorkItem(**work_item_data)
        
        log_structured(
            logger,
            "info",
            "Extracted work item from webhook",
            extra={
                "work_item_id": work_item.id,
                "work_item_type": work_item.work_item_type,
                "title": work_item.title[:100] + "..." if len(work_item.title) > 100 else work_item.title
            }
        )
        
        return work_item
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to extract work item from webhook",
            extra={
                "error": str(e),
                "webhook_keys": list(webhook_data.keys()) if webhook_data else []
            }
        )
        return None


def _extract_user_display_name(user_field: Any) -> str:
    """Extract display name from user field (can be dict or string)."""
    if isinstance(user_field, dict):
        return user_field.get('displayName', '')
    elif isinstance(user_field, str):
        return user_field
    else:
        return ''


async def process_workitem_webhook(req: HttpRequest) -> HttpResponse:
    """
    Main function to process work items - PRODUCTION SAFETY: Only processes Bug 748404.

    ⚠️ PRODUCTION SAFETY RESTRICTION:
    During development and testing, this function ONLY processes Bug 748404.
    All other production work items are completely ignored to prevent accidental modifications.

    This function:
    1. Checks if work item is Bug 748404 - if not, returns immediately
    2. Runs AI triage for Bug 748404 only (duplicate detection, assignment, priority)
    3. Sends Teams notifications with recommendations (does NOT update work items)
    4. Indexes work item for future searches

    NOTE: Work items are NOT automatically updated. Updates only happen when users
    respond to Teams notifications via the Teams response flow.

    Query parameters:
    - work_item_id: Specific work item ID to process (must be 748404)
    - dry_run: If true, only shows what would be processed without making changes
    """
    try:
        # Get query parameters
        work_item_id = req.params.get('work_item_id')
        dry_run = req.params.get('dry_run', 'false').lower() == 'true'
        hours_back = 24  # Default value for response messages

        # Production Safety Guardrails
        config = get_config()

        if work_item_id:
            work_item_id = int(work_item_id)

            # Check if work item is allowed by safety settings
            if not config.is_work_item_allowed(work_item_id):
                log_structured(
                    logger,
                    "warning",
                    f"PRODUCTION SAFETY: Work item {work_item_id} not allowed by SAFETY_ONLY_WORKITEM_ID setting",
                    extra={"work_item_id": work_item_id, "allowed_id": config.SAFETY_ONLY_WORKITEM_ID}
                )
                return func.HttpResponse(
                    json.dumps({
                        "status": "ignored",
                        "message": f"PRODUCTION SAFETY: Work item {work_item_id} not allowed by safety settings.",
                        "work_item_id": work_item_id,
                        "safety_setting": config.SAFETY_ONLY_WORKITEM_ID
                    }),
                    status_code=200,
                    mimetype="application/json"
                )
        else:
            # If no work_item_id specified and safety setting exists, use it
            if config.SAFETY_ONLY_WORKITEM_ID:
                try:
                    work_item_id = int(config.SAFETY_ONLY_WORKITEM_ID)
                    log_structured(
                        logger,
                        "info",
                        f"Using safety work item ID from configuration: {work_item_id}",
                        extra={"work_item_id": work_item_id}
                    )
                except (ValueError, TypeError):
                    return func.HttpResponse(
                        json.dumps({
                            "status": "error",
                            "message": "No work_item_id provided and SAFETY_ONLY_WORKITEM_ID is invalid"
                        }),
                        status_code=400,
                        mimetype="application/json"
                    )
            else:
                return func.HttpResponse(
                    json.dumps({
                        "status": "error",
                        "message": "work_item_id parameter is required"
                    }),
                    status_code=400,
                    mimetype="application/json"
                )

        log_structured(
            logger,
            "info",
            f"Processing work item {work_item_id} with safety guardrails",
            extra={
                "work_item_id": work_item_id,
                "dry_run": dry_run,
                "read_only": config.READ_ONLY,
                "method": req.method
            }
        )

        # Initialize clients
        clients = get_clients()

        # Get the specified work item
        try:
            work_item_data = await clients['ado'].get_work_item(work_item_id)
            if not work_item_data:
                return func.HttpResponse(
                    json.dumps({
                        "status": "error",
                        "message": f"Work item {work_item_id} not found in Azure DevOps",
                        "work_item_id": work_item_id
                    }),
                    status_code=404,
                    mimetype="application/json"
                )

            # Keep as raw ADO data for processing loop
            bug_work_items = [work_item_data]

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error fetching Bug 748404: {e}",
                extra={"work_item_id": 748404, "error": str(e)}
            )
            return func.HttpResponse(
                json.dumps({
                    "status": "error",
                    "message": f"Error fetching Bug 748404: {e}",
                    "work_item_id": 748404
                }),
                status_code=500,
                mimetype="application/json"
            )

        if not bug_work_items:
            response_data = {
                "status": "success",
                "message": f"No Bug/defect work items found in last {hours_back} hours",
                "work_items_found": 0,
                "work_items_processed": 0,
                "dry_run": dry_run,
                "hours_back": hours_back
            }

            log_structured(
                logger,
                "info",
                f"No Bug work items found in last {hours_back} hours"
            )

            return func.HttpResponse(
                json.dumps(response_data, indent=2),
                status_code=200,
                mimetype="application/json"
            )

        log_structured(
            logger,
            "info",
            f"Found {len(bug_work_items)} Bug/defect work items to process"
        )

        # Process each Bug work item
        processed_items = []
        total_processed = 0

        for work_item_data in bug_work_items:
            try:
                # Convert to WorkItem object
                work_item = convert_ado_to_work_item(work_item_data)

                if dry_run:
                    # Simulate processing
                    log_structured(
                        logger,
                        "info",
                        f"DRY RUN: Would process Bug {work_item.id}",
                        extra={
                            "work_item_id": work_item.id,
                            "title": work_item.title[:50]
                        }
                    )

                    processed_items.append({
                        "work_item_id": work_item.id,
                        "title": work_item.title[:100],
                        "work_item_type": work_item.work_item_type,
                        "state": work_item.state,
                        "dry_run": True,
                        "would_process": True
                    })
                    total_processed += 1
                    continue

                # Run AI triage pipeline
                triage_result = await run_triage_pipeline(work_item, clients)

                # Send Teams notification (do NOT update work item automatically)
                # Work items should only be updated when users respond via Teams
                await send_teams_notification(work_item, triage_result, clients['teams'])

                # Index work item for future similarity searches
                await index_work_item(work_item, clients['search'])

                processed_items.append({
                    "work_item_id": work_item.id,
                    "title": work_item.title[:100],
                    "work_item_type": work_item.work_item_type,
                    "state": work_item.state,
                    "assigned_to": triage_result.assigned_to if triage_result else None,
                    "priority": triage_result.priority if triage_result else None,
                    "duplicates_found": len(triage_result.duplicates) if triage_result and triage_result.duplicates else 0,
                    "confidence_score": triage_result.confidence_score if triage_result else None
                })

                total_processed += 1

                log_structured(
                    logger,
                    "info",
                    f"Successfully processed Bug {work_item.id}",
                    extra={
                        "work_item_id": work_item.id,
                        "assigned_to": triage_result.assigned_to if triage_result else None,
                        "priority": triage_result.priority if triage_result else None
                    }
                )

            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error processing Bug work item {work_item_data.get('id', 'unknown')}: {e}",
                    extra={"work_item_data": work_item_data},
                    exc_info=True
                )

                processed_items.append({
                    "work_item_id": work_item_data.get('id', 'unknown'),
                    "title": work_item_data.get('fields', {}).get('System.Title', 'Unknown')[:100],
                    "error": str(e),
                    "processed": False
                })
                continue

        # Return success response
        response_data = {
            "status": "success",
            "message": f"Processed {total_processed} Bug/defect work items from last {hours_back} hours",
            "work_items_found": len(bug_work_items),
            "work_items_processed": total_processed,
            "dry_run": dry_run,
            "hours_back": hours_back,
            "processed_items": processed_items
        }

        log_structured(
            logger,
            "info",
            f"Bug processing completed",
            extra={
                "work_items_found": len(bug_work_items),
                "work_items_processed": total_processed,
                "dry_run": dry_run
            }
        )

        return func.HttpResponse(
            json.dumps(response_data, indent=2),
            status_code=200,
            mimetype="application/json"
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Unexpected error processing webhook",
            extra={
                "error": str(e),
                "error_type": type(e).__name__
            }
        )
        
        return func.HttpResponse(
            json.dumps({
                "error": "Internal server error",
                "message": str(e)
            }),
            status_code=500,
            mimetype="application/json"
        )


async def get_recent_bug_work_items(ado_client: AdoClient, hours_back: int = 24) -> List[Dict[str, Any]]:
    """Get Bug/defect work items created in the last N hours."""
    try:
        from datetime import datetime, timedelta

        # Calculate the date threshold (date only, no time for WIQL)
        threshold_date = datetime.utcnow() - timedelta(hours=hours_back)
        threshold_str = threshold_date.strftime('%Y-%m-%d')

        # WIQL query to get Bug work items created in the specified time range
        # Using minimal fields that we know work
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.WorkItemType], [System.CreatedDate]
        FROM WorkItems
        WHERE [System.WorkItemType] = 'Bug'
        AND [System.CreatedDate] >= '{threshold_str}'
        ORDER BY [System.CreatedDate] DESC
        """

        log_structured(
            logger,
            "info",
            f"Querying for Bug work items created since {threshold_str}",
            extra={"hours_back": hours_back, "threshold": threshold_str}
        )

        # Execute the WIQL query
        work_items = await ado_client.query_work_items(wiql_query)

        log_structured(
            logger,
            "info",
            f"Found {len(work_items)} Bug work items in last {hours_back} hours"
        )

        return work_items

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error querying Bug work items: {e}",
            exc_info=True
        )
        return []


def convert_ado_to_work_item(work_item_data: Dict[str, Any]) -> WorkItem:
    """Convert Azure DevOps work item data to WorkItem object."""
    try:
        fields = work_item_data.get("fields", {})

        # Extract basic fields
        work_item_id = work_item_data.get("id")
        title = fields.get("System.Title", "")
        work_item_type = fields.get("System.WorkItemType", "Bug")
        state = fields.get("System.State", "New")

        # Extract description and repro steps
        description = fields.get("System.Description", "")
        repro_steps = fields.get("System.ReproSteps", "")

        # Combine description and repro steps for content
        content_parts = []
        if description:
            content_parts.append(f"Description: {description}")
        if repro_steps:
            content_parts.append(f"Reproduction Steps: {repro_steps}")

        content = "\n\n".join(content_parts) if content_parts else title

        # Extract other fields
        assigned_to = fields.get("System.AssignedTo", {}).get("displayName", "") if isinstance(fields.get("System.AssignedTo"), dict) else fields.get("System.AssignedTo", "")
        priority = fields.get("Microsoft.VSTS.Common.Priority") or fields.get("System.Priority", 2)
        severity = fields.get("Microsoft.VSTS.Common.Severity", "3 - Medium")
        area_path = fields.get("System.AreaPath", "")
        iteration_path = fields.get("System.IterationPath", "")

        # Extract dates
        created_date = fields.get("System.CreatedDate", "")
        changed_date = fields.get("System.ChangedDate", "")

        # Create WorkItem object
        work_item = WorkItem(
            id=work_item_id,
            title=title,
            description=content,  # Use description field instead of content
            work_item_type=work_item_type,
            state=state,
            assigned_to=assigned_to,
            priority=int(priority) if isinstance(priority, (int, str)) and str(priority).isdigit() else 2,
            severity=severity,
            area_path=area_path,
            iteration_path=iteration_path,
            created_date=created_date,
            changed_date=changed_date,
            tags=""  # Tags would need to be extracted separately if needed
        )

        log_structured(
            logger,
            "debug",
            f"Converted ADO work item {work_item_id} to WorkItem object",
            extra={
                "work_item_id": work_item_id,
                "title": title[:50],
                "type": work_item_type,
                "state": state
            }
        )

        return work_item

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error converting ADO work item to WorkItem object: {e}",
            extra={"work_item_data": work_item_data},
            exc_info=True
        )
        raise


async def run_triage_pipeline(work_item: WorkItem, clients: Dict[str, Any]) -> Optional[TriageResult]:
    """
    Run the complete AI triage pipeline for a work item using REAL data only.
    NO HALLUCINATION - Uses only real historical data from Azure DevOps.
    """
    try:
        log_structured(
            logger,
            "info",
            "Starting triage pipeline with REAL data only",
            extra={
                "work_item_id": work_item.id,
                "work_item_type": work_item.work_item_type,
                "data_source": "real_ado_history_only"
            }
        )

        # Step 1: Duplicate Detection
        duplicates = await clients['duplicate'].find_duplicates(work_item)

        # Step 2: Priority Analysis
        priority = await clients['priority'].calculate_priority(work_item)

        # Step 3: Find similar historical items
        work_item_dict = {
            'id': work_item.id,
            'fields': {
                'System.Title': work_item.title,
                'System.Description': work_item.description,
                'System.WorkItemType': work_item.work_item_type
            }
        }

        # Generate placeholder embedding vector (in real implementation, this would use proper embedding service)
        query_vector = [0.0] * 1536  # Placeholder vector

        similar_items = await clients['history'].find_similar_items(
            work_item=work_item_dict,
            query_vector=query_vector,
            k=10,
            include_resolved=True
        )

        # Convert SearchHit objects to dict format for triage service
        similar_items_dict = []
        for item in similar_items:
            similar_items_dict.append({
                'id': item.id,
                'score': item.score,
                'title': item.metadata.get('title', ''),
                'assigned_to': item.metadata.get('assigned_to', ''),
                'work_item_type': item.metadata.get('work_item_type', ''),
                'state': item.metadata.get('state', ''),
                'area_path': item.metadata.get('area_path', ''),
                'iteration_path': item.metadata.get('iteration_path', ''),
                'created_date': item.metadata.get('created_date', ''),
                'priority': item.metadata.get('priority', 3)
            })

        # Step 4: Get triage recommendations using REAL historical data
        recommendations = await clients['triage'].get_triage_recommendations(
            work_item_id=work_item.id,
            title=work_item.title,
            description=work_item.description,
            work_item_type=work_item.work_item_type,
            similar_items=similar_items_dict
        )

        # Extract assignee suggestions
        suggested_assignees = recommendations.get('suggested_assignees', [])

        # Format for TriageResult
        assigned_to = ''
        reasoning_parts = []
        suggestions = []

        if suggested_assignees:
            # Use top suggestion as assigned_to
            top_assignee = suggested_assignees[0]
            assigned_to = top_assignee['email']
            reasoning_parts.append(f"Top suggestion: {top_assignee['name']} (Score: {top_assignee['score']:.3f})")

            # Format all suggestions
            for assignee in suggested_assignees:
                suggestions.append({
                    "assignee": assignee['email'],
                    "confidence": assignee['score'],
                    "reasoning": assignee['rationale']
                })

        # Add priority reasoning
        priority_rec = recommendations.get('priority_recommendation', {})
        if priority_rec.get('rationale'):
            reasoning_parts.append(f"Priority: {priority_rec['rationale']}")

        reasoning = '; '.join(reasoning_parts)

        log_structured(
            logger,
            "info",
            "Triage recommendations generated with REAL data",
            extra={
                "work_item_id": work_item.id,
                "similar_items_count": len(similar_items),
                "assignee_suggestions": len(suggested_assignees),
                "data_source": recommendations.get('data_source', 'unknown')
            }
        )

        triage_result = TriageResult(
            work_item_id=work_item.id,
            assigned_to=assigned_to,
            priority=priority,
            duplicates=[dup.dict() for dup in duplicates] if duplicates else [],
            confidence_score=suggested_assignees[0]['score'] if suggested_assignees else 0.0,
            reasoning=reasoning,
            suggestions=suggestions
        )

        log_structured(
            logger,
            "info",
            "Completed triage pipeline with REAL data",
            extra={
                "work_item_id": work_item.id,
                "duplicates_found": len(duplicates) if duplicates else 0,
                "assigned_to": triage_result.assigned_to,
                "priority": triage_result.priority,
                "data_source": "real_ado_history"
            }
        )

        return triage_result

    except Exception as e:
        log_structured(
            logger,
            "error",
            "Error in triage pipeline",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )
        return None


async def update_work_item_with_triage(work_item: WorkItem, triage_result: TriageResult, ado_client: AdoClient):
    """Update the work item in ADO with triage results."""
    try:
        if not triage_result:
            return
        
        updates = {}
        
        # Update assignment if we have a confident assignment
        if triage_result.assigned_to and triage_result.confidence_score > 0.7:
            updates['System.AssignedTo'] = triage_result.assigned_to
        
        # Update priority if it changed
        if triage_result.priority != work_item.priority:
            updates['Microsoft.VSTS.Common.Priority'] = triage_result.priority
        
        # Add triage reasoning as a comment
        if triage_result.reasoning:
            comment = f"🤖 AI Triage: {triage_result.reasoning}"
            if triage_result.duplicates:
                comment += f"\n\n🔍 Potential duplicates found: {len(triage_result.duplicates)}"
            
            updates['System.History'] = comment
        
        if updates:
            await ado_client.update_work_item(work_item.id, updates)
            log_structured(
                logger,
                "info",
                "Updated work item with triage results",
                extra={
                    "work_item_id": work_item.id,
                    "updates": list(updates.keys())
                }
            )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to update work item",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )


async def send_teams_notification(work_item: WorkItem, triage_result: TriageResult, teams_client: TeamsClient):
    """Send notification to Teams channel."""
    try:
        if not triage_result:
            return
        
        await teams_client.send_triage_notification(work_item, triage_result)
        
        log_structured(
            logger,
            "info",
            "Sent Teams notification",
            extra={"work_item_id": work_item.id}
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to send Teams notification",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )


async def index_work_item(work_item: WorkItem, search_client: SearchClient):
    """Index the work item for future similarity searches."""
    try:
        await search_client.index_work_item(work_item)
        
        log_structured(
            logger,
            "info",
            "Indexed work item for search",
            extra={"work_item_id": work_item.id}
        )
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            "Failed to index work item",
            extra={
                "work_item_id": work_item.id,
                "error": str(e)
            }
        )
