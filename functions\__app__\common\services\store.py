"""
Idempotency and State Management Store
Supports Redis, Azure Cosmos, and SQLite backends for deduplication and state tracking.
"""

import json
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Protocol
from abc import abstractmethod

logger = logging.getLogger(__name__)


class StateStore(Protocol):
    """Protocol for state storage backends."""
    
    @abstractmethod
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        pass
    
    @abstractmethod
    async def set(self, key: str, value: str, ttl_seconds: Optional[int] = None) -> None:
        """Set value with optional TTL."""
        pass
    
    @abstractmethod
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        pass
    
    @abstractmethod
    async def delete(self, key: str) -> None:
        """Delete key."""
        pass


class SQLiteStore:
    """SQLite-based store for development and testing."""
    
    def __init__(self, db_path: str = ":memory:"):
        self.db_path = db_path
        self._init_db()
    
    def _init_db(self):
        """Initialize SQLite database."""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute("""
                CREATE TABLE IF NOT EXISTS kv_store (
                    key TEXT PRIMARY KEY,
                    value TEXT NOT NULL,
                    expires_at TIMESTAMP,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            conn.execute("""
                CREATE INDEX IF NOT EXISTS idx_expires_at ON kv_store(expires_at)
            """)
            conn.commit()
        finally:
            conn.close()
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        conn = sqlite3.connect(self.db_path)
        try:
            cursor = conn.execute(
                "SELECT value FROM kv_store WHERE key = ? AND (expires_at IS NULL OR expires_at > ?)",
                (key, datetime.utcnow())
            )
            row = cursor.fetchone()
            return row[0] if row else None
        finally:
            conn.close()
    
    async def set(self, key: str, value: str, ttl_seconds: Optional[int] = None) -> None:
        """Set value with optional TTL."""
        expires_at = None
        if ttl_seconds:
            expires_at = datetime.utcnow() + timedelta(seconds=ttl_seconds)
        
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute(
                "INSERT OR REPLACE INTO kv_store (key, value, expires_at) VALUES (?, ?, ?)",
                (key, value, expires_at)
            )
            conn.commit()
        finally:
            conn.close()
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        value = await self.get(key)
        return value is not None
    
    async def delete(self, key: str) -> None:
        """Delete key."""
        conn = sqlite3.connect(self.db_path)
        try:
            conn.execute("DELETE FROM kv_store WHERE key = ?", (key,))
            conn.commit()
        finally:
            conn.close()


class RedisStore:
    """Redis-based store for production."""
    
    def __init__(self, connection_string: str):
        self.connection_string = connection_string
        self._redis = None
    
    async def _get_redis(self):
        """Get Redis connection (lazy initialization)."""
        if self._redis is None:
            try:
                import aioredis
                self._redis = aioredis.from_url(self.connection_string)
            except ImportError:
                raise ImportError("aioredis package required for Redis store")
        return self._redis
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        redis = await self._get_redis()
        value = await redis.get(key)
        return value.decode('utf-8') if value else None
    
    async def set(self, key: str, value: str, ttl_seconds: Optional[int] = None) -> None:
        """Set value with optional TTL."""
        redis = await self._get_redis()
        await redis.set(key, value, ex=ttl_seconds)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        redis = await self._get_redis()
        return await redis.exists(key) > 0
    
    async def delete(self, key: str) -> None:
        """Delete key."""
        redis = await self._get_redis()
        await redis.delete(key)


class CosmosStore:
    """Azure Cosmos DB-based store for production."""
    
    def __init__(self, connection_string: str, database_name: str = "autodefecttriage", container_name: str = "state"):
        self.connection_string = connection_string
        self.database_name = database_name
        self.container_name = container_name
        self._client = None
        self._container = None
    
    async def _get_container(self):
        """Get Cosmos container (lazy initialization)."""
        if self._container is None:
            try:
                from azure.cosmos.aio import CosmosClient
                self._client = CosmosClient.from_connection_string(self.connection_string)
                database = self._client.get_database_client(self.database_name)
                self._container = database.get_container_client(self.container_name)
            except ImportError:
                raise ImportError("azure-cosmos package required for Cosmos store")
        return self._container
    
    async def get(self, key: str) -> Optional[str]:
        """Get value by key."""
        container = await self._get_container()
        try:
            item = await container.read_item(item=key, partition_key=key)
            # Check expiration
            if item.get('expires_at'):
                expires_at = datetime.fromisoformat(item['expires_at'])
                if datetime.utcnow() > expires_at:
                    await self.delete(key)
                    return None
            return item.get('value')
        except Exception:
            return None
    
    async def set(self, key: str, value: str, ttl_seconds: Optional[int] = None) -> None:
        """Set value with optional TTL."""
        container = await self._get_container()
        
        item = {
            'id': key,
            'key': key,
            'value': value,
            'created_at': datetime.utcnow().isoformat()
        }
        
        if ttl_seconds:
            item['expires_at'] = (datetime.utcnow() + timedelta(seconds=ttl_seconds)).isoformat()
            item['ttl'] = ttl_seconds  # Cosmos DB TTL
        
        await container.upsert_item(item)
    
    async def exists(self, key: str) -> bool:
        """Check if key exists."""
        value = await self.get(key)
        return value is not None
    
    async def delete(self, key: str) -> None:
        """Delete key."""
        container = await self._get_container()
        try:
            await container.delete_item(item=key, partition_key=key)
        except Exception:
            pass  # Item doesn't exist


class IdempotencyStore:
    """High-level idempotency and deduplication store."""
    
    def __init__(self, config):
        self.config = config
        self._store = self._create_store()
    
    def _create_store(self) -> StateStore:
        """Create appropriate store based on configuration."""
        
        # Check for Redis configuration
        redis_conn = getattr(self.config, 'REDIS_CONNECTION_STRING', None)
        if redis_conn:
            logger.info("Using Redis store for idempotency")
            return RedisStore(redis_conn)
        
        # Check for Cosmos configuration
        cosmos_conn = getattr(self.config, 'COSMOS_CONNECTION_STRING', None)
        if cosmos_conn:
            logger.info("Using Cosmos DB store for idempotency")
            return CosmosStore(cosmos_conn)
        
        # Default to SQLite for development
        logger.info("Using SQLite store for idempotency (development mode)")
        return SQLiteStore()
    
    async def is_processed(self, idempotency_key: str) -> bool:
        """Check if an event has already been processed."""
        key = f"processed:{idempotency_key}"
        return await self._store.exists(key)
    
    async def mark_processing(self, idempotency_key: str) -> None:
        """Mark an event as being processed."""
        key = f"processing:{idempotency_key}"
        value = json.dumps({
            "status": "processing",
            "started_at": datetime.utcnow().isoformat()
        })
        # 10 minute TTL for processing state
        await self._store.set(key, value, ttl_seconds=600)
    
    async def mark_completed(self, idempotency_key: str, result: Dict[str, Any]) -> None:
        """Mark an event as completed."""
        processing_key = f"processing:{idempotency_key}"
        completed_key = f"processed:{idempotency_key}"
        
        value = json.dumps({
            "status": "completed",
            "completed_at": datetime.utcnow().isoformat(),
            "result": result
        })
        
        # Remove processing state and mark as completed (24 hour TTL)
        await self._store.delete(processing_key)
        await self._store.set(completed_key, value, ttl_seconds=86400)
    
    async def mark_failed(self, idempotency_key: str, error: str) -> None:
        """Mark an event as failed."""
        processing_key = f"processing:{idempotency_key}"
        failed_key = f"failed:{idempotency_key}"
        
        value = json.dumps({
            "status": "failed",
            "failed_at": datetime.utcnow().isoformat(),
            "error": error
        })
        
        # Remove processing state and mark as failed (1 hour TTL for retry)
        await self._store.delete(processing_key)
        await self._store.set(failed_key, value, ttl_seconds=3600)
    
    async def is_recent_notification(self, dedup_key: str, minutes: int = 60) -> bool:
        """Check if a notification was sent recently."""
        key = f"notification:{dedup_key}"
        return await self._store.exists(key)
    
    async def mark_notification_sent(self, dedup_key: str, ttl_minutes: int = 60) -> None:
        """Mark a notification as sent."""
        key = f"notification:{dedup_key}"
        value = json.dumps({
            "sent_at": datetime.utcnow().isoformat()
        })
        await self._store.set(key, value, ttl_seconds=ttl_minutes * 60)
    
    async def get_processing_status(self, idempotency_key: str) -> Optional[Dict[str, Any]]:
        """Get the processing status of an event."""
        
        # Check if completed
        completed_key = f"processed:{idempotency_key}"
        completed_value = await self._store.get(completed_key)
        if completed_value:
            return json.loads(completed_value)
        
        # Check if processing
        processing_key = f"processing:{idempotency_key}"
        processing_value = await self._store.get(processing_key)
        if processing_value:
            return json.loads(processing_value)
        
        # Check if failed
        failed_key = f"failed:{idempotency_key}"
        failed_value = await self._store.get(failed_key)
        if failed_value:
            return json.loads(failed_value)
        
        return None
