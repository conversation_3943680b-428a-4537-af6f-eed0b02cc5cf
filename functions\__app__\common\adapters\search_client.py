"""
Azure AI Search Client
Handles indexing, searching, and vector operations for work items.
"""

import json
import logging
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
import numpy as np

from azure.search.documents import SearchClient as AzureSearchClient
from azure.search.documents.indexes import SearchIndexClient
from azure.search.documents.models import VectorizedQuery
from azure.core.credentials import AzureKeyCredential

from ..models.schemas import WorkItem, SearchResult
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class SearchClient:
    """Client for Azure AI Search operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.service_name = config.AZURE_SEARCH_SERVICE_NAME
        self.admin_key = config.AZURE_SEARCH_ADMIN_KEY
        self.index_name = getattr(config, 'SEARCH_INDEX_NAME', 'workitems')
        
        # Initialize Azure Search clients
        endpoint = f"https://{self.service_name}.search.windows.net"
        credential = AzureKeyCredential(self.admin_key)
        
        self.search_client = AzureSearchClient(
            endpoint=endpoint,
            index_name=self.index_name,
            credential=credential
        )
        
        self.index_client = SearchIndexClient(
            endpoint=endpoint,
            credential=credential
        )
    
    async def ensure_index_exists(self) -> None:
        """
        Ensure the search index exists with the correct schema.
        """
        try:
            from azure.search.documents.indexes.models import (
                SearchIndex,
                SearchField,
                SearchFieldDataType,
                SimpleField,
                SearchableField,
                VectorSearch,
                HnswAlgorithmConfiguration,
                VectorSearchProfile,
                SemanticConfiguration,
                SemanticSearch,
                SemanticField,
                SemanticPrioritizedFields
            )
            
            # Define the comprehensive index schema with all ADO fields
            fields = [
                # Core identification fields
                SimpleField(name="id", type=SearchFieldDataType.String, key=True),
                SimpleField(name="work_item_id", type=SearchFieldDataType.Int32, filterable=True, sortable=True),
                SimpleField(name="revision", type=SearchFieldDataType.Int32, filterable=True, sortable=True),

                # Core content fields (searchable)
                SearchableField(name="title", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="description", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="repro_steps", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="system_info", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="acceptance_criteria", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),
                SearchableField(name="history", type=SearchFieldDataType.String, analyzer_name="en.microsoft"),

                # Classification fields
                SimpleField(name="work_item_type", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="state", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="reason", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="priority", type=SearchFieldDataType.Int32, filterable=True, sortable=True, facetable=True),
                SimpleField(name="severity", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="risk", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # Organization fields
                SimpleField(name="project", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="area_path", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="iteration_path", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="team_project", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # People fields
                SimpleField(name="assigned_to", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="assigned_to_email", type=SearchFieldDataType.String, filterable=True),
                SimpleField(name="created_by", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="created_by_email", type=SearchFieldDataType.String, filterable=True),
                SimpleField(name="changed_by", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="activated_by", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="resolved_by", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="closed_by", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # Date fields
                SimpleField(name="created_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="changed_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="activated_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="resolved_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="closed_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="state_change_date", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),

                # Effort and planning fields
                SimpleField(name="story_points", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="effort", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="original_estimate", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="remaining_work", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="completed_work", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="activity", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # Business value and ranking
                SimpleField(name="business_value", type=SearchFieldDataType.Int32, filterable=True, sortable=True),
                SimpleField(name="stack_rank", type=SearchFieldDataType.Double, filterable=True, sortable=True),
                SimpleField(name="backlog_priority", type=SearchFieldDataType.Double, filterable=True, sortable=True),

                # Tags and categorization
                SearchableField(name="tags", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="board_column", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SimpleField(name="board_column_done", type=SearchFieldDataType.Boolean, filterable=True),
                SimpleField(name="kanban_column", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # Bug-specific fields
                SearchableField(name="found_in_build", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="integrated_in_build", type=SearchFieldDataType.String, filterable=True, facetable=True),
                SearchableField(name="how_found", type=SearchFieldDataType.String, filterable=True, facetable=True),

                # Custom fields (commonly used)
                SearchableField(name="custom_string_01", type=SearchFieldDataType.String, filterable=True),
                SearchableField(name="custom_string_02", type=SearchFieldDataType.String, filterable=True),
                SearchableField(name="custom_string_03", type=SearchFieldDataType.String, filterable=True),
                SimpleField(name="custom_int_01", type=SearchFieldDataType.Int32, filterable=True, sortable=True),
                SimpleField(name="custom_int_02", type=SearchFieldDataType.Int32, filterable=True, sortable=True),
                SimpleField(name="custom_date_01", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),
                SimpleField(name="custom_date_02", type=SearchFieldDataType.DateTimeOffset, filterable=True, sortable=True),

                # Vector fields for AI search
                SearchField(
                    name="title_vector",
                    type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True,
                    vector_search_dimensions=384,  # Adjust based on your embedding model
                    vector_search_profile_name="default-vector-profile"
                ),
                SearchField(
                    name="content_vector",
                    type=SearchFieldDataType.Collection(SearchFieldDataType.Single),
                    searchable=True,
                    vector_search_dimensions=384,
                    vector_search_profile_name="default-vector-profile"
                )
            ]
            
            # Configure vector search
            vector_search = VectorSearch(
                algorithms=[
                    HnswAlgorithmConfiguration(name="default-hnsw")
                ],
                profiles=[
                    VectorSearchProfile(
                        name="default-vector-profile",
                        algorithm_configuration_name="default-hnsw"
                    )
                ]
            )
            
            # Configure enhanced semantic search with more fields
            semantic_config = SemanticConfiguration(
                name="default-semantic-config",
                prioritized_fields=SemanticPrioritizedFields(
                    title_field=SemanticField(field_name="title"),
                    content_fields=[
                        SemanticField(field_name="description"),
                        SemanticField(field_name="repro_steps"),
                        SemanticField(field_name="system_info"),
                        SemanticField(field_name="acceptance_criteria"),
                        SemanticField(field_name="history")
                    ],
                    keywords_fields=[
                        SemanticField(field_name="tags"),
                        SemanticField(field_name="area_path"),
                        SemanticField(field_name="iteration_path"),
                        SemanticField(field_name="work_item_type"),
                        SemanticField(field_name="state"),
                        SemanticField(field_name="severity"),
                        SemanticField(field_name="assigned_to"),
                        SemanticField(field_name="found_in_build"),
                        SemanticField(field_name="how_found")
                    ]
                )
            )
            
            semantic_search = SemanticSearch(
                configurations=[semantic_config]
            )
            
            # Create the index
            index = SearchIndex(
                name=self.index_name,
                fields=fields,
                vector_search=vector_search,
                semantic_search=semantic_search
            )
            
            # Create or update the index
            result = self.index_client.create_or_update_index(index)
            
            log_structured(
                logger,
                "info",
                "Search index ensured",
                extra={
                    "index_name": self.index_name,
                    "field_count": len(fields)
                }
            )
            
        except Exception as e:
            logger.error(f"Error ensuring search index exists: {e}")
            raise
    
    def _extract_comprehensive_fields(self, work_item: WorkItem, ado_fields: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Extract comprehensive fields from work item and ADO fields for indexing.

        Args:
            work_item: The WorkItem object
            ado_fields: Optional raw ADO fields dictionary

        Returns:
            Dictionary with all fields mapped for search index
        """
        # Start with basic WorkItem fields
        document = {
            "id": str(work_item.id),
            "work_item_id": work_item.id,
            "title": work_item.title or "",
            "description": work_item.description or "",
            "work_item_type": work_item.work_item_type or "",
            "state": work_item.state or "",
            "project": work_item.project or "",
            "area_path": work_item.area_path or "",
            "iteration_path": work_item.iteration_path or "",
            "assigned_to": work_item.assigned_to or "",
            "created_by": work_item.created_by or "",
            "created_date": work_item.created_date,
            "changed_date": work_item.changed_date,
            "priority": work_item.priority or 2,
            "severity": work_item.severity or "",
            "tags": work_item.tags or "",
            "repro_steps": work_item.repro_steps or "",
            "system_info": work_item.system_info or "",
        }

        # If we have raw ADO fields, extract additional fields
        if ado_fields:
            # Helper function to safely extract field values
            def safe_extract(field_name: str, default: Any = None) -> Any:
                value = ado_fields.get(field_name, default)
                if isinstance(value, dict) and 'displayName' in value:
                    return value['displayName']
                return value

            # Extract additional ADO fields
            additional_fields = {
                # Revision and metadata
                "revision": ado_fields.get("System.Rev", 0),
                "team_project": safe_extract("System.TeamProject", ""),
                "reason": safe_extract("System.Reason", ""),
                "risk": safe_extract("Microsoft.VSTS.Common.Risk", ""),

                # People fields with email extraction
                "assigned_to_email": ado_fields.get("System.AssignedTo", {}).get("uniqueName", "") if isinstance(ado_fields.get("System.AssignedTo"), dict) else "",
                "created_by_email": ado_fields.get("System.CreatedBy", {}).get("uniqueName", "") if isinstance(ado_fields.get("System.CreatedBy"), dict) else "",
                "changed_by": safe_extract("System.ChangedBy", ""),
                "activated_by": safe_extract("Microsoft.VSTS.Common.ActivatedBy", ""),
                "resolved_by": safe_extract("Microsoft.VSTS.Common.ResolvedBy", ""),
                "closed_by": safe_extract("Microsoft.VSTS.Common.ClosedBy", ""),

                # Date fields
                "activated_date": safe_extract("Microsoft.VSTS.Common.ActivatedDate"),
                "resolved_date": safe_extract("Microsoft.VSTS.Common.ResolvedDate"),
                "closed_date": safe_extract("Microsoft.VSTS.Common.ClosedDate"),
                "state_change_date": safe_extract("Microsoft.VSTS.Common.StateChangeDate"),

                # Effort and planning
                "story_points": safe_extract("Microsoft.VSTS.Scheduling.StoryPoints", 0),
                "effort": safe_extract("Microsoft.VSTS.Scheduling.Effort", 0),
                "original_estimate": safe_extract("Microsoft.VSTS.Scheduling.OriginalEstimate", 0),
                "remaining_work": safe_extract("Microsoft.VSTS.Scheduling.RemainingWork", 0),
                "completed_work": safe_extract("Microsoft.VSTS.Scheduling.CompletedWork", 0),
                "activity": safe_extract("Microsoft.VSTS.Common.Activity", ""),

                # Business value and ranking
                "business_value": safe_extract("Microsoft.VSTS.Common.BusinessValue", 0),
                "stack_rank": safe_extract("Microsoft.VSTS.Common.StackRank", 0),
                "backlog_priority": safe_extract("Microsoft.VSTS.Common.BacklogPriority", 0),

                # Board and Kanban
                "board_column": safe_extract("WEF_BoardColumn", ""),
                "board_column_done": safe_extract("WEF_BoardColumnDone", False),
                "kanban_column": safe_extract("WEF_KanbanColumn", ""),

                # Bug-specific fields
                "found_in_build": safe_extract("Microsoft.VSTS.Build.FoundIn", ""),
                "integrated_in_build": safe_extract("Microsoft.VSTS.Build.IntegrationBuild", ""),
                "how_found": safe_extract("Microsoft.VSTS.Common.HowFound", ""),

                # Additional content fields
                "acceptance_criteria": safe_extract("Microsoft.VSTS.Common.AcceptanceCriteria", ""),
                "history": safe_extract("System.History", ""),

                # Custom fields (commonly used patterns)
                "custom_string_01": safe_extract("Custom.String01", ""),
                "custom_string_02": safe_extract("Custom.String02", ""),
                "custom_string_03": safe_extract("Custom.String03", ""),
                "custom_int_01": safe_extract("Custom.Int01", 0),
                "custom_int_02": safe_extract("Custom.Int02", 0),
                "custom_date_01": safe_extract("Custom.Date01"),
                "custom_date_02": safe_extract("Custom.Date02"),
            }

            # Merge additional fields, only adding non-empty values
            for key, value in additional_fields.items():
                if value is not None and value != "" and value != 0:
                    document[key] = value

        return document

    async def upsert_work_item(self, work_item: WorkItem, embedding: Optional[List[float]] = None, ado_fields: Optional[Dict[str, Any]] = None) -> None:
        """
        Upsert a work item to the search index with comprehensive field mapping.

        ⚠️ PRODUCTION SAFETY: Only indexes Bug 748404 during development.

        Args:
            work_item: The work item to index
            embedding: Optional pre-computed embedding
            ado_fields: Optional raw ADO fields for comprehensive indexing
        """
        # PRODUCTION SAFETY: Only index Bug 748404
        if work_item.id != 748404:
            log_structured(
                logger,
                "warning",
                f"PRODUCTION SAFETY: Search client ignoring work item {work_item.id} - only Bug 748404 is indexed",
                extra={"work_item_id": work_item.id, "allowed_id": 748404}
            )
            return

        try:
            # Extract comprehensive fields
            document = self._extract_comprehensive_fields(work_item, ado_fields)

            # Add embeddings if provided
            if embedding:
                document["title_vector"] = embedding
                document["content_vector"] = embedding

            # Upload the document
            result = self.search_client.upload_documents([document])

            log_structured(
                logger,
                "debug",
                "Upserted work item to search index with comprehensive fields",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title or "") > 50 else work_item.title,
                    "has_embedding": embedding is not None,
                    "has_ado_fields": ado_fields is not None,
                    "total_fields": len(document)
                }
            )

        except Exception as e:
            logger.error(f"Error upserting work item {work_item.id} to search index: {e}")
            raise
    
    async def upsert_work_item_with_embedding(
        self,
        work_item: WorkItem,
        embedding: List[float],
        ado_fields: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Upsert a work item with its embedding to the search index.

        Args:
            work_item: The work item to index
            embedding: The vector embedding
            ado_fields: Optional raw ADO fields for comprehensive indexing
        """
        await self.upsert_work_item(work_item, embedding, ado_fields)
    
    async def hybrid_search(
        self,
        query_text: str,
        query_vector: Optional[List[float]] = None,
        filters: Optional[str] = None,
        top: int = 10,
        include_total_count: bool = False
    ) -> List[SearchResult]:
        """
        Perform hybrid search combining text and vector search.
        
        Args:
            query_text: Text query
            query_vector: Query vector for semantic search
            filters: OData filter expression
            top: Number of results to return
            include_total_count: Whether to include total count
        
        Returns:
            List of search results
        """
        try:
            search_params = {
                "search_text": query_text,
                "top": top,
                "include_total_count": include_total_count,
                "query_type": "semantic",
                "semantic_configuration_name": "default-semantic-config"
            }
            
            if filters:
                search_params["filter"] = filters
            
            # Add vector query if provided
            if query_vector:
                vector_query = VectorizedQuery(
                    vector=query_vector,
                    k_nearest_neighbors=top,
                    fields="content_vector"
                )
                search_params["vector_queries"] = [vector_query]
            
            # Execute search
            results = self.search_client.search(**search_params)
            
            # Convert to SearchResult objects
            search_results = []
            for result in results:
                search_result = SearchResult(
                    work_item_id=int(result["id"]),
                    title=result.get("title", ""),
                    description=result.get("description", ""),
                    work_item_type=result.get("work_item_type", ""),
                    area_path=result.get("area_path", ""),
                    score=result.get("@search.score", 0.0),
                    reranker_score=result.get("@search.reranker_score"),
                    highlights=result.get("@search.highlights", {})
                )
                search_results.append(search_result)
            
            log_structured(
                logger,
                "info",
                "Executed hybrid search",
                extra={
                    "query_text": query_text[:100] + "..." if len(query_text) > 100 else query_text,
                    "has_vector": query_vector is not None,
                    "result_count": len(search_results),
                    "top": top
                }
            )
            
            return search_results
            
        except Exception as e:
            logger.error(f"Error executing hybrid search: {e}")
            raise
    
    async def find_similar_work_items(
        self,
        work_item: WorkItem,
        embedding: List[float],
        similarity_threshold: float = 0.8,
        max_results: int = 5
    ) -> List[SearchResult]:
        """
        Find work items similar to the given work item.
        
        Args:
            work_item: The reference work item
            embedding: Embedding of the work item
            similarity_threshold: Minimum similarity score
            max_results: Maximum number of results
        
        Returns:
            List of similar work items
        """
        try:
            # Build search query combining title and description
            query_text = f"{work_item.title} {work_item.description}"
            
            # Add filters to exclude the same work item and closed items
            filters = f"id ne '{work_item.id}' and state ne 'Closed' and state ne 'Resolved'"
            
            # Perform hybrid search
            results = await self.hybrid_search(
                query_text=query_text,
                query_vector=embedding,
                filters=filters,
                top=max_results * 2  # Get more results to filter by threshold
            )
            
            # Filter by similarity threshold
            similar_items = [
                result for result in results
                if result.score >= similarity_threshold
            ][:max_results]
            
            log_structured(
                logger,
                "info",
                "Found similar work items",
                extra={
                    "reference_work_item_id": work_item.id,
                    "similar_count": len(similar_items),
                    "threshold": similarity_threshold
                }
            )
            
            return similar_items

        except Exception as e:
            logger.error(f"Error finding similar work items: {e}")
            raise

    async def index_work_item(self, work_item: WorkItem, ado_fields: Optional[Dict[str, Any]] = None) -> None:
        """
        Index a work item for future similarity searches with comprehensive field mapping.

        Args:
            work_item: The work item to index
            ado_fields: Optional raw ADO fields for comprehensive indexing
        """
        await self.upsert_work_item(work_item, ado_fields=ado_fields)

    async def index_work_item_from_ado(self, ado_work_item: Dict[str, Any]) -> None:
        """
        Index a work item directly from ADO API response with all available fields.

        Args:
            ado_work_item: Raw work item data from Azure DevOps API
        """
        try:
            # Convert ADO work item to WorkItem object
            from ..workitem_created.handler import convert_ado_to_work_item
            work_item = convert_ado_to_work_item(ado_work_item)

            # Extract all ADO fields
            ado_fields = ado_work_item.get("fields", {})

            # Index with comprehensive field mapping
            await self.upsert_work_item(work_item, ado_fields=ado_fields)

            log_structured(
                logger,
                "info",
                "Indexed work item from ADO with comprehensive fields",
                extra={
                    "work_item_id": work_item.id,
                    "title": work_item.title[:50] + "..." if len(work_item.title or "") > 50 else work_item.title,
                    "ado_fields_count": len(ado_fields)
                }
            )

        except Exception as e:
            logger.error(f"Error indexing work item from ADO: {e}")
            raise
