# Deploy Teams Logic App for Auto Defect Triage
# ============================================
#
# This script deploys the Teams Logic App that handles adaptive card posting
# and waits for user responses to update work items like Bug-748404.
#
# Usage:
#   .\deploy-logic-app-teams.ps1 -ResourceGroupName "rg-autodefecttriage" -LogicAppName "teams-notification-logic-app"

param(
    [Parameter(Mandatory=$true)]
    [string]$ResourceGroupName,
    
    [Parameter(Mandatory=$true)]
    [string]$LogicAppName,
    
    [string]$Location = "East US",
    [string]$TeamsWebhookUrl = "",
    [string]$FunctionAppUrl = "https://your-function-app.azurewebsites.net",
    [string]$FunctionAppKey = ""
)

function Write-StepHeader {
    param([string]$Title, [string]$Icon = "🔄")
    
    Write-Host ""
    Write-Host "$Icon $Title" -ForegroundColor Cyan
    Write-Host ("-" * 60) -ForegroundColor Gray
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

Write-StepHeader "Teams Logic App Deployment" "🚀"

Write-Info "Resource Group: $ResourceGroupName"
Write-Info "Logic App Name: $LogicAppName"
Write-Info "Location: $Location"
Write-Info "Function App URL: $FunctionAppUrl"

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json | ConvertFrom-Json
    Write-Success "Azure CLI version: $($azVersion.'azure-cli')"
} catch {
    Write-Error "Azure CLI is not installed or not in PATH"
    exit 1
}

# Check if logged in to Azure
try {
    $account = az account show --output json | ConvertFrom-Json
    Write-Success "Logged in as: $($account.user.name)"
} catch {
    Write-Error "Not logged in to Azure. Please run 'az login'"
    exit 1
}

Write-StepHeader "Loading Logic App Definition" "📋"

# Load the Logic App definition
$definitionPath = "logic-app-teams-notification.json"
if (-not (Test-Path $definitionPath)) {
    Write-Error "Logic App definition file not found: $definitionPath"
    exit 1
}

try {
    $logicAppDefinition = Get-Content $definitionPath -Raw | ConvertFrom-Json
    Write-Success "Loaded Logic App definition from $definitionPath"
} catch {
    Write-Error "Failed to parse Logic App definition: $_"
    exit 1
}

Write-StepHeader "Updating Parameters" "⚙️"

# Update parameters in the definition
if ($TeamsWebhookUrl) {
    $logicAppDefinition.parameters.teams_webhook_url.defaultValue = $TeamsWebhookUrl
    Write-Success "Updated Teams webhook URL"
}

if ($FunctionAppUrl) {
    $logicAppDefinition.parameters.function_app_url.defaultValue = $FunctionAppUrl
    Write-Success "Updated Function App URL"
}

# Convert back to JSON
$updatedDefinition = $logicAppDefinition | ConvertTo-Json -Depth 20

# Create temporary file with updated definition
$tempDefinitionPath = "temp-teams-logic-app-definition.json"
$updatedDefinition | Out-File -FilePath $tempDefinitionPath -Encoding UTF8

Write-StepHeader "Creating Logic App" "🏗️"

Write-Info "Creating Logic App: $LogicAppName..."

# Create the Logic App
try {
    az logic workflow create `
        --resource-group $ResourceGroupName `
        --name $LogicAppName `
        --location $Location `
        --definition $tempDefinitionPath `
        --parameters teams_webhook_url=$TeamsWebhookUrl `
        --parameters function_app_url=$FunctionAppUrl `
        --parameters function_app_key=$FunctionAppKey

    Write-Success "Logic App created successfully"
} catch {
    Write-Error "Failed to create Logic App: $_"
    exit 1
} finally {
    # Clean up temporary file
    if (Test-Path $tempDefinitionPath) {
        Remove-Item $tempDefinitionPath
    }
}

Write-StepHeader "Getting Logic App URL" "🔗"

# Get the Logic App trigger URL
try {
    $triggerUrl = az logic workflow show `
        --resource-group $ResourceGroupName `
        --name $LogicAppName `
        --query "accessEndpoint" `
        --output tsv

    Write-Success "Logic App deployed successfully!"
    Write-Info "Trigger URL: $triggerUrl"
    Write-Info ""
    Write-Info "Next steps:"
    Write-Info "1. Update your function app configuration with this Logic App URL"
    Write-Info "2. Configure Teams connection in the Logic App"
    Write-Info "3. Test the workflow with Bug-748404"
    
} catch {
    Write-Error "Failed to get Logic App URL: $_"
}

Write-StepHeader "Deployment Complete" "🎉"
