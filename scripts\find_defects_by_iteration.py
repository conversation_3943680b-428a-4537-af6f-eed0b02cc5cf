#!/usr/bin/env python3
"""
Find defects by Iteration Path: Air4 Channels Testing\AI Testing - Defect Management
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def scan_work_items_for_iteration(ado_client, start_id=1, count=1000):
    """Scan work items by ID to find ones with the target iteration."""
    print(f"\n🔍 Scanning work items for Iteration Path...")
    print(f"   Target: 'Air4 Channels Testing\\AI Testing - Defect Management'")
    
    target_iteration = "Air4 Channels Testing\\AI Testing - Defect Management"
    matching_items = []
    all_iterations = set()
    defects_found = []
    
    print(f"   Scanning IDs {start_id} to {start_id + count - 1}...")
    
    for work_item_id in range(start_id, start_id + count):
        try:
            item = await ado_client.get_work_item(work_item_id)
            if item:
                fields = item.get('fields', {})
                iteration_path = fields.get('System.IterationPath', '')
                work_item_type = fields.get('System.WorkItemType', '')
                
                # Collect all unique iteration paths
                if iteration_path:
                    all_iterations.add(iteration_path)
                
                # Check if this matches our target iteration
                if target_iteration.lower() in iteration_path.lower():
                    matching_items.append(item)
                    print(f"   ✅ Found match: ID {work_item_id} - {work_item_type}")
                
                # Also collect all defects regardless of iteration
                if work_item_type.lower() in ['bug', 'defect']:
                    defects_found.append(item)
                
                # Progress indicator
                if work_item_id % 100 == 0:
                    print(f"   Scanned up to ID {work_item_id}... (found {len(matching_items)} matches, {len(defects_found)} defects)")
                    
        except Exception:
            # Work item doesn't exist or can't be accessed
            continue
    
    print(f"\n📊 SCAN RESULTS:")
    print(f"   Total work items found: {len(all_iterations)} (with unique iterations)")
    print(f"   Items matching target iteration: {len(matching_items)}")
    print(f"   Total defects/bugs found: {len(defects_found)}")
    
    return matching_items, defects_found, all_iterations


async def analyze_iterations(all_iterations):
    """Analyze and display all found iteration paths."""
    print(f"\n📍 ALL ITERATION PATHS FOUND ({len(all_iterations)}):")
    print("=" * 80)
    
    # Sort iterations and look for AI Testing related ones
    sorted_iterations = sorted(all_iterations)
    ai_testing_iterations = []
    
    for iteration in sorted_iterations:
        print(f"   - {iteration}")
        if 'ai testing' in iteration.lower() or 'defect management' in iteration.lower():
            ai_testing_iterations.append(iteration)
    
    if ai_testing_iterations:
        print(f"\n🤖 AI TESTING RELATED ITERATIONS:")
        for iteration in ai_testing_iterations:
            print(f"   ⭐ {iteration}")
    
    return ai_testing_iterations


async def display_defects(defects, title="DEFECTS FOUND"):
    """Display defect information."""
    if not defects:
        print(f"\n❌ No {title.lower()}")
        return
    
    print(f"\n🐛 {title} ({len(defects)}):")
    print("=" * 120)
    print(f"{'ID':<8} {'Type':<12} {'State':<12} {'Iteration Path':<50} {'Title'}")
    print("-" * 120)
    
    for item in defects:
        fields = item.get('fields', {})
        work_item_id = item.get('id', 'N/A')
        title = fields.get('System.Title', 'No Title')[:30]
        state = fields.get('System.State', 'N/A')
        work_type = fields.get('System.WorkItemType', 'N/A')
        iteration_path = fields.get('System.IterationPath', 'N/A')[:50]
        
        print(f"{work_item_id:<8} {work_type:<12} {state:<12} {iteration_path:<50} {title}")
    
    # Show detailed info for first few items
    print(f"\n📋 DETAILED INFO (First 5 items):")
    for i, item in enumerate(defects[:5]):
        fields = item.get('fields', {})
        work_item_id = item.get('id')
        
        print(f"\n📄 Work Item {work_item_id}:")
        print(f"   Title: {fields.get('System.Title', 'N/A')}")
        print(f"   Type: {fields.get('System.WorkItemType', 'N/A')}")
        print(f"   State: {fields.get('System.State', 'N/A')}")
        print(f"   Iteration: {fields.get('System.IterationPath', 'N/A')}")
        print(f"   Area: {fields.get('System.AreaPath', 'N/A')}")
        print(f"   Assigned To: {fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')}")
        print(f"   Created: {fields.get('System.CreatedDate', 'N/A')}")
        print(f"   Priority: {fields.get('Microsoft.VSTS.Common.Priority', 'N/A')}")
        print(f"   Severity: {fields.get('Microsoft.VSTS.Common.Severity', 'N/A')}")
        print(f"   URL: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{work_item_id}")


async def export_results(matching_items, all_defects, all_iterations):
    """Export results to JSON file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"defects_by_iteration_{timestamp}.json"
    
    export_data = {
        "timestamp": timestamp,
        "target_iteration": "Air4 Channels Testing\\AI Testing - Defect Management",
        "matching_items_count": len(matching_items),
        "total_defects_count": len(all_defects),
        "total_iterations_found": len(all_iterations),
        "matching_items": [],
        "all_defects": [],
        "all_iterations": sorted(list(all_iterations))
    }
    
    # Export matching items
    for item in matching_items:
        fields = item.get('fields', {})
        export_data["matching_items"].append({
            "id": item.get('id'),
            "title": fields.get('System.Title'),
            "type": fields.get('System.WorkItemType'),
            "state": fields.get('System.State'),
            "iteration_path": fields.get('System.IterationPath'),
            "area_path": fields.get('System.AreaPath'),
            "assigned_to": fields.get('System.AssignedTo', {}).get('displayName'),
            "created_date": fields.get('System.CreatedDate'),
            "priority": fields.get('Microsoft.VSTS.Common.Priority'),
            "severity": fields.get('Microsoft.VSTS.Common.Severity'),
            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
        })
    
    # Export all defects
    for item in all_defects:
        fields = item.get('fields', {})
        export_data["all_defects"].append({
            "id": item.get('id'),
            "title": fields.get('System.Title'),
            "type": fields.get('System.WorkItemType'),
            "state": fields.get('System.State'),
            "iteration_path": fields.get('System.IterationPath'),
            "area_path": fields.get('System.AreaPath'),
            "assigned_to": fields.get('System.AssignedTo', {}).get('displayName'),
            "created_date": fields.get('System.CreatedDate'),
            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
        })
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results exported to: {filename}")
    return filename


async def main():
    """Main function."""
    print("🚀 Finding Defects by Iteration Path")
    print("=" * 60)
    print("🎯 Target: Air4 Channels Testing\\AI Testing - Defect Management")
    print("=" * 60)
    
    try:
        # Get configuration and create client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        ado_client = AdoClient(config)
        
        # Test basic connectivity
        print(f"\n🔍 Testing connectivity...")
        try:
            test_item = await ado_client.get_work_item(1)
            print("✅ Connection successful")
        except Exception as e:
            print(f"⚠️ Connection test: {e}")
        
        # Scan for work items with the target iteration
        matching_items, all_defects, all_iterations = await scan_work_items_for_iteration(
            ado_client, start_id=1, count=2000  # Scan more IDs
        )
        
        # Analyze all iterations found
        ai_testing_iterations = await analyze_iterations(all_iterations)
        
        # Display results
        if matching_items:
            await display_defects(matching_items, "ITEMS WITH TARGET ITERATION")
        else:
            print(f"\n❌ No items found with exact iteration: 'Air4 Channels Testing\\AI Testing - Defect Management'")
            
            if ai_testing_iterations:
                print(f"\n💡 However, found {len(ai_testing_iterations)} AI Testing related iterations:")
                for iteration in ai_testing_iterations:
                    print(f"   - {iteration}")
                print(f"\n🔍 You might want to search for items in these iterations instead.")
        
        # Show all defects found
        if all_defects:
            await display_defects(all_defects, "ALL DEFECTS/BUGS FOUND")
        
        # Export results
        filename = await export_results(matching_items, all_defects, all_iterations)
        
        # Summary
        print(f"\n📊 FINAL SUMMARY:")
        print("=" * 60)
        print(f"Items with target iteration: {len(matching_items)}")
        print(f"Total defects/bugs found: {len(all_defects)}")
        print(f"Total unique iterations: {len(all_iterations)}")
        print(f"AI Testing related iterations: {len(ai_testing_iterations)}")
        print(f"Results exported to: {filename}")
        
        if matching_items:
            print(f"\n🎉 Found {len(matching_items)} items with the target iteration!")
        elif all_defects:
            print(f"\n💡 No items with exact iteration, but found {len(all_defects)} defects total")
        else:
            print(f"\n⚠️ No defects found. The project might be empty or have different work item types.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
