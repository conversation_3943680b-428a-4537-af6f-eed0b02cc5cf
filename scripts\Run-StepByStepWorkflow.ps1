# AutoDefectTriage Step-by-Step Workflow Execution
# ================================================
# 
# This PowerShell script runs the complete step-by-step workflow
# from work item creation through to Teams and email notifications.
#
# Usage:
#   .\Run-StepByStepWorkflow.ps1 -WorkItemId "748404"  # Use test work item only
#   .\Run-StepByStepWorkflow.ps1 -RecentHours 24
#   .\Run-StepByStepWorkflow.ps1 -Demo
#
# IMPORTANT: For testing, use only work item 748404 to protect production data

param(
    [string]$WorkItemId,
    [int]$RecentHours,
    [switch]$Demo,
    [string]$FunctionUrl = "https://your-function-app.azurewebsites.net",
    [string]$FunctionKey = ""
)

function Write-StepHeader {
    param([string]$Title, [string]$Icon = "🔄")
    
    Write-Host ""
    Write-Host "$Icon $Title" -ForegroundColor Cyan
    Write-Host ("-" * 60) -ForegroundColor Gray
}

function Write-Success {
    param([string]$Message)
    Write-Host "✅ $Message" -ForegroundColor Green
}

function Write-Error {
    param([string]$Message)
    Write-Host "❌ $Message" -ForegroundColor Red
}

function Write-Info {
    param([string]$Message)
    Write-Host "ℹ️  $Message" -ForegroundColor Blue
}

function Invoke-WorkflowAPI {
    param(
        [hashtable]$Payload,
        [string]$Url,
        [string]$Key
    )
    
    $headers = @{
        "Content-Type" = "application/json"
    }
    
    if ($Key) {
        $headers["x-functions-key"] = $Key
    }
    
    $body = $Payload | ConvertTo-Json -Depth 10
    
    try {
        $response = Invoke-RestMethod -Uri "$Url/api/step_by_step_workflow" -Method POST -Headers $headers -Body $body
        return $response
    }
    catch {
        Write-Error "API call failed: $($_.Exception.Message)"
        return $null
    }
}

function Show-WorkflowResults {
    param([object]$Result)
    
    if (-not $Result) {
        Write-Error "No results to display"
        return
    }
    
    Write-StepHeader "WORKFLOW EXECUTION RESULTS" "📊"
    
    # Basic Information
    Write-Host "📋 Basic Information:" -ForegroundColor Yellow
    Write-Host "   Work Item ID: $($Result.work_item_id)"
    Write-Host "   Success: $(if ($Result.success) { '✅ YES' } else { '❌ NO' })"
    Write-Host "   Processing Mode: $($Result.processing_mode)"
    Write-Host "   Total Time: $($Result.total_time) seconds"
    Write-Host ""
    
    # Step Timings
    if ($Result.step_timings) {
        Write-Host "⏱️  Step Execution Times:" -ForegroundColor Yellow
        $Result.step_timings.PSObject.Properties | ForEach-Object {
            $stepName = $_.Name -replace "_", " " | ForEach-Object { (Get-Culture).TextInfo.ToTitleCase($_) }
            Write-Host "   $stepName`: $($_.Value)s"
        }
        Write-Host ""
    }
    
    # Notifications
    Write-Host "📬 Notification Delivery:" -ForegroundColor Yellow
    Write-Host "   Email Sent: $(if ($Result.email_sent) { '✅ YES' } else { '❌ NO' })"
    Write-Host "   Teams Sent: $(if ($Result.teams_sent) { '✅ YES' } else { '❌ NO' })"
    Write-Host ""
    
    # AI Triage Results
    if ($Result.triage_result) {
        Write-Host "🤖 AI Triage Results:" -ForegroundColor Yellow
        Write-Host "   Assigned To: $($Result.triage_result.assigned_to)"
        Write-Host "   Priority: $($Result.triage_result.priority)"
        Write-Host "   Confidence: $($Result.triage_result.confidence_score)"
        Write-Host "   Duplicates Found: $($Result.triage_result.duplicates.Count)"
        if ($Result.triage_result.reasoning) {
            $reasoning = $Result.triage_result.reasoning
            if ($reasoning.Length -gt 100) {
                $reasoning = $reasoning.Substring(0, 100) + "..."
            }
            Write-Host "   Reasoning: $reasoning"
        }
        Write-Host ""
    }
    
    # Errors
    if ($Result.errors -and $Result.errors.Count -gt 0) {
        Write-Host "❌ Errors Encountered:" -ForegroundColor Red
        $Result.errors | ForEach-Object { Write-Host "   • $_" }
        Write-Host ""
    }
    
    # Workflow Steps
    if ($Result.workflow_steps) {
        Write-Host "📋 Workflow Steps Executed:" -ForegroundColor Yellow
        $Result.workflow_steps.PSObject.Properties | ForEach-Object {
            Write-Host "   $($_.Name): $($_.Value)"
        }
    }
}

function Show-BatchResults {
    param([object]$Result)
    
    if (-not $Result) {
        Write-Error "No batch results to display"
        return
    }
    
    Write-StepHeader "BATCH PROCESSING RESULTS" "📦"
    
    # Summary
    Write-Host "📊 Processing Summary:" -ForegroundColor Yellow
    Write-Host "   Total Items: $($Result.total_items_processed)"
    Write-Host "   Successful: $($Result.successful_items) ✅"
    Write-Host "   Failed: $($Result.failed_items) ❌"
    
    if ($Result.total_items_processed -gt 0) {
        $successRate = ($Result.successful_items / $Result.total_items_processed) * 100
        Write-Host "   Success Rate: $($successRate.ToString('F1'))%"
    }
    
    Write-Host "   Hours Back: $($Result.hours_back)"
    Write-Host "   Execution Time: $($Result.execution_time_seconds) seconds"
    Write-Host ""
    
    # Individual Results
    if ($Result.results -and $Result.results.Count -gt 0) {
        Write-Host "📋 Individual Item Results:" -ForegroundColor Yellow
        $Result.results | ForEach-Object -Begin { $i = 1 } -Process {
            $status = if ($_.success) { "✅" } else { "❌" }
            $email = if ($_.email_sent) { "✅" } else { "❌" }
            $teams = if ($_.teams_sent) { "✅" } else { "❌" }
            
            Write-Host "   $i. Work Item $($_.work_item_id): $status"
            Write-Host "      Email: $email | Teams: $teams"
            
            if ($_.errors -and $_.errors.Count -gt 0) {
                Write-Host "      Errors: $($_.errors -join ', ')" -ForegroundColor Red
            }
            
            $i++
        }
    }
}

function Run-DemoWorkflow {
    Write-StepHeader "DEMO WORKFLOW EXECUTION" "🎭"
    
    Write-Info "Running demonstration of the complete step-by-step workflow..."
    Write-Info "This will show all 5 steps in action with sample data."
    Write-Host ""
    
    # Run the Python test script instead of API call for demo
    $scriptPath = Join-Path $PSScriptRoot "test_workflow_execution.py"
    
    if (Test-Path $scriptPath) {
        Write-Info "Executing demo workflow script..."
        try {
            python $scriptPath
            Write-Success "Demo workflow completed successfully!"
        }
        catch {
            Write-Error "Demo execution failed: $($_.Exception.Message)"
        }
    }
    else {
        Write-Error "Demo script not found at: $scriptPath"
        Write-Info "Please ensure test_workflow_execution.py is in the scripts directory."
    }
}

# Main execution logic
Write-Host "🚀 AutoDefectTriage Step-by-Step Workflow Execution" -ForegroundColor Cyan
Write-Host "=" * 80 -ForegroundColor Gray
Write-Host ""

if ($Demo) {
    Run-DemoWorkflow
}
elseif ($WorkItemId) {
    Write-StepHeader "SINGLE WORK ITEM PROCESSING" "📝"
    Write-Info "Processing work item: $WorkItemId"

    # Production Safety Warning
    Write-Host "⚠️  PRODUCTION SAFETY NOTICE:" -ForegroundColor Yellow
    Write-Host "   This system is configured with production safety guardrails." -ForegroundColor Yellow
    Write-Host "   Check SAFETY_ONLY_WORKITEM_ID and READ_ONLY settings in configuration." -ForegroundColor Yellow
    Write-Host "   In READ_ONLY mode, no work items will be modified." -ForegroundColor Yellow
    Write-Host ""

    if ($WorkItemId) {
        Write-Info "Processing work item: $WorkItemId"
    } else {
        Write-Info "No work item ID specified - will use configuration defaults"
    }

    Write-Info "Function URL: $FunctionUrl"
    Write-Host ""

    $payload = @{
        work_item_id = $WorkItemId
    }

    Write-Info "Sending request to workflow API..."
    $result = Invoke-WorkflowAPI -Payload $payload -Url $FunctionUrl -Key $FunctionKey
    
    if ($result) {
        Show-WorkflowResults -Result $result
        
        if ($result.success) {
            Write-Success "Workflow completed successfully!"
        }
        else {
            Write-Error "Workflow completed with errors."
        }
    }
}
elseif ($RecentHours) {
    Write-StepHeader "BATCH PROCESSING" "📦"
    Write-Info "Processing work items from last $RecentHours hours"
    Write-Info "Function URL: $FunctionUrl"
    Write-Host ""
    
    $payload = @{
        hours_back = $RecentHours
    }
    
    Write-Info "Sending batch request to workflow API..."
    $result = Invoke-WorkflowAPI -Payload $payload -Url $FunctionUrl -Key $FunctionKey
    
    if ($result) {
        Show-BatchResults -Result $result
        
        if ($result.success) {
            Write-Success "Batch processing completed successfully!"
        }
        else {
            Write-Error "Batch processing completed with some failures."
        }
    }
}
else {
    Write-Host "Usage Examples:" -ForegroundColor Yellow
    Write-Host "  .\Run-StepByStepWorkflow.ps1 -Demo" -ForegroundColor Green
    Write-Host "  .\Run-StepByStepWorkflow.ps1 -WorkItemId '12345'" -ForegroundColor Green
    Write-Host "  .\Run-StepByStepWorkflow.ps1 -RecentHours 24" -ForegroundColor Green
    Write-Host ""
    Write-Host "Parameters:" -ForegroundColor Yellow
    Write-Host "  -Demo           : Run demonstration with sample data"
    Write-Host "  -WorkItemId     : Process specific work item ID"
    Write-Host "  -RecentHours    : Process items from last N hours"
    Write-Host "  -FunctionUrl    : Azure Function app URL"
    Write-Host "  -FunctionKey    : Function key for authentication"
}

Write-Host ""
Write-Host "✨ Execution completed!" -ForegroundColor Green
