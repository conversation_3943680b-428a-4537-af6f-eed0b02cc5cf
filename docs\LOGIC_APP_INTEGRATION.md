# Logic App Integration for Teams Personal Chat

This document describes the Teams personal chat integration via Azure Logic App for the AutoDefectTriage system.

## Overview

The AutoDefectTriage system integrates with Azure Logic App to send personalized Teams messages to suggested assignees when work items are created or updated. This provides real-time notifications with triage recommendations and allows for interactive responses back to Azure DevOps.

## Architecture

```
ADO Work Item Event → Azure Function → Logic App → Teams Personal Chat → User Response → ADO Update
```

1. **Event Trigger**: ADO Service Hook sends work item events to Azure Function
2. **Triage Processing**: Function analyzes work item and generates recommendations
3. **Logic App Call**: Function sends notification request to Logic App
4. **Teams Message**: Logic App delivers personalized message to suggested assignee
5. **User Response**: User can interact with Adaptive Card actions
6. **ADO Update**: Logic App updates ADO based on user response

## Configuration

### Environment Variables

Add these environment variables to your Function App settings:

```bash
# Required: Logic App endpoint URL
LOGICAPP_URL="https://prod-12.uksouth.logic.azure.com:443/workflows/teams-personal-chat/triggers/manual/paths/invoke"

# Optional: Bypass Logic App for testing (default: false)
BYPASS_LOGICAPP_AND_UPDATE_ADO="false"
```

### Local Development

For local development, add to `functions/local.settings.json`:

```json
{
  "Values": {
    "LOGICAPP_URL": "https://your-logic-app-url.azure.com/workflows/teams-personal-chat/triggers/manual/paths/invoke",
    "BYPASS_LOGICAPP_AND_UPDATE_ADO": "false"
  }
}
```

## Logic App Payload

The Function sends a JSON payload to the Logic App with the following structure:

```json
{
  "To": "<EMAIL>",
  "Subject": "[#12345] Bug Title",
  "Body": "Work item summary with recommendations",
  "work_item_id": 12345,
  "adaptive_card": {
    "type": "AdaptiveCard",
    "version": "1.3",
    "body": [...],
    "actions": [...]
  }
}
```

See `samples/logicapp_request.json` for a complete example.

## Response Handling

### Async Processing (202 Response)

When Logic App returns HTTP 202, it indicates async processing:

```json
{
  "status": "accepted",
  "trackingId": "workflow-run-id-123"
}
```

The `trackingId` can be used to track the Logic App execution.

### Sync Processing (200 Response)

When Logic App completes immediately:

```json
{
  "status": "completed",
  "message": "Teams message sent successfully",
  "messageId": "msg-123"
}
```

## Adaptive Cards

The system can optionally send Adaptive Cards for interactive Teams messages. Cards include:

- **Work Item Details**: ID, title, type, state, priority
- **Recommendations**: Suggested priority with rationale
- **Similar Items**: Historical work items for context
- **Actions**: Accept assignment, reassign, update priority

### Sample Adaptive Card Actions

```json
{
  "actions": [
    {
      "type": "Action.OpenUrl",
      "title": "View Work Item",
      "url": "https://dev.azure.com/org/project/_workitems/edit/12345"
    },
    {
      "type": "Action.Submit",
      "title": "Accept Assignment",
      "data": {
        "action": "accept_assignment",
        "work_item_id": 12345,
        "assignee": "<EMAIL>"
      }
    }
  ]
}
```

## Assignee Selection Logic

The system determines the notification target using this priority:

1. **Top Suggested Assignee**: From ML-based recommendations
2. **Current Assignee**: Fallback if no suggestions available
3. **Skip Notification**: If no target email found

## Error Handling

### Logic App Errors

- **400 Bad Request**: Invalid payload or email address
- **401 Unauthorized**: Authentication failure
- **404 Not Found**: Logic App endpoint not found
- **500 Server Error**: Logic App internal error
- **Timeout**: Request timeout (30 seconds)

All errors are logged with structured logging and App Insights events.

### Bypass Mode

For testing, set `BYPASS_LOGICAPP_AND_UPDATE_ADO=true` to:
- Skip Logic App calls
- Directly update ADO with test data
- Log bypass actions

## Observability

### App Insights Events

The system logs custom events to App Insights:

```json
{
  "event_name": "logicapp_request_sent",
  "properties": {
    "wi_id": 12345,
    "to": "example.com",
    "trackingId": "workflow-run-123",
    "duration_ms": 250
  }
}
```

```json
{
  "event_name": "logicapp_request_failed",
  "properties": {
    "status": 400,
    "error": "Invalid email address",
    "wi_id": 12345,
    "duration_ms": 150
  }
}
```

### Structured Logging

All Logic App interactions are logged with structured data:

```json
{
  "level": "info",
  "message": "Logic App notification sent for work item 12345",
  "work_item_id": 12345,
  "tracking_id": "workflow-run-123",
  "target_email_domain": "example.com",
  "duration_ms": 250
}
```

## Security & Privacy

### PII Protection

- Email addresses are logged as domain-only (`example.com`)
- Full email addresses are not stored in logs
- Adaptive Card content is sanitized

### Authentication

- Logic App uses managed identity or SAS tokens
- Function App authenticates via Azure AD
- Teams messages use service principal

## Testing

### Unit Tests

Run Logic App client tests:

```bash
cd tests
python -m pytest test_logicapp_client.py -v
```

### Integration Tests

Run handler integration tests:

```bash
cd tests
python -m pytest test_handler_integration.py -v
```

### Manual Testing

1. Set `BYPASS_LOGICAPP_AND_UPDATE_ADO=true`
2. Trigger work item event
3. Verify logs show "bypassed" status
4. Set `BYPASS_LOGICAPP_AND_UPDATE_ADO=false`
5. Trigger work item event
6. Verify Logic App call in logs

## Deployment

### Function App Settings

```bash
az functionapp config appsettings set \
  --name your-function-app \
  --resource-group your-rg \
  --settings \
  LOGICAPP_URL="https://your-logic-app-url" \
  BYPASS_LOGICAPP_AND_UPDATE_ADO="false"
```

### Logic App Configuration

Ensure your Logic App:
1. Accepts HTTP POST requests
2. Handles the payload structure
3. Returns appropriate status codes
4. Implements Teams message delivery
5. Handles user response actions

## Troubleshooting

### Common Issues

1. **Logic App URL not configured**
   - Error: `LOGICAPP_URL not configured`
   - Solution: Set `LOGICAPP_URL` environment variable

2. **Logic App returns 400**
   - Check payload structure
   - Verify email address format
   - Review Logic App logs

3. **Timeout errors**
   - Check Logic App performance
   - Verify network connectivity
   - Consider increasing timeout

4. **No notifications sent**
   - Check assignee selection logic
   - Verify email addresses in ADO
   - Review safety guardrails

### Debug Mode

Enable detailed logging by setting log level to DEBUG:

```json
{
  "logging": {
    "logLevel": {
      "default": "Debug"
    }
  }
}
```

## Future Enhancements

- **Rich Adaptive Cards**: Enhanced UI with more interactive elements
- **Bulk Notifications**: Handle multiple work items in single request
- **Response Webhooks**: Direct ADO updates from Teams responses
- **Analytics Dashboard**: Track notification effectiveness
- **A/B Testing**: Compare notification formats
