"""
Qdrant Vector Store Implementation
Provides vector search capabilities using Qdrant vector database.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base import VectorStore, VectorDoc, SearchHit, SearchFilters

logger = logging.getLogger(__name__)


class QdrantClient:
    """Qdrant implementation of VectorStore protocol."""
    
    def __init__(self, config):
        self.config = config
        self.url = config.QDRANT_URL
        self.collection_name = "workitems"
        self._client = None
        self._models = None
    
    def _get_client(self):
        """Get Qdrant client (lazy initialization)."""
        if self._client is None:
            try:
                from qdrant_client import QdrantClient as QdrantClientLib
                from qdrant_client.http import models
                
                self._client = QdrantClientLib(url=self.url)
                self._models = models
                
            except ImportError:
                raise ImportError("qdrant-client package required for Qdrant vector store")
        
        return self._client
    
    async def upsert(self, doc: VectorDoc) -> None:
        """Insert or update a document."""
        client = self._get_client()
        
        try:
            # Convert to Qdrant point format
            point = self._models.PointStruct(
                id=doc.id,
                vector=doc.vector,
                payload={
                    "text": doc.text,
                    **doc.metadata
                }
            )
            
            # Upsert the point
            client.upsert(
                collection_name=self.collection_name,
                points=[point]
            )
            
            logger.debug(f"Upserted document {doc.id} to Qdrant")
            
        except Exception as e:
            logger.error(f"Error upserting document {doc.id}: {e}")
            raise
    
    async def upsert_batch(self, docs: List[VectorDoc]) -> None:
        """Insert or update multiple documents in batch."""
        if not docs:
            return
        
        client = self._get_client()
        
        try:
            # Convert to Qdrant points
            points = []
            for doc in docs:
                point = self._models.PointStruct(
                    id=doc.id,
                    vector=doc.vector,
                    payload={
                        "text": doc.text,
                        **doc.metadata
                    }
                )
                points.append(point)
            
            # Batch upsert
            client.upsert(
                collection_name=self.collection_name,
                points=points
            )
            
            logger.info(f"Upserted {len(docs)} documents to Qdrant")
            
        except Exception as e:
            logger.error(f"Error upserting batch of {len(docs)} documents: {e}")
            raise
    
    async def search(
        self, 
        query_vector: List[float], 
        k: int = 10,
        filters: Optional[SearchFilters] = None
    ) -> List[SearchHit]:
        """Search using vector similarity."""
        client = self._get_client()
        
        try:
            # Build search request
            search_params = {
                "collection_name": self.collection_name,
                "query_vector": query_vector,
                "limit": k,
                "with_payload": True,
                "with_vectors": False
            }
            
            # Add filters if provided
            if filters:
                search_params["query_filter"] = self._build_filter(filters)
            
            # Execute search
            results = client.search(**search_params)
            
            # Convert to SearchHit objects
            hits = []
            for result in results:
                payload = result.payload or {}
                
                hit = SearchHit(
                    id=str(result.id),
                    text=payload.get("text", ""),
                    score=result.score,
                    metadata={k: v for k, v in payload.items() if k != "text"}
                )
                hits.append(hit)
            
            logger.debug(f"Vector search returned {len(hits)} results")
            return hits
            
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            raise
    
    async def search_hybrid(
        self,
        query_text: str,
        query_vector: List[float],
        k: int = 10,
        filters: Optional[SearchFilters] = None,
        alpha: float = 0.5
    ) -> List[SearchHit]:
        """
        Hybrid search combining text and vector similarity.
        Note: Qdrant doesn't have built-in hybrid search, so we simulate it.
        """
        client = self._get_client()
        
        try:
            # For now, just do vector search
            # TODO: Implement proper hybrid search when Qdrant supports it
            logger.warning("Qdrant hybrid search not fully implemented, falling back to vector search")
            return await self.search(query_vector, k, filters)
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            raise
    
    def _build_filter(self, filters: SearchFilters) -> Dict[str, Any]:
        """Build Qdrant filter from SearchFilters."""
        conditions = []
        
        if filters.project:
            conditions.append({
                "key": "project",
                "match": {"value": filters.project}
            })
        
        if filters.work_item_type:
            conditions.append({
                "key": "work_item_type",
                "match": {"value": filters.work_item_type}
            })
        
        if filters.state:
            conditions.append({
                "key": "state",
                "match": {"any": filters.state}
            })
        
        if filters.assigned_to:
            conditions.append({
                "key": "assigned_to",
                "match": {"value": filters.assigned_to}
            })
        
        if filters.priority:
            conditions.append({
                "key": "priority",
                "match": {"any": filters.priority}
            })
        
        if filters.created_after:
            conditions.append({
                "key": "created_date",
                "range": {"gte": filters.created_after.isoformat()}
            })
        
        if filters.created_before:
            conditions.append({
                "key": "created_date",
                "range": {"lte": filters.created_before.isoformat()}
            })
        
        if filters.exclude_ids:
            for exclude_id in filters.exclude_ids:
                conditions.append({
                    "key": "id",
                    "match": {"value": exclude_id}
                })
        
        if not conditions:
            return None
        
        return {"must": conditions} if len(conditions) > 1 else conditions[0]
    
    async def get_by_id(self, doc_id: str) -> Optional[VectorDoc]:
        """Get document by ID."""
        client = self._get_client()
        
        try:
            result = client.retrieve(
                collection_name=self.collection_name,
                ids=[doc_id],
                with_payload=True,
                with_vectors=True
            )
            
            if result:
                point = result[0]
                payload = point.payload or {}
                
                # Extract metadata (all fields except text)
                metadata = {k: v for k, v in payload.items() if k != "text"}
                
                return VectorDoc(
                    id=str(point.id),
                    text=payload.get("text", ""),
                    vector=point.vector,
                    metadata=metadata
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {doc_id}: {e}")
            return None
    
    async def delete(self, doc_id: str) -> None:
        """Delete document by ID."""
        client = self._get_client()
        
        try:
            client.delete(
                collection_name=self.collection_name,
                points_selector=self._models.PointIdsList(
                    points=[doc_id]
                )
            )
            
            logger.debug(f"Deleted document {doc_id} from Qdrant")
            
        except Exception as e:
            logger.error(f"Error deleting document {doc_id}: {e}")
            raise
    
    async def delete_batch(self, doc_ids: List[str]) -> None:
        """Delete multiple documents by ID."""
        if not doc_ids:
            return
        
        client = self._get_client()
        
        try:
            client.delete(
                collection_name=self.collection_name,
                points_selector=self._models.PointIdsList(
                    points=doc_ids
                )
            )
            
            logger.info(f"Deleted {len(doc_ids)} documents from Qdrant")
            
        except Exception as e:
            logger.error(f"Error deleting batch of {len(doc_ids)} documents: {e}")
            raise
    
    async def count(self, filters: Optional[SearchFilters] = None) -> int:
        """Count documents matching filters."""
        client = self._get_client()
        
        try:
            count_params = {
                "collection_name": self.collection_name,
                "exact": True
            }
            
            # Add filters if provided
            if filters:
                count_params["count_filter"] = self._build_filter(filters)
            
            result = client.count(**count_params)
            return result.count
            
        except Exception as e:
            logger.error(f"Error counting documents: {e}")
            return 0
    
    async def initialize(self) -> None:
        """Initialize the Qdrant collection."""
        client = self._get_client()
        
        try:
            # Check if collection exists
            try:
                client.get_collection(self.collection_name)
                logger.info(f"Qdrant collection '{self.collection_name}' already exists")
                return
            except Exception:
                # Collection doesn't exist, create it
                pass
            
            # Create collection with vector configuration
            client.create_collection(
                collection_name=self.collection_name,
                vectors_config=self._models.VectorParams(
                    size=1536,  # Adjust based on your embedding model
                    distance=self._models.Distance.COSINE
                )
            )
            
            # Create payload indexes for filtering
            client.create_payload_index(
                collection_name=self.collection_name,
                field_name="project",
                field_schema=self._models.KeywordIndexParams()
            )
            
            client.create_payload_index(
                collection_name=self.collection_name,
                field_name="work_item_type",
                field_schema=self._models.KeywordIndexParams()
            )
            
            client.create_payload_index(
                collection_name=self.collection_name,
                field_name="state",
                field_schema=self._models.KeywordIndexParams()
            )
            
            client.create_payload_index(
                collection_name=self.collection_name,
                field_name="assigned_to",
                field_schema=self._models.KeywordIndexParams()
            )
            
            client.create_payload_index(
                collection_name=self.collection_name,
                field_name="priority",
                field_schema=self._models.IntegerIndexParams()
            )
            
            logger.info(f"Created Qdrant collection '{self.collection_name}'")
            
        except Exception as e:
            logger.error(f"Error initializing Qdrant collection: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of Qdrant."""
        try:
            client = self._get_client()
            
            # Get collection info
            collection_info = client.get_collection(self.collection_name)
            
            # Get document count
            doc_count = await self.count()
            
            return {
                "status": "healthy",
                "backend": "qdrant",
                "url": self.url,
                "collection_name": self.collection_name,
                "document_count": doc_count,
                "vector_size": collection_info.config.params.vectors.size,
                "distance_metric": collection_info.config.params.vectors.distance.value
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "qdrant",
                "error": str(e),
                "url": self.url,
                "collection_name": self.collection_name
            }
