#!/usr/bin/env python3
"""
Comprehensive analysis of work item 752662 - get history, similar items, and assignee info.
No hallucination - only real data from Azure DevOps.
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def get_work_item_details(ado_client, work_item_id):
    """Get complete work item details."""
    print(f"\n📋 Getting Work Item {work_item_id} Details...")
    
    try:
        # Get work item with all fields
        item = await ado_client.get_work_item(work_item_id)
        if not item:
            print(f"❌ Work item {work_item_id} not found")
            return None
        
        fields = item.get('fields', {})
        
        print(f"✅ Work Item {work_item_id} found!")
        print("=" * 80)
        print(f"ID: {item.get('id')}")
        print(f"URL: {item.get('url')}")
        print(f"Title: {fields.get('System.Title', 'N/A')}")
        print(f"Work Item Type: {fields.get('System.WorkItemType', 'N/A')}")
        print(f"State: {fields.get('System.State', 'N/A')}")
        print(f"Reason: {fields.get('System.Reason', 'N/A')}")
        print(f"Area Path: {fields.get('System.AreaPath', 'N/A')}")
        print(f"Iteration Path: {fields.get('System.IterationPath', 'N/A')}")
        print(f"Priority: {fields.get('Microsoft.VSTS.Common.Priority', 'N/A')}")
        print(f"Severity: {fields.get('Microsoft.VSTS.Common.Severity', 'N/A')}")
        print(f"Created Date: {fields.get('System.CreatedDate', 'N/A')}")
        print(f"Created By: {fields.get('System.CreatedBy', {}).get('displayName', 'N/A')}")
        print(f"Changed Date: {fields.get('System.ChangedDate', 'N/A')}")
        print(f"Changed By: {fields.get('System.ChangedBy', {}).get('displayName', 'N/A')}")
        
        # Assignee information
        assigned_to = fields.get('System.AssignedTo', {})
        if assigned_to:
            print(f"\n👤 ASSIGNEE INFORMATION:")
            print(f"   Display Name: {assigned_to.get('displayName', 'N/A')}")
            print(f"   Unique Name: {assigned_to.get('uniqueName', 'N/A')}")
            print(f"   Email: {assigned_to.get('uniqueName', 'N/A')}")  # uniqueName is usually email
        else:
            print(f"\n👤 ASSIGNEE: Unassigned")
        
        # Description
        description = fields.get('System.Description', '')
        if description:
            print(f"\n📝 DESCRIPTION:")
            print(f"{description}")
        
        # Tags
        tags = fields.get('System.Tags', '')
        if tags:
            print(f"\n🏷️ TAGS: {tags}")
        
        return item
        
    except Exception as e:
        print(f"❌ Error getting work item details: {e}")
        return None


async def get_work_item_history(ado_client, work_item_id):
    """Get work item revision history."""
    print(f"\n📚 Getting Work Item {work_item_id} History...")
    
    try:
        config = get_config()
        # Use the ADO client's session to make direct API calls for revisions
        url = f"https://dev.azure.com/{config.ADO_ORGANIZATION}/{config.ADO_PROJECT}/_apis/wit/workitems/{work_item_id}/revisions?api-version=7.0"
        
        async with ado_client.session.get(url) as response:
            if response.status == 200:
                data = await response.json()
                revisions = data.get('value', [])
                
                print(f"✅ Found {len(revisions)} revisions")
                print("=" * 80)
                
                for i, revision in enumerate(revisions):
                    rev_fields = revision.get('fields', {})
                    rev_num = revision.get('rev', i + 1)
                    
                    print(f"\n📝 REVISION {rev_num}:")
                    print(f"   Date: {rev_fields.get('System.ChangedDate', 'N/A')}")
                    print(f"   Changed By: {rev_fields.get('System.ChangedBy', {}).get('displayName', 'N/A')}")
                    print(f"   State: {rev_fields.get('System.State', 'N/A')}")
                    print(f"   Reason: {rev_fields.get('System.Reason', 'N/A')}")
                    
                    assigned_to = rev_fields.get('System.AssignedTo', {})
                    if assigned_to:
                        print(f"   Assigned To: {assigned_to.get('displayName', 'N/A')}")
                    else:
                        print(f"   Assigned To: Unassigned")
                    
                    # Show title if it changed
                    title = rev_fields.get('System.Title', '')
                    if title:
                        print(f"   Title: {title}")
                
                return revisions
            else:
                print(f"❌ Failed to get revisions: HTTP {response.status}")
                return []
                
    except Exception as e:
        print(f"❌ Error getting work item history: {e}")
        return []


async def find_similar_work_items(ado_client, base_item, search_range=1000):
    """Find similar work items based on title, assignee, and other criteria."""
    print(f"\n🔍 Finding Similar Work Items...")
    
    if not base_item:
        return []
    
    base_fields = base_item.get('fields', {})
    base_title = base_fields.get('System.Title', '').lower()
    base_assignee = base_fields.get('System.AssignedTo', {}).get('displayName', '')
    base_type = base_fields.get('System.WorkItemType', '')
    base_iteration = base_fields.get('System.IterationPath', '')
    base_area = base_fields.get('System.AreaPath', '')
    
    # Extract key terms from title for similarity matching
    title_keywords = []
    if 'irops' in base_title:
        title_keywords.append('irops')
    if 'b-' in base_title:
        # Extract bug number
        import re
        bug_match = re.search(r'b-(\d+)', base_title)
        if bug_match:
            title_keywords.append(f'b-{bug_match.group(1)}')
    if 'error' in base_title:
        title_keywords.append('error')
    
    print(f"   Base item: {base_item.get('id')} - {base_title[:50]}")
    print(f"   Assignee: {base_assignee}")
    print(f"   Keywords: {title_keywords}")
    print(f"   Iteration: {base_iteration}")
    
    similar_items = []
    base_id = base_item.get('id')
    
    # Search in a range around the base item
    start_id = max(1, base_id - search_range)
    end_id = base_id + search_range
    
    print(f"   Searching range {start_id} to {end_id}...")
    
    checked_count = 0
    for work_item_id in range(start_id, end_id + 1):
        if work_item_id == base_id:
            continue  # Skip the base item itself
            
        try:
            item = await ado_client.get_work_item(work_item_id)
            if item:
                checked_count += 1
                fields = item.get('fields', {})
                title = fields.get('System.Title', '').lower()
                assignee = fields.get('System.AssignedTo', {}).get('displayName', '')
                work_type = fields.get('System.WorkItemType', '')
                iteration = fields.get('System.IterationPath', '')
                area = fields.get('System.AreaPath', '')
                
                similarity_score = 0
                similarity_reasons = []
                
                # Check for exact assignee match
                if assignee and assignee == base_assignee:
                    similarity_score += 3
                    similarity_reasons.append(f"Same assignee: {assignee}")
                
                # Check for same iteration
                if iteration == base_iteration:
                    similarity_score += 2
                    similarity_reasons.append(f"Same iteration: {iteration}")
                
                # Check for same work item type
                if work_type == base_type:
                    similarity_score += 1
                    similarity_reasons.append(f"Same type: {work_type}")
                
                # Check for title keyword matches
                for keyword in title_keywords:
                    if keyword in title:
                        similarity_score += 2
                        similarity_reasons.append(f"Title contains: {keyword}")
                
                # Check for same area
                if area == base_area:
                    similarity_score += 1
                    similarity_reasons.append(f"Same area: {area}")
                
                # If similarity score is high enough, consider it similar
                if similarity_score >= 2:
                    similar_items.append({
                        'item': item,
                        'score': similarity_score,
                        'reasons': similarity_reasons
                    })
                    print(f"   ✅ Similar: {work_item_id} (score: {similarity_score}) - {title[:40]}")
                    for reason in similarity_reasons:
                        print(f"      • {reason}")
                
                # Progress indicator
                if checked_count % 100 == 0:
                    print(f"   Checked {checked_count} items, found {len(similar_items)} similar...")
                    
        except Exception:
            continue
    
    # Sort by similarity score
    similar_items.sort(key=lambda x: x['score'], reverse=True)
    
    print(f"\n✅ Found {len(similar_items)} similar work items")
    return similar_items


async def analyze_assignee_workload(ado_client, assignee_name, search_range=2000):
    """Analyze the workload of the assignee."""
    print(f"\n👤 Analyzing Assignee Workload: {assignee_name}")
    
    if not assignee_name or assignee_name == 'Unassigned':
        print("   No assignee to analyze")
        return []
    
    assignee_items = []
    base_id = 752662
    start_id = max(1, base_id - search_range)
    end_id = base_id + search_range
    
    print(f"   Searching range {start_id} to {end_id} for items assigned to {assignee_name}...")
    
    checked_count = 0
    for work_item_id in range(start_id, end_id + 1):
        try:
            item = await ado_client.get_work_item(work_item_id)
            if item:
                checked_count += 1
                fields = item.get('fields', {})
                assigned_to = fields.get('System.AssignedTo', {}).get('displayName', '')
                
                if assigned_to == assignee_name:
                    assignee_items.append(item)
                    work_type = fields.get('System.WorkItemType', 'Unknown')
                    state = fields.get('System.State', 'Unknown')
                    title = fields.get('System.Title', 'No Title')[:50]
                    print(f"   ✅ {work_item_id}: {work_type} ({state}) - {title}")
                
                if checked_count % 200 == 0:
                    print(f"   Checked {checked_count} items, found {len(assignee_items)} assigned to {assignee_name}...")
                    
        except Exception:
            continue
    
    print(f"\n📊 Assignee {assignee_name} has {len(assignee_items)} work items")
    
    if assignee_items:
        # Analyze by state
        states = {}
        types = {}
        iterations = {}
        
        for item in assignee_items:
            fields = item.get('fields', {})
            state = fields.get('System.State', 'Unknown')
            work_type = fields.get('System.WorkItemType', 'Unknown')
            iteration = fields.get('System.IterationPath', 'Unknown')
            
            states[state] = states.get(state, 0) + 1
            types[work_type] = types.get(work_type, 0) + 1
            iterations[iteration] = iterations.get(iteration, 0) + 1
        
        print(f"\n   📊 By State:")
        for state, count in sorted(states.items(), key=lambda x: x[1], reverse=True):
            print(f"      {state}: {count}")
        
        print(f"\n   📊 By Type:")
        for work_type, count in sorted(types.items(), key=lambda x: x[1], reverse=True):
            print(f"      {work_type}: {count}")
        
        print(f"\n   📊 By Iteration:")
        for iteration, count in sorted(iterations.items(), key=lambda x: x[1], reverse=True):
            print(f"      {iteration}: {count}")
    
    return assignee_items


async def export_complete_analysis(work_item, history, similar_items, assignee_items):
    """Export complete analysis to JSON."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"work_item_752662_complete_analysis_{timestamp}.json"
    
    export_data = {
        "timestamp": timestamp,
        "work_item_id": 752662,
        "analysis": {
            "work_item": work_item,
            "history": history,
            "similar_items": [
                {
                    "item": item_data['item'],
                    "similarity_score": item_data['score'],
                    "similarity_reasons": item_data['reasons']
                }
                for item_data in similar_items
            ],
            "assignee_workload": assignee_items
        },
        "summary": {
            "total_revisions": len(history),
            "similar_items_count": len(similar_items),
            "assignee_total_items": len(assignee_items)
        }
    }
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False, default=str)
    
    print(f"\n💾 Complete analysis exported to: {filename}")
    return filename


async def main():
    """Main function."""
    print("🚀 Complete Analysis of Work Item 752662")
    print("=" * 60)
    print("📋 Getting real data from Azure DevOps - no hallucination")
    print("=" * 60)

    try:
        # Get configuration and create client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        print(f"   PAT Token: {'SET' if config.ADO_PAT_TOKEN else 'NOT SET'}")

        ado_client = AdoClient(config)
        print("✅ ADO Client created successfully")
        
        # 1. Get work item details
        work_item = await get_work_item_details(ado_client, 752662)
        
        if not work_item:
            print("❌ Cannot proceed without work item data")
            return
        
        # 2. Get work item history
        history = await get_work_item_history(ado_client, 752662)
        
        # 3. Find similar work items
        similar_items = await find_similar_work_items(ado_client, work_item, search_range=1000)
        
        # 4. Analyze assignee workload
        assignee_name = work_item.get('fields', {}).get('System.AssignedTo', {}).get('displayName', '')
        assignee_items = await analyze_assignee_workload(ado_client, assignee_name, search_range=1500)
        
        # 5. Export complete analysis
        filename = await export_complete_analysis(work_item, history, similar_items, assignee_items)
        
        # Final summary
        print(f"\n🎉 COMPLETE ANALYSIS SUMMARY:")
        print("=" * 60)
        print(f"✅ Work Item 752662 analyzed successfully")
        print(f"📚 History: {len(history)} revisions found")
        print(f"🔍 Similar Items: {len(similar_items)} found")
        print(f"👤 Assignee Workload: {len(assignee_items)} items assigned to {assignee_name}")
        print(f"💾 Complete data exported to: {filename}")
        print(f"🔗 Azure DevOps URL: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/752662")
        
        if similar_items:
            print(f"\n🔍 TOP SIMILAR ITEMS:")
            for item_data in similar_items[:5]:  # Show top 5
                item = item_data['item']
                score = item_data['score']
                fields = item.get('fields', {})
                work_item_id = item.get('id')
                title = fields.get('System.Title', 'No Title')[:60]
                print(f"   • {work_item_id} (score: {score}): {title}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
