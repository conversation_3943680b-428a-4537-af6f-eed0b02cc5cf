{"IsEncrypted": false, "Values": {"AzureWebJobsStorage": "DefaultEndpointsProtocol=https;AccountName=<storage-account>;AccountKey=<key>;EndpointSuffix=core.windows.net", "FUNCTIONS_WORKER_RUNTIME": "python", "FUNCTIONS_EXTENSION_VERSION": "~4", "PYTHON_ISOLATE_WORKER_DEPENDENCIES": "1", "WEBSITE_PYTHON_DEFAULT_VERSION": "3.11", "ADO_ORGANIZATION": "your-organization", "ADO_PROJECT": "your-project", "ADO_PAT_TOKEN": "your-personal-access-token", "AZURE_SEARCH_SERVICE_NAME": "your-search-service", "AZURE_SEARCH_ADMIN_KEY": "your-search-admin-key", "AZURE_SEARCH_INDEX_NAME": "workitems", "VECTOR_STORAGE_TYPE": "local", "SQL_CONNECTION_STRING": "DRIVER={ODBC Driver 17 for SQL Server};SERVER=your-server.database.windows.net;DATABASE=your-database;UID=your-username;PWD=your-password;Encrypt=yes;TrustServerCertificate=no;Connection Timeout=30;", "SQL_DATABASE_TYPE": "azure_sql", "KEY_VAULT_URL": "https://your-keyvault.vault.azure.net/", "TEAMS_WEBHOOK_URL": "https://your-org.webhook.office.com/webhookb2/...", "TEAMS_LOGIC_APP_URL": "https://prod-xx.westus.logic.azure.com:443/workflows/your-workflow-id/triggers/manual/paths/invoke?api-version=2016-10-01&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&sig=your-signature", "EMAIL_LOGIC_APP_URL": "https://prod-xx.uksouth.logic.azure.com:443/workflows/email-workitem-notifications/triggers/Recurrence/paths/invoke", "NOTIFICATION_TIMEOUT_MINUTES": "30", "DEFAULT_TIMEOUT_ASSIGNEE": "<EMAIL>", "TIMEOUT_FOLLOW_UP_ENABLED": "true", "TIMEOUT_AUTO_ASSIGN_ENABLED": "true", "EMBEDDING_PROVIDER": "sentence_transformers", "EMBEDDING_MODEL": "microsoft/E5-large-v2", "OPENAI_API_KEY": "sk-proj-your-openai-key", "AZURE_OPENAI_ENDPOINT": "https://your-openai.openai.azure.com/", "AZURE_OPENAI_API_KEY": "your-azure-openai-key", "AZURE_OPENAI_API_VERSION": "2024-02-01", "LOG_LEVEL": "INFO", "ENVIRONMENT": "development"}, "Host": {"LocalHttpPort": 7071, "CORS": "*", "CORSCredentials": false}}