# Post-Refactor Repository Structure

```
AutoDefectTriage/
├── README.md                                    # Updated with new architecture
├── REFACTOR_SUMMARY.md                         # 📄 NEW: Comprehensive refactor summary
├── POST_REFACTOR_TREE.md                       # 📄 NEW: This file
├── .gitignore
├── requirements.txt                             # Updated with new dependencies
│
├── docs/                                        # Documentation
│   ├── production-safety-bug-748404-only.md    # 🔄 UPDATED: New guardrails
│   ├── architecture.md                         # Architecture overview
│   ├── configuration.md                        # Configuration guide
│   └── deployment.md                           # Deployment instructions
│
├── functions/                                   # Azure Functions
│   ├── host.json                               # Function app configuration
│   ├── local.settings.json                     # 🔄 UPDATED: New settings
│   ├── requirements.txt                        # Python dependencies
│   │
│   └── __app__/                                # Function app code
│       ├── function_app.py                     # 📄 NEW: Main entry point
│       │
│       ├── workitem_event/                     # 📁 NEW: Event-driven handler
│       │   ├── __init__.py                     # 📄 NEW
│       │   └── handler.py                      # 📄 NEW: ADO Service Hook handler
│       │
│       ├── ageing_scan/                        # 📁 NEW: Ageing checks
│       │   ├── __init__.py                     # 📄 NEW
│       │   └── handler.py                      # 📄 NEW: Timer-based ageing scan
│       │
│       ├── workitem_created/                   # Legacy handler (maintained)
│       │   ├── __init__.py
│       │   ├── function.json
│       │   └── handler.py                      # 🔄 UPDATED: Safety guardrails
│       │
│       ├── team_notification_function.py       # 🔄 UPDATED: Configurable work item
│       ├── aging_reminders.py                  # Legacy timer function
│       ├── work_item_check.py                  # Legacy timer function
│       ├── aging_summary_email.py              # Legacy timer function
│       ├── notification_timeout_timer.py       # Legacy timer function
│       ├── backfill_job.py                     # Legacy timer function
│       │
│       └── common/                             # Shared modules
│           ├── __init__.py
│           │
│           ├── utils/                          # Utilities
│           │   ├── __init__.py
│           │   ├── config.py                   # 🔄 UPDATED: Production config system
│           │   ├── logging.py                  # Structured logging
│           │   └── retry.py                    # Retry logic utilities
│           │
│           ├── adapters/                       # External service adapters
│           │   ├── __init__.py
│           │   ├── ado_client.py               # Azure DevOps client
│           │   ├── teams_client.py             # Teams notification client
│           │   └── embedding_service.py        # Embedding generation
│           │
│           ├── vectorstore/                    # 📁 NEW: Vector store abstraction
│           │   ├── __init__.py                 # 📄 NEW: Exports
│           │   ├── base.py                     # 📄 NEW: Protocol and models
│           │   ├── azure_search.py             # 📄 NEW: Azure AI Search impl
│           │   ├── pgvector.py                 # 📄 NEW: PostgreSQL+pgvector impl
│           │   └── qdrant.py                   # 📄 NEW: Qdrant implementation
│           │
│           └── services/                       # 📁 NEW: Business logic
│               ├── __init__.py                 # 📄 NEW
│               ├── store.py                    # 📄 NEW: Idempotency store
│               ├── history.py                  # 📄 NEW: Historical analysis
│               ├── triage.py                   # 📄 NEW: Assignment/priority suggestions
│               ├── ownership.py                # 📄 NEW: Ownership and workload
│               └── notify.py                   # 📄 NEW: Notifications and actions
│
├── scripts/                                    # Utility scripts
│   ├── check-available-workitems.py           # Work item availability check
│   ├── get_all_defects.py                     # Get all defects
│   ├── get_defects_ai_testing.py              # Get AI testing defects
│   ├── simple_ado_test.py                     # Simple ADO connection test
│   ├── test_ado_connection.py                 # ADO connection test
│   ├── find_defects_by_iteration.py           # Find defects by iteration
│   ├── check_specific_workitem.py             # Check specific work item
│   ├── find_all_ai_testing_defects.py         # 🔄 UPDATED: Configurable iteration
│   ├── analyze_workitem_752662.py             # Analyze specific work item
│   ├── send_teams_message_752662.py           # Send Teams message
│   ├── test_team_notification_752662.py       # Test team notification
│   └── Run-StepByStepWorkflow.ps1              # 🔄 UPDATED: Removed hardcoded safety
│
├── tests/                                      # 📁 PLANNED: Test suite
│   ├── __init__.py
│   ├── conftest.py                            # Pytest configuration
│   ├── test_handlers.py                       # Handler tests
│   ├── test_vectorstore.py                    # Vector store tests
│   ├── test_services.py                       # Business logic tests
│   ├── test_config.py                         # Configuration tests
│   └── test_integration.py                    # Integration tests
│
├── infra/                                      # 📁 PLANNED: Infrastructure as Code
│   ├── bicep/                                 # Azure Bicep templates
│   │   ├── main.bicep                         # Main template
│   │   ├── function-app.bicep                 # Function App resources
│   │   ├── storage.bicep                      # Storage resources
│   │   ├── search.bicep                       # Azure Search resources
│   │   └── monitoring.bicep                   # App Insights and monitoring
│   │
│   └── terraform/                             # Alternative Terraform templates
│       ├── main.tf                            # Main configuration
│       ├── variables.tf                       # Variable definitions
│       ├── outputs.tf                         # Output definitions
│       └── modules/                           # Reusable modules
│           ├── function-app/
│           ├── storage/
│           └── monitoring/
│
├── .azure-pipelines/                          # 📁 PLANNED: CI/CD Pipelines
│   ├── ci.yml                                 # Continuous Integration
│   ├── cd.yml                                 # Continuous Deployment
│   ├── pr-validation.yml                      # Pull Request validation
│   └── templates/                             # Pipeline templates
│       ├── build-functions.yml               # Function app build
│       ├── run-tests.yml                     # Test execution
│       └── deploy-infrastructure.yml          # Infrastructure deployment
│
└── .vscode/                                   # VS Code configuration
    ├── settings.json                          # Workspace settings
    ├── launch.json                            # Debug configuration
    └── extensions.json                        # Recommended extensions
```

## Key Changes Summary

### 📄 NEW Files (Created)
- `REFACTOR_SUMMARY.md` - Comprehensive refactor documentation
- `POST_REFACTOR_TREE.md` - This repository structure
- `functions/__app__/function_app.py` - Main Azure Functions entry point
- `functions/__app__/workitem_event/` - Event-driven handler module
- `functions/__app__/ageing_scan/` - Ageing scan module
- `functions/__app__/common/vectorstore/` - Vector store abstraction (4 files)
- `functions/__app__/common/services/` - Business logic modules (5 files)

### 🔄 UPDATED Files (Modified)
- `functions/__app__/common/utils/config.py` - Production configuration system
- `functions/__app__/workitem_created/handler.py` - Safety guardrails
- `functions/__app__/team_notification_function.py` - Configurable work item
- `scripts/find_all_ai_testing_defects.py` - Configurable iteration
- `scripts/Run-StepByStepWorkflow.ps1` - Removed hardcoded safety
- `docs/production-safety-bug-748404-only.md` - New guardrails documentation

### 📁 PLANNED Directories (Ready for Implementation)
- `tests/` - Comprehensive test suite with pytest
- `infra/` - Infrastructure as Code (Bicep/Terraform)
- `.azure-pipelines/` - CI/CD pipelines

## Architecture Overview

### Event-Driven Flow
```
ADO Work Item Created/Updated
    ↓
ADO Service Hook
    ↓
POST /api/workitem_event
    ↓
Event Validation & Idempotency Check
    ↓
Production Safety Guardrails
    ↓
Work Item Processing Pipeline
    ↓
Vector Search & Historical Analysis
    ↓
Triage Recommendations
    ↓
Teams Notification + ADO Comment
```

### Timer-Based Flow
```
Timer Trigger (15 min)
    ↓
/api/ageing_scan
    ↓
WIQL Query for Ageing Items
    ↓
Categorize by Alert Type
    ↓
Deduplication Check
    ↓
Teams Ageing Summary
```

### Vector Store Abstraction
```
VectorStoreFactory
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│  Azure Search   │   PostgreSQL    │     Qdrant      │
│                 │   + pgvector    │                 │
└─────────────────┴─────────────────┴─────────────────┘
```

### Teams Integration Modes
```
Configuration Detection
    ↓
┌─────────────────┬─────────────────┬─────────────────┐
│    Webhook      │   Bot Framework │   Logic App     │
│   (Simple)      │  (Interactive)  │ (Alternative)   │
└─────────────────┴─────────────────┴─────────────────┘
```

## Configuration Hierarchy

### Production Safety (Highest Priority)
1. `READ_ONLY` - Prevents all mutations
2. `ALLOW_PROJECTS` - Project allow-list
3. `SAFETY_ONLY_WORKITEM_ID` - Single work item restriction
4. `TENANT_ID_ALLOWLIST` - Tenant security

### Vector Backend Selection
1. `VECTOR_BACKEND` - azure|pg|qdrant
2. Backend-specific connection settings
3. Fallback to Azure Search if misconfigured

### Teams Notification Mode
1. Bot Framework (if `MICROSOFT_APP_ID` set)
2. Logic App (if `TEAMS_LOGIC_APP_URL` set)
3. Webhook (if `TEAMS_WEBHOOK_URL` set)
4. Disabled (if none configured)

### Idempotency Store
1. Redis (if `REDIS_CONNECTION_STRING` set)
2. Cosmos DB (if `COSMOS_CONNECTION_STRING` set)
3. SQLite (development fallback)

## Deployment Strategy

### Phase 1: Development Testing
```bash
SAFETY_ONLY_WORKITEM_ID="752662"
READ_ONLY="true"
VECTOR_BACKEND="azure"
TEAMS_WEBHOOK_URL="https://..."
```

### Phase 2: Limited Production
```bash
ALLOW_PROJECTS="TestProject"
READ_ONLY="true"
ALLOW_COMMENTS_IN_READ_ONLY="true"
```

### Phase 3: Full Production
```bash
ALLOW_PROJECTS=""  # All projects
READ_ONLY="false"
AUTO_ASSIGN_MIN_CONF="0.85"
REDIS_CONNECTION_STRING="..."
```

## Monitoring Points

### Health Checks
- `/api/health` - Overall system health
- Vector store connectivity
- ADO API accessibility
- Teams webhook/bot status

### Key Metrics
- Event processing duration
- Assignment confidence scores
- Notification success rates
- Error rates by component
- Idempotency hit rates

### Alerts
- High error rates
- Vector store unavailability
- ADO API failures
- Teams notification failures
- Configuration validation errors

## Security Considerations

### Access Control
- Azure Function authentication
- ADO PAT token with minimal permissions
- Teams app registration with limited scopes
- Key Vault for sensitive configuration

### Data Protection
- No sensitive data in logs
- Vector embeddings anonymized
- Work item data encrypted in transit
- Idempotency keys with TTL

### Network Security
- VNet integration for production
- Private endpoints for storage
- Firewall rules for external access
- TLS 1.2+ for all communications

This refactored architecture provides a robust, scalable, and secure foundation for production deployment of the AutoDefectTriage service.
