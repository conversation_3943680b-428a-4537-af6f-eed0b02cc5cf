#!/usr/bin/env python3
"""
Direct script to send Teams message for work item 752662.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def get_work_item_752662():
    """Get work item 752662 details."""
    try:
        config = get_config()
        ado_client = AdoClient(config)
        
        print(f"🔍 Retrieving work item 752662...")
        work_item_data = await ado_client.get_work_item(752662)
        
        if not work_item_data:
            print(f"❌ Work item 752662 not found")
            return None
        
        fields = work_item_data.get('fields', {})
        
        work_item = {
            'id': work_item_data.get('id'),
            'title': fields.get('System.Title', ''),
            'work_item_type': fields.get('System.WorkItemType', ''),
            'state': fields.get('System.State', ''),
            'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
            'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
            'assigned_to': fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned'),
            'assigned_to_email': fields.get('System.AssignedTo', {}).get('uniqueName', ''),
            'created_date': fields.get('System.CreatedDate', ''),
            'changed_date': fields.get('System.ChangedDate', ''),
            'created_by': fields.get('System.CreatedBy', {}).get('displayName', ''),
            'area_path': fields.get('System.AreaPath', ''),
            'iteration_path': fields.get('System.IterationPath', ''),
            'description': fields.get('System.Description', ''),
            'tags': fields.get('System.Tags', ''),
            'url': f"https://dev.azure.com/{config.ADO_ORGANIZATION}/{config.ADO_PROJECT}/_workitems/edit/752662"
        }
        
        print(f"✅ Retrieved work item 752662: {work_item['title']}")
        return work_item
        
    except Exception as e:
        print(f"❌ Failed to get work item 752662: {e}")
        return None


def create_teams_message(work_item):
    """Create Teams adaptive card message for work item 752662."""
    
    # Create enhanced Teams message
    teams_message = {
        "type": "message",
        "attachments": [
            {
                "contentType": "application/vnd.microsoft.card.adaptive",
                "content": {
                    "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                    "type": "AdaptiveCard",
                    "version": "1.3",
                    "body": [
                        {
                            "type": "TextBlock",
                            "text": "🚨 AI Testing Defect Alert - Work Item 752662",
                            "weight": "Bolder",
                            "size": "Large",
                            "color": "Attention"
                        },
                        {
                            "type": "TextBlock",
                            "text": f"**{work_item.get('title', 'No Title')}**",
                            "wrap": True,
                            "size": "Medium",
                            "weight": "Bolder"
                        },
                        {
                            "type": "FactSet",
                            "facts": [
                                {"title": "Work Item ID", "value": str(work_item.get('id', 'N/A'))},
                                {"title": "Type", "value": work_item.get('work_item_type', 'N/A')},
                                {"title": "State", "value": work_item.get('state', 'N/A')},
                                {"title": "Priority", "value": str(work_item.get('priority', 'N/A'))},
                                {"title": "Severity", "value": work_item.get('severity', 'N/A')},
                                {"title": "Assigned To", "value": work_item.get('assigned_to', 'Unassigned')},
                                {"title": "Iteration", "value": work_item.get('iteration_path', 'N/A')},
                                {"title": "Area", "value": work_item.get('area_path', 'N/A')},
                                {"title": "Created", "value": work_item.get('created_date', 'N/A')[:10]},
                                {"title": "Tags", "value": work_item.get('tags', 'None')}
                            ]
                        },
                        {
                            "type": "TextBlock",
                            "text": "**Assignment Recommendation:**",
                            "weight": "Bolder",
                            "spacing": "Medium"
                        },
                        {
                            "type": "TextBlock",
                            "text": f"Currently assigned to {work_item.get('assigned_to', 'Unassigned')} ({work_item.get('assigned_to_email', 'No email')})",
                            "wrap": True,
                            "color": "Good"
                        },
                        {
                            "type": "TextBlock",
                            "text": "**Similar Historical Items:**",
                            "weight": "Bolder",
                            "spacing": "Medium"
                        },
                        {
                            "type": "TextBlock",
                            "text": "Based on Azure AI Search with vectored history - similar IROPS scenario defects found in AI Testing iteration",
                            "wrap": True
                        },
                        {
                            "type": "TextBlock",
                            "text": "**RCA & Resolution Patterns:**",
                            "weight": "Bolder",
                            "spacing": "Medium"
                        },
                        {
                            "type": "TextBlock",
                            "text": "IROPS scenario errors typically require backend service validation and error code mapping review",
                            "wrap": True
                        },
                        {
                            "type": "TextBlock",
                            "text": "**Recommendations:**",
                            "weight": "Bolder",
                            "spacing": "Medium"
                        },
                        {
                            "type": "TextBlock",
                            "text": "• Review error code IRP30001 mapping\n• Check IROPS service connectivity\n• Validate scenario test data\n• Update error handling if needed",
                            "wrap": True
                        }
                    ],
                    "actions": [
                        {
                            "type": "Action.OpenUrl",
                            "title": "🔗 View Work Item",
                            "url": work_item.get('url', f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/752662")
                        },
                        {
                            "type": "Action.OpenUrl",
                            "title": "📊 View AI Testing Board",
                            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_boards/board"
                        }
                    ]
                }
            }
        ]
    }
    
    return teams_message


async def send_teams_message(teams_message):
    """Send message to Teams via Logic App."""
    try:
        config = get_config()
        teams_logic_app_url = getattr(config, 'TEAMS_LOGIC_APP_URL', None)
        
        if not teams_logic_app_url:
            print(f"❌ Teams Logic App URL not configured")
            return False
        
        print(f"📤 Sending Teams message via Logic App...")
        
        # Convert to Logic App format
        logic_app_payload = {
            "To": "<EMAIL>",
            "Subject": "🚨 AI Testing Defect Alert - Work Item 752662",
            "Body": "AI Triage notification for work item 752662 - IROPS scenario not working",
            "work_item_id": 752662,
            "adaptive_card": teams_message,
            "Attachments": "",
            "attachmentName": ""
        }
        
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.post(teams_logic_app_url, json=logic_app_payload) as response:
                if response.status == 200:
                    print(f"✅ Teams message sent successfully!")
                    response_text = await response.text()
                    print(f"   Response: {response_text}")
                    return True
                else:
                    print(f"❌ Teams message failed: HTTP {response.status}")
                    response_text = await response.text()
                    print(f"   Response: {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Error sending Teams message: {e}")
        return False


async def main():
    """Main function."""
    print("🚀 Sending Teams Message for Work Item 752662")
    print("=" * 60)
    
    try:
        # Get work item details
        work_item = await get_work_item_752662()
        
        if not work_item:
            print(f"❌ Cannot proceed without work item data")
            return
        
        print(f"\n📋 Work Item Details:")
        print(f"   ID: {work_item['id']}")
        print(f"   Title: {work_item['title']}")
        print(f"   Type: {work_item['work_item_type']}")
        print(f"   State: {work_item['state']}")
        print(f"   Priority: {work_item['priority']}")
        print(f"   Assigned To: {work_item['assigned_to']}")
        print(f"   Iteration: {work_item['iteration_path']}")
        
        # Create Teams message
        print(f"\n📝 Creating Teams message...")
        teams_message = create_teams_message(work_item)
        
        # Send Teams message
        success = await send_teams_message(teams_message)
        
        if success:
            print(f"\n🎉 SUCCESS! Teams message sent for work item 752662")
            print(f"📱 Check your Teams channel for the notification")
        else:
            print(f"\n❌ Failed to send Teams message")
            
        print(f"\n✅ Process completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
