#!/usr/bin/env python3
"""
Simple script to check if work item 748404 exists and is accessible.
"""

import os
import sys
import asyncio
import logging

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.common.utils.config import get_config
from __app__.common.adapters.ado_client import AdoClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def check_work_item_748404():
    """Check if work item 748404 exists and is accessible."""
    
    print("🔍 Checking Work Item 748404")
    print("=" * 40)
    
    try:
        # Load configuration
        config = get_config()
        
        print(f"ADO Organization: {config.ADO_ORG}")
        print(f"ADO Project: {config.ADO_PROJECT}")
        print(f"ADO PAT Token: {'***' + config.ADO_PAT_TOKEN[-4:] if config.ADO_PAT_TOKEN else 'Not set'}")
        
        # Create ADO client
        ado_client = AdoClient(config)
        
        # Try to fetch work item 748404
        print(f"\n🔍 Fetching work item 748404...")
        work_item = await ado_client.get_work_item(748404)
        
        if work_item:
            print("✅ Work item 748404 found!")
            fields = work_item.get('fields', {})
            print(f"   Title: {fields.get('System.Title', 'Unknown')}")
            print(f"   State: {fields.get('System.State', 'Unknown')}")
            print(f"   Type: {fields.get('System.WorkItemType', 'Unknown')}")
            print(f"   Assigned To: {fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')}")
            print(f"   Priority: {fields.get('Microsoft.VSTS.Common.Priority', 'Unknown')}")
            print(f"   Project: {fields.get('System.TeamProject', 'Unknown')}")
            print(f"   Area Path: {fields.get('System.AreaPath', 'Unknown')}")
            print(f"   Created Date: {fields.get('System.CreatedDate', 'Unknown')}")
            
            return True
        else:
            print("❌ Work item 748404 not found or not accessible")
            
            # Try to search for work items in the project to see what's available
            print("\n🔍 Searching for available work items in the project...")
            
            # Try a simple query to see if we can access any work items
            try:
                wiql_query = """
                SELECT [System.Id], [System.Title], [System.State]
                FROM WorkItems
                WHERE [System.TeamProject] = @project
                ORDER BY [System.Id] DESC
                """
                
                query_result = await ado_client.query_work_items(wiql_query)
                work_items = query_result.get('workItems', [])
                
                if work_items:
                    print(f"✅ Found {len(work_items)} work items in the project")
                    print("   Recent work items:")
                    for item in work_items[:5]:
                        item_id = item.get('id')
                        print(f"   - Work Item {item_id}")
                else:
                    print("❌ No work items found in the project")
                    
            except Exception as e:
                print(f"❌ Error querying work items: {e}")
            
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Failed to check work item")
        return False


async def check_specific_work_items():
    """Check some specific work items that we know exist."""
    
    print("\n🔍 Checking Known Work Items")
    print("=" * 40)
    
    # Work items we've tested before
    known_work_items = [752662, 748404, 750000, 760000]
    
    config = get_config()
    ado_client = AdoClient(config)
    
    found_items = []
    
    for work_item_id in known_work_items:
        try:
            print(f"Checking work item {work_item_id}...")
            work_item = await ado_client.get_work_item(work_item_id)
            
            if work_item:
                title = work_item.get('fields', {}).get('System.Title', 'Unknown')
                state = work_item.get('fields', {}).get('System.State', 'Unknown')
                print(f"   ✅ {work_item_id}: {title} ({state})")
                found_items.append(work_item_id)
            else:
                print(f"   ❌ {work_item_id}: Not found")
                
        except Exception as e:
            print(f"   ❌ {work_item_id}: Error - {e}")
    
    print(f"\n📊 Summary: Found {len(found_items)} out of {len(known_work_items)} work items")
    if found_items:
        print(f"   Available work items: {found_items}")
        return found_items[0]  # Return the first available work item
    
    return None


async def main():
    """Main function."""
    
    # First check if 748404 exists
    exists = await check_work_item_748404()
    
    if not exists:
        # If 748404 doesn't exist, find an alternative work item
        alternative = await check_specific_work_items()
        
        if alternative:
            print(f"\n💡 Suggestion: Use work item {alternative} for testing instead of 748404")
            
            # Update the configuration suggestion
            print(f"\n📝 Update your local.settings.json:")
            print(f'   "SAFETY_ONLY_WORKITEM_ID": "{alternative}",')
        else:
            print("\n❌ No accessible work items found. Please check:")
            print("   1. ADO PAT token permissions")
            print("   2. Project name is correct")
            print("   3. Work items exist in the specified project")
    
    return exists


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
