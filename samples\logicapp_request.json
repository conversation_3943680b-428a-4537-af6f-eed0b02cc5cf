{"To": "<EMAIL>", "Subject": "[#12345] Test Bug - Login not working", "Body": "Work Item: 12345\nTitle: Test Bug - Login not working\nType: Bug\nState: New\nRecommended Priority: 2 (Confidence: 0.75)\nRationale: Based on similar resolved issues with login functionality\n\nSimilar Items Found: 3\n  1. #11111 - Previous login authentication issue...\n  2. #22222 - User authentication failure in prod...\n  3. #33333 - Login timeout causing user lockout...", "work_item_id": 12345, "adaptive_card": {"type": "AdaptiveCard", "version": "1.3", "body": [{"type": "TextBlock", "text": "🐛 New Work Item Assignment", "weight": "Bolder", "size": "Medium", "color": "Attention"}, {"type": "FactSet", "facts": [{"title": "Work Item ID:", "value": "#12345"}, {"title": "Title:", "value": "Test Bug - <PERSON><PERSON> not working"}, {"title": "Type:", "value": "Bug"}, {"title": "State:", "value": "New"}, {"title": "Recommended Priority:", "value": "2 (High Confidence: 75%)"}]}, {"type": "TextBlock", "text": "**Rationale:** Based on similar resolved issues with login functionality", "wrap": true, "spacing": "Medium"}, {"type": "TextBlock", "text": "**Similar Items Found:**", "weight": "Bolder", "spacing": "Medium"}, {"type": "TextBlock", "text": "• #11111 - Previous login authentication issue\n• #22222 - User authentication failure in prod\n• #33333 - Login timeout causing user lockout", "wrap": true, "spacing": "Small"}], "actions": [{"type": "Action.OpenUrl", "title": "View Work Item", "url": "https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/12345"}, {"type": "Action.Submit", "title": "Accept Assignment", "data": {"action": "accept_assignment", "work_item_id": 12345, "assignee": "<EMAIL>"}}, {"type": "Action.Submit", "title": "Reassign", "data": {"action": "reassign", "work_item_id": 12345}}, {"type": "Action.Submit", "title": "Update Priority", "data": {"action": "update_priority", "work_item_id": 12345, "recommended_priority": 2}}]}}