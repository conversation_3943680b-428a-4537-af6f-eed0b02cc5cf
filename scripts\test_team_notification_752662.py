#!/usr/bin/env python3
"""
Test script to trigger team notification for hardcoded work item 752662.
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported basic modules")

    # Try to import the team notification service
    try:
        from __app__.team_notification_function import TeamNotificationService
        print("✅ Successfully imported TeamNotificationService")
    except ImportError as e:
        print(f"⚠️ Could not import TeamNotificationService: {e}")
        print("Will create a simplified version for testing")
        TeamNotificationService = None

except ImportError as e:
    print(f"❌ Failed to import basic modules: {e}")
    sys.exit(1)


async def test_team_notification():
    """Test team notification for work item 752662."""
    print("🚀 Testing Team Notification for Work Item 752662")
    print("=" * 60)
    
    try:
        # Get configuration
        config = get_config()
        print(f"🔗 Configuration loaded:")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        print(f"   Teams Webhook: {'SET' if hasattr(config, 'TEAMS_WEBHOOK_URL') and config.TEAMS_WEBHOOK_URL else 'NOT SET'}")
        
        # Initialize clients
        print(f"\n🔧 Initializing clients...")
        ado_client = AdoClient(config)
        
        # Initialize vector storage and embedding service
        vector_storage = VectorStorageFactory.create_vector_storage(config)
        embedding_service = EmbeddingService(config)
        
        # Initialize vector storage
        await VectorStorageFactory.initialize_storage(vector_storage)
        
        print(f"✅ Clients initialized successfully")
        
        # Create team notification service
        notification_service = TeamNotificationService(
            ado_client=ado_client,
            vector_storage=vector_storage,
            embedding_service=embedding_service
        )
        
        print(f"\n📋 Testing hardcoded work item retrieval...")
        
        # Test getting the hardcoded work item
        hardcoded_item = await notification_service.get_hardcoded_work_item_752662()
        
        if hardcoded_item:
            print(f"✅ Successfully retrieved work item 752662:")
            print(f"   ID: {hardcoded_item.get('id')}")
            print(f"   Title: {hardcoded_item.get('title')}")
            print(f"   Type: {hardcoded_item.get('work_item_type')}")
            print(f"   State: {hardcoded_item.get('state')}")
            print(f"   Priority: {hardcoded_item.get('priority')}")
            print(f"   Assigned To: {hardcoded_item.get('assigned_to')}")
            print(f"   Iteration: {hardcoded_item.get('iteration_path')}")
        else:
            print(f"❌ Failed to retrieve hardcoded work item 752662")
            return
        
        print(f"\n🔍 Running team notification process...")
        
        # Run the team notification process (which will use the hardcoded item)
        result = await notification_service.process_team_notifications(days_back=1)
        
        print(f"\n📊 Team Notification Results:")
        print(f"   Status: {result.get('status', 'Unknown')}")
        print(f"   Message: {result.get('message', 'No message')}")
        print(f"   Items Processed: {result.get('items_processed', 0)}")
        print(f"   Teams Notification Sent: {result.get('teams_notification_sent', False)}")
        
        if result.get('status') == 'success':
            print(f"\n🎉 SUCCESS! Team notification sent for work item 752662")
            
            # Show the Teams message that was generated
            if 'teams_message' in result:
                print(f"\n📱 Teams Message Preview:")
                teams_message = result['teams_message']
                if 'attachments' in teams_message and teams_message['attachments']:
                    card_content = teams_message['attachments'][0].get('content', {})
                    if 'body' in card_content:
                        for body_item in card_content['body']:
                            if body_item.get('type') == 'TextBlock':
                                print(f"   📝 {body_item.get('text', '')}")
        else:
            print(f"\n❌ Team notification failed")
            
    except Exception as e:
        print(f"❌ Error during team notification test: {e}")
        import traceback
        traceback.print_exc()


async def test_teams_webhook():
    """Test Teams webhook connectivity."""
    print(f"\n🔗 Testing Teams Webhook Connectivity...")
    
    try:
        config = get_config()
        teams_webhook_url = getattr(config, 'TEAMS_WEBHOOK_URL', None)
        
        if not teams_webhook_url:
            print(f"⚠️ Teams webhook URL not configured in settings")
            return False
        
        # Test with a simple message
        test_message = {
            "type": "message",
            "text": "🧪 Test message from AutoDefectTriage - Work Item 752662 notification system"
        }
        
        import aiohttp
        async with aiohttp.ClientSession() as session:
            async with session.post(teams_webhook_url, json=test_message) as response:
                if response.status == 200:
                    print(f"✅ Teams webhook test successful!")
                    return True
                else:
                    print(f"❌ Teams webhook test failed: HTTP {response.status}")
                    response_text = await response.text()
                    print(f"   Response: {response_text}")
                    return False
                    
    except Exception as e:
        print(f"❌ Teams webhook test error: {e}")
        return False


async def main():
    """Main function."""
    print("🚀 Team Notification Test for Work Item 752662")
    print("=" * 60)
    
    # Test Teams webhook first
    webhook_ok = await test_teams_webhook()
    
    if not webhook_ok:
        print(f"\n⚠️ Teams webhook test failed, but continuing with notification test...")
    
    # Test team notification
    await test_team_notification()
    
    print(f"\n✅ Test completed at {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")


if __name__ == "__main__":
    asyncio.run(main())
