"""
Ownership and Area Path Management Service
Provides ownership mapping and load balancing for work item assignment.
"""

import logging
from typing import List, Dict, Any, Optional, Set
from datetime import datetime, timedelta
from collections import defaultdict

from ..adapters.ado_client import AdoClient
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class OwnershipService:
    """Service for managing work item ownership and load balancing."""
    
    def __init__(self, ado_client: AdoClient, config=None):
        self.ado_client = ado_client
        self.config = config or get_config()
        self._ownership_cache = {}
        self._cache_expiry = {}
        self._cache_ttl = 3600  # 1 hour cache
    
    async def get_area_owners(self, area_path: str, project: str) -> List[str]:
        """
        Get owners for a specific area path.
        
        Args:
            area_path: Area path to look up owners for
            project: Project name
            
        Returns:
            List of owner usernames/emails
        """
        try:
            cache_key = f"{project}:{area_path}"
            
            # Check cache first
            if self._is_cache_valid(cache_key):
                return self._ownership_cache[cache_key]
            
            # Try multiple ownership sources
            owners = []
            
            # 1. Try CODEOWNERS file approach
            codeowners = await self._get_codeowners_mapping(project)
            if codeowners:
                owners.extend(self._match_codeowners(area_path, codeowners))
            
            # 2. Try area path configuration
            area_config = await self._get_area_configuration(area_path, project)
            if area_config:
                owners.extend(area_config)
            
            # 3. Try team membership lookup
            team_owners = await self._get_team_owners(area_path, project)
            if team_owners:
                owners.extend(team_owners)
            
            # Remove duplicates and cache result
            unique_owners = list(set(owners))
            self._cache_result(cache_key, unique_owners)
            
            logger.debug(
                f"Found {len(unique_owners)} owners for area path {area_path}",
                extra={"area_path": area_path, "project": project, "owners": unique_owners}
            )
            
            return unique_owners
            
        except Exception as e:
            logger.error(f"Error getting area owners: {e}")
            return []
    
    async def get_current_workloads(
        self,
        assignees: List[str],
        project: str,
        include_types: Optional[List[str]] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get current workload information for assignees.
        
        Args:
            assignees: List of assignee names to check
            project: Project name
            include_types: Work item types to include (default: all)
            
        Returns:
            Dictionary mapping assignee to workload information
        """
        try:
            workloads = {}
            
            for assignee in assignees:
                workload_data = await self._calculate_assignee_workload(
                    assignee, project, include_types
                )
                workloads[assignee] = workload_data
            
            return workloads
            
        except Exception as e:
            logger.error(f"Error getting current workloads: {e}")
            return {assignee: self._default_workload() for assignee in assignees}
    
    async def calculate_load_scores(
        self,
        assignees: List[str],
        project: str
    ) -> Dict[str, float]:
        """
        Calculate load scores for assignees (0.0 = no load, 1.0 = overloaded).
        
        Args:
            assignees: List of assignee names
            project: Project name
            
        Returns:
            Dictionary mapping assignee to load score
        """
        try:
            workloads = await self.get_current_workloads(assignees, project)
            load_scores = {}
            
            # Define load thresholds
            optimal_load = 5  # Optimal number of active items
            max_load = 15     # Maximum before overload
            
            for assignee, workload_data in workloads.items():
                active_count = workload_data.get('active_items', 0)
                
                if active_count <= optimal_load:
                    # Under or at optimal load
                    load_score = active_count / optimal_load * 0.5
                else:
                    # Above optimal load
                    excess = active_count - optimal_load
                    excess_ratio = excess / (max_load - optimal_load)
                    load_score = 0.5 + (excess_ratio * 0.5)
                
                # Cap at 1.0
                load_scores[assignee] = min(load_score, 1.0)
            
            return load_scores
            
        except Exception as e:
            logger.error(f"Error calculating load scores: {e}")
            return {assignee: 0.5 for assignee in assignees}  # Default moderate load
    
    async def get_expertise_scores(
        self,
        assignees: List[str],
        work_item: Dict[str, Any],
        project: str
    ) -> Dict[str, float]:
        """
        Calculate expertise scores based on historical work and area knowledge.
        
        Args:
            assignees: List of assignee names
            work_item: Current work item
            project: Project name
            
        Returns:
            Dictionary mapping assignee to expertise score (0.0-1.0)
        """
        try:
            area_path = work_item.get('fields', {}).get('System.AreaPath', '')
            work_item_type = work_item.get('fields', {}).get('System.WorkItemType', '')
            
            expertise_scores = {}
            
            for assignee in assignees:
                score = 0.0
                
                # Area path expertise
                area_owners = await self.get_area_owners(area_path, project)
                if assignee in area_owners:
                    score += 0.4
                
                # Work item type expertise (simplified)
                type_expertise = await self._get_type_expertise(assignee, work_item_type, project)
                score += type_expertise * 0.3
                
                # Recent activity in area
                recent_activity = await self._get_recent_area_activity(assignee, area_path, project)
                score += recent_activity * 0.3
                
                expertise_scores[assignee] = min(score, 1.0)
            
            return expertise_scores
            
        except Exception as e:
            logger.error(f"Error calculating expertise scores: {e}")
            return {assignee: 0.5 for assignee in assignees}  # Default moderate expertise
    
    async def _get_codeowners_mapping(self, project: str) -> Dict[str, List[str]]:
        """Get CODEOWNERS file mapping (simplified implementation)."""
        # TODO: Implement actual CODEOWNERS file parsing
        # This would typically read from a Git repository or configuration
        return {}
    
    def _match_codeowners(self, area_path: str, codeowners: Dict[str, List[str]]) -> List[str]:
        """Match area path to CODEOWNERS patterns."""
        # TODO: Implement pattern matching for CODEOWNERS
        return []
    
    async def _get_area_configuration(self, area_path: str, project: str) -> List[str]:
        """Get area configuration from ADO project settings."""
        try:
            # TODO: Implement ADO area configuration lookup
            # This would query the project's area path configuration
            return []
            
        except Exception as e:
            logger.warning(f"Error getting area configuration: {e}")
            return []
    
    async def _get_team_owners(self, area_path: str, project: str) -> List[str]:
        """Get team owners based on area path."""
        try:
            # TODO: Implement team membership lookup
            # This would query ADO teams and their area path assignments
            return []
            
        except Exception as e:
            logger.warning(f"Error getting team owners: {e}")
            return []
    
    async def _calculate_assignee_workload(
        self,
        assignee: str,
        project: str,
        include_types: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """Calculate detailed workload for an assignee."""
        try:
            # Build WIQL query for active items
            type_filter = ""
            if include_types:
                type_list = "', '".join(include_types)
                type_filter = f"AND [System.WorkItemType] IN ('{type_list}')"
            
            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.State], [System.Priority], [System.CreatedDate]
            FROM WorkItems
            WHERE [System.AssignedTo] = '{assignee}'
            AND [System.State] NOT IN ('Closed', 'Resolved', 'Done', 'Completed')
            {type_filter}
            ORDER BY [System.Priority] ASC, [System.CreatedDate] ASC
            """
            
            # Execute query
            try:
                query_result = await self.ado_client.query_work_items(wiql_query)
                work_items = query_result.get('workItems', [])
            except Exception as e:
                logger.warning(f"WIQL query failed for workload calculation: {e}")
                # Fallback to estimated workload
                return self._estimate_workload(assignee)
            
            # Analyze workload
            active_items = len(work_items)
            priority_breakdown = defaultdict(int)
            age_breakdown = {"new": 0, "medium": 0, "old": 0}
            
            now = datetime.utcnow()
            
            for item in work_items:
                # Get full work item details
                work_item_id = item.get('id')
                if work_item_id:
                    full_item = await self.ado_client.get_work_item(work_item_id)
                    if full_item:
                        fields = full_item.get('fields', {})
                        
                        # Priority breakdown
                        priority = fields.get('Microsoft.VSTS.Common.Priority', 3)
                        priority_breakdown[priority] += 1
                        
                        # Age breakdown
                        created_date_str = fields.get('System.CreatedDate', '')
                        if created_date_str:
                            try:
                                created_date = datetime.fromisoformat(created_date_str.replace('Z', '+00:00'))
                                age_days = (now - created_date).days
                                
                                if age_days <= 7:
                                    age_breakdown["new"] += 1
                                elif age_days <= 30:
                                    age_breakdown["medium"] += 1
                                else:
                                    age_breakdown["old"] += 1
                            except (ValueError, TypeError):
                                age_breakdown["medium"] += 1
            
            return {
                "active_items": active_items,
                "priority_breakdown": dict(priority_breakdown),
                "age_breakdown": age_breakdown,
                "load_category": self._categorize_load(active_items),
                "last_updated": now.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error calculating workload for {assignee}: {e}")
            return self._default_workload()
    
    def _estimate_workload(self, assignee: str) -> Dict[str, Any]:
        """Provide estimated workload when query fails."""
        # Simple estimation - could be enhanced with caching or other data sources
        estimated_load = 5  # Assume moderate load
        
        return {
            "active_items": estimated_load,
            "priority_breakdown": {2: 1, 3: 3, 4: 1},
            "age_breakdown": {"new": 2, "medium": 2, "old": 1},
            "load_category": self._categorize_load(estimated_load),
            "estimated": True,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    def _default_workload(self) -> Dict[str, Any]:
        """Default workload structure."""
        return {
            "active_items": 0,
            "priority_breakdown": {},
            "age_breakdown": {"new": 0, "medium": 0, "old": 0},
            "load_category": "unknown",
            "error": True,
            "last_updated": datetime.utcnow().isoformat()
        }
    
    def _categorize_load(self, active_items: int) -> str:
        """Categorize load level."""
        if active_items == 0:
            return "none"
        elif active_items <= 3:
            return "light"
        elif active_items <= 7:
            return "moderate"
        elif active_items <= 12:
            return "heavy"
        else:
            return "overloaded"
    
    async def _get_type_expertise(self, assignee: str, work_item_type: str, project: str) -> float:
        """Get expertise score for work item type (simplified)."""
        # TODO: Implement historical analysis of work item type handling
        # For now, return moderate expertise
        return 0.5
    
    async def _get_recent_area_activity(self, assignee: str, area_path: str, project: str) -> float:
        """Get recent activity score in area path (simplified)."""
        # TODO: Implement recent activity analysis
        # For now, return moderate activity
        return 0.3
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cache entry is still valid."""
        if cache_key not in self._cache_expiry:
            return False
        
        return datetime.utcnow() < self._cache_expiry[cache_key]
    
    def _cache_result(self, cache_key: str, result: Any) -> None:
        """Cache a result with TTL."""
        self._ownership_cache[cache_key] = result
        self._cache_expiry[cache_key] = datetime.utcnow() + timedelta(seconds=self._cache_ttl)
    
    async def get_ownership_summary(
        self,
        work_item: Dict[str, Any],
        assignee_candidates: List[str]
    ) -> Dict[str, Any]:
        """
        Get comprehensive ownership summary for a work item.
        
        Args:
            work_item: Current work item
            assignee_candidates: List of potential assignees
            
        Returns:
            Ownership analysis including area owners, workloads, and expertise
        """
        try:
            project = work_item.get('fields', {}).get('System.TeamProject', '')
            area_path = work_item.get('fields', {}).get('System.AreaPath', '')
            
            # Get area owners
            area_owners = await self.get_area_owners(area_path, project)
            
            # Get workloads for candidates
            workloads = await self.get_current_workloads(assignee_candidates, project)
            
            # Get load scores
            load_scores = await self.calculate_load_scores(assignee_candidates, project)
            
            # Get expertise scores
            expertise_scores = await self.get_expertise_scores(assignee_candidates, work_item, project)
            
            return {
                "area_path": area_path,
                "area_owners": area_owners,
                "candidate_analysis": {
                    assignee: {
                        "workload": workloads.get(assignee, {}),
                        "load_score": load_scores.get(assignee, 0.5),
                        "expertise_score": expertise_scores.get(assignee, 0.5),
                        "is_area_owner": assignee in area_owners
                    }
                    for assignee in assignee_candidates
                },
                "recommendations": self._generate_ownership_recommendations(
                    area_owners, assignee_candidates, load_scores, expertise_scores
                ),
                "timestamp": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error getting ownership summary: {e}")
            return {
                "area_path": "",
                "area_owners": [],
                "candidate_analysis": {},
                "recommendations": [],
                "error": str(e)
            }
    
    def _generate_ownership_recommendations(
        self,
        area_owners: List[str],
        candidates: List[str],
        load_scores: Dict[str, float],
        expertise_scores: Dict[str, float]
    ) -> List[str]:
        """Generate ownership-based recommendations."""
        recommendations = []
        
        # Check for area owners in candidates
        area_owner_candidates = [c for c in candidates if c in area_owners]
        if area_owner_candidates:
            recommendations.append(f"Area owners available: {', '.join(area_owner_candidates)}")
        
        # Check for low-load candidates
        low_load_candidates = [c for c in candidates if load_scores.get(c, 0.5) < 0.3]
        if low_load_candidates:
            recommendations.append(f"Low workload candidates: {', '.join(low_load_candidates)}")
        
        # Check for high expertise candidates
        high_expertise_candidates = [c for c in candidates if expertise_scores.get(c, 0.5) > 0.7]
        if high_expertise_candidates:
            recommendations.append(f"High expertise candidates: {', '.join(high_expertise_candidates)}")
        
        return recommendations
