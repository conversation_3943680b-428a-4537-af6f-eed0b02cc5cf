"""
Test script for Teams notification timeout handling.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, Any

# Mock imports for testing
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.common.services.notification_tracking_service import NotificationTrackingService
from __app__.common.utils.config import Config

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockConfig(Config):
    """Mock configuration for testing."""
    
    def __init__(self):
        # Override with test values
        self.NOTIFICATION_TIMEOUT_MINUTES = 1  # 1 minute for testing
        self.DEFAULT_TIMEOUT_ASSIGNEE = "<EMAIL>"
        self.TIMEOUT_FOLLOW_UP_ENABLED = True
        self.TIMEOUT_AUTO_ASSIGN_ENABLED = True
    
    def get(self, key: str, default: Any = None) -> Any:
        return getattr(self, key, default)


async def test_notification_tracking():
    """Test basic notification tracking functionality."""
    print("\n🧪 Testing Notification Tracking...")
    
    config = MockConfig()
    service = NotificationTrackingService(config)
    
    # Test storing notification
    work_item_id = 12345
    notification_id = "test-notification-123"
    
    success = await service.store_notification(
        work_item_id=work_item_id,
        notification_id=notification_id,
        teams_message_id="teams-msg-123",
        status="sent"
    )
    
    assert success, "Failed to store notification"
    print("✅ Notification stored successfully")
    
    # Verify notification is in store
    assert notification_id in service._notification_store
    stored_record = service._notification_store[notification_id]
    assert stored_record["work_item_id"] == work_item_id
    assert stored_record["status"] == "sent"
    print("✅ Notification data verified")


async def test_timeout_detection():
    """Test timeout detection functionality."""
    print("\n🧪 Testing Timeout Detection...")
    
    config = MockConfig()
    service = NotificationTrackingService(config)
    
    # Create a notification that should timeout (sent 2 minutes ago)
    old_timestamp = (datetime.utcnow() - timedelta(minutes=2)).isoformat()
    
    await service.store_notification(
        work_item_id=12346,
        notification_id="timeout-test-123",
        sent_timestamp=old_timestamp,
        status="sent"
    )
    
    # Create a recent notification that should not timeout
    recent_timestamp = datetime.utcnow().isoformat()
    
    await service.store_notification(
        work_item_id=12347,
        notification_id="recent-test-123",
        sent_timestamp=recent_timestamp,
        status="sent"
    )
    
    # Check for timed out notifications
    timed_out = await service.get_timed_out_notifications(timeout_minutes=1)
    
    assert len(timed_out) == 1, f"Expected 1 timed out notification, got {len(timed_out)}"
    assert timed_out[0]["notification_id"] == "timeout-test-123"
    print("✅ Timeout detection working correctly")


async def test_timeout_handling():
    """Test timeout handling functionality."""
    print("\n🧪 Testing Timeout Handling...")
    
    config = MockConfig()
    service = NotificationTrackingService(config)
    
    # Create a timed out notification
    old_timestamp = (datetime.utcnow() - timedelta(minutes=2)).isoformat()
    notification_id = "handle-timeout-123"
    
    await service.store_notification(
        work_item_id=12348,
        notification_id=notification_id,
        sent_timestamp=old_timestamp,
        status="sent"
    )
    
    # Get the notification record
    notification_record = service._notification_store[notification_id]
    
    # Handle the timeout
    success = await service.handle_notification_timeout(notification_record)
    
    assert success, "Failed to handle timeout"
    
    # Verify the notification was marked as handled
    updated_record = service._notification_store[notification_id]
    assert updated_record["timeout_handled"] == True
    assert updated_record["status"] == "timed_out"
    assert "timeout_handled_at" in updated_record
    
    print("✅ Timeout handling working correctly")


async def test_response_marking():
    """Test marking notifications as responded."""
    print("\n🧪 Testing Response Marking...")
    
    config = MockConfig()
    service = NotificationTrackingService(config)
    
    # Store a notification
    notification_id = "response-test-123"
    await service.store_notification(
        work_item_id=12349,
        notification_id=notification_id,
        status="sent"
    )
    
    # Mark as responded
    success = await service.mark_notification_responded(notification_id)
    
    assert success, "Failed to mark as responded"
    
    # Verify status change
    record = service._notification_store[notification_id]
    assert record["status"] == "responded"
    assert "responded_at" in record
    
    print("✅ Response marking working correctly")


async def test_full_workflow():
    """Test the complete timeout workflow."""
    print("\n🧪 Testing Full Workflow...")
    
    config = MockConfig()
    service = NotificationTrackingService(config)
    
    # Step 1: Store multiple notifications with different timestamps
    notifications = [
        {
            "work_item_id": 12350,
            "notification_id": "workflow-old-1",
            "sent_timestamp": (datetime.utcnow() - timedelta(minutes=5)).isoformat(),
            "status": "sent"
        },
        {
            "work_item_id": 12351,
            "notification_id": "workflow-old-2",
            "sent_timestamp": (datetime.utcnow() - timedelta(minutes=3)).isoformat(),
            "status": "sent"
        },
        {
            "work_item_id": 12352,
            "notification_id": "workflow-recent",
            "sent_timestamp": datetime.utcnow().isoformat(),
            "status": "sent"
        }
    ]
    
    for notif in notifications:
        await service.store_notification(**notif)
    
    print(f"📝 Stored {len(notifications)} notifications")
    
    # Step 2: Check for timeouts
    timed_out = await service.get_timed_out_notifications(timeout_minutes=1)
    print(f"⏰ Found {len(timed_out)} timed out notifications")
    
    assert len(timed_out) == 2, f"Expected 2 timed out notifications, got {len(timed_out)}"
    
    # Step 3: Handle timeouts
    handled_count = 0
    for notification_record in timed_out:
        success = await service.handle_notification_timeout(notification_record)
        if success:
            handled_count += 1
    
    print(f"✅ Handled {handled_count} timeouts")
    assert handled_count == 2, f"Expected to handle 2 timeouts, handled {handled_count}"
    
    # Step 4: Verify no more timeouts (already handled)
    remaining_timeouts = await service.get_timed_out_notifications(timeout_minutes=1)
    assert len(remaining_timeouts) == 0, f"Expected 0 remaining timeouts, got {len(remaining_timeouts)}"
    
    print("✅ Full workflow completed successfully")


def create_test_payload() -> Dict[str, Any]:
    """Create a test payload for API testing."""
    return {
        "work_item_id": 12345,
        "notification_id": f"api-test-{datetime.utcnow().strftime('%Y%m%d%H%M%S')}",
        "teams_message_id": "teams-msg-test-123",
        "sent_timestamp": datetime.utcnow().isoformat(),
        "status": "sent"
    }


async def main():
    """Run all tests."""
    print("🚀 Starting Teams Timeout Handling Tests")
    print("=" * 50)
    
    try:
        await test_notification_tracking()
        await test_timeout_detection()
        await test_timeout_handling()
        await test_response_marking()
        await test_full_workflow()
        
        print("\n" + "=" * 50)
        print("🎉 All tests passed successfully!")
        
        # Print example API payload
        print("\n📋 Example API Test Payload:")
        print(json.dumps(create_test_payload(), indent=2))
        
        print("\n📚 Test Commands:")
        print("# Test notification storage:")
        print("curl -X POST 'https://your-function-app.azurewebsites.net/api/store_notification_tracking' \\")
        print("  -H 'Content-Type: application/json' \\")
        print(f"  -d '{json.dumps(create_test_payload())}'")
        
        print("\n# Test timeout checking:")
        print("curl 'https://your-function-app.azurewebsites.net/api/check_notification_timeouts'")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
