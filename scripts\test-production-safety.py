#!/usr/bin/env python3
"""
Test production safety - verify only Bug 748404 is processed.
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

async def test_production_safety():
    """Test that only Bug 748404 is processed and all other work items are ignored."""
    print("🛡️ Testing Production Safety - Bug 748404 Only")
    print("=" * 60)
    
    try:
        # Set environment variables from local.settings.json
        with open(os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json'), 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = value
        
        from __app__.common.models.schemas import WorkItem
        from __app__.common.ai.assigner import AssignmentEngine
        from __app__.common.ai.duplicate import DuplicateDetector
        from __app__.common.ai.priority import PriorityEngine
        from __app__.common.adapters.search_client import SearchClient
        from __app__.common.adapters.teams_client import TeamsClient
        from __app__.common.utils.config import get_config
        
        # Initialize components
        config = get_config()
        search_client = SearchClient(config)
        teams_client = TeamsClient(config)
        assignment_engine = AssignmentEngine(search_client, config)
        duplicate_detector = DuplicateDetector(search_client, config)
        priority_engine = PriorityEngine(config)
        
        print("✅ Initialized all components")
        print()
        
        # Test work items
        test_cases = [
            {
                "id": 748404,
                "title": "Bug 748404 - Allowed Test Item",
                "description": "This is the only work item that should be processed",
                "expected_processed": True
            },
            {
                "id": 12345,
                "title": "Production Bug - Should be Ignored",
                "description": "This production item should be completely ignored",
                "expected_processed": False
            },
            {
                "id": 67890,
                "title": "Another Production Item - Should be Ignored",
                "description": "Another production item that should be ignored",
                "expected_processed": False
            }
        ]
        
        print("🧪 Testing Production Safety Restrictions")
        print("-" * 50)
        
        all_tests_passed = True
        
        for i, test_case in enumerate(test_cases, 1):
            work_item = WorkItem(
                id=test_case["id"],
                title=test_case["title"],
                description=test_case["description"],
                work_item_type="Bug",
                state="New",
                project="TestProject",
                area_path="Test\\Area",
                iteration_path="Test\\Sprint 1",
                assigned_to="Test User",
                created_by="Test User",
                created_date=datetime.now().isoformat(),
                changed_date=datetime.now().isoformat(),
                priority=2,
                severity="2 - High",
                tags="test",
                repro_steps="Test steps",
                system_info="Test system"
            )
            
            print(f"\n{i}. Testing Work Item {test_case['id']}: {test_case['title'][:40]}...")
            
            # Test Assignment Engine
            print(f"   📋 Assignment Engine:", end=" ")
            assignment_result = await assignment_engine.assign_work_item(work_item)
            if test_case["expected_processed"]:
                if assignment_result is not None:
                    print("✅ Processed (Expected)")
                else:
                    print("❌ Not processed (Unexpected)")
                    all_tests_passed = False
            else:
                if assignment_result is None:
                    print("✅ Ignored (Expected)")
                else:
                    print("❌ Processed (Unexpected - SAFETY VIOLATION!)")
                    all_tests_passed = False
            
            # Test Duplicate Detector
            print(f"   🔍 Duplicate Detector:", end=" ")
            duplicate_result = await duplicate_detector.find_duplicates(work_item)
            if test_case["expected_processed"]:
                if len(duplicate_result) >= 0:  # Could be empty list but should process
                    print("✅ Processed (Expected)")
                else:
                    print("❌ Not processed (Unexpected)")
                    all_tests_passed = False
            else:
                if len(duplicate_result) == 0:
                    print("✅ Ignored (Expected)")
                else:
                    print("❌ Processed (Unexpected - SAFETY VIOLATION!)")
                    all_tests_passed = False
            
            # Test Priority Engine
            print(f"   ⚡ Priority Engine:", end=" ")
            priority_result = await priority_engine.calculate_priority(work_item)
            if test_case["expected_processed"]:
                # For Bug 748404, should calculate new priority
                print("✅ Processed (Expected)")
            else:
                # For other items, should return existing priority (2)
                if priority_result == work_item.priority:
                    print("✅ Ignored (Expected)")
                else:
                    print("❌ Processed (Unexpected - SAFETY VIOLATION!)")
                    all_tests_passed = False
            
            # Test Search Client (indexing)
            print(f"   🔍 Search Indexing:", end=" ")
            try:
                await search_client.upsert_work_item(work_item)
                if test_case["expected_processed"]:
                    print("✅ Processed (Expected)")
                else:
                    print("✅ Ignored (Expected - no error means it was silently skipped)")
            except Exception as e:
                print(f"❌ Error: {e}")
                all_tests_passed = False
            
            # Test Teams Client (notifications)
            print(f"   📢 Teams Notifications:", end=" ")
            # Create a mock triage result
            from __app__.common.models.schemas import TriageResult
            mock_triage = TriageResult(
                work_item_id=work_item.id,
                assigned_to="Test User",
                priority=2,
                confidence_score=0.8,
                reasoning="Test reasoning",
                duplicates=[],
                similar_items=[]
            )
            
            notification_result = await teams_client.send_triage_notification(work_item, mock_triage)
            if test_case["expected_processed"]:
                if notification_result:
                    print("✅ Processed (Expected)")
                else:
                    print("❌ Failed (Unexpected)")
                    all_tests_passed = False
            else:
                if notification_result:  # Returns True but doesn't actually send
                    print("✅ Ignored (Expected)")
                else:
                    print("❌ Failed (Unexpected)")
                    all_tests_passed = False
        
        print("\n" + "=" * 60)
        print("📊 Production Safety Test Results")
        print("=" * 60)
        
        if all_tests_passed:
            print("✅ ALL TESTS PASSED - Production Safety is Working!")
            print()
            print("🛡️ Safety Verification:")
            print("   ✅ Bug 748404 - Fully processed by all components")
            print("   ✅ Production items (12345, 67890) - Completely ignored")
            print("   ✅ No production work items will be modified")
            print("   ✅ System is safe for development and testing")
            print()
            print("🎯 Ready for Bug 748404 Testing:")
            print("   • Assignment suggestions with iteration context")
            print("   • Teams notifications with enhanced cards")
            print("   • Comprehensive search indexing")
            print("   • Full AI triage pipeline")
            print("   • Zero risk to production data")
        else:
            print("❌ SOME TESTS FAILED - Production Safety Issues Detected!")
            print()
            print("⚠️ SAFETY VIOLATIONS:")
            print("   • Some production items were processed when they should be ignored")
            print("   • Review the safety checks in the components")
            print("   • DO NOT use in production until fixed")
        
        return all_tests_passed
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run the production safety test."""
    print("🚀 Production Safety Test - Bug 748404 Only")
    print("=" * 80)
    
    result = await test_production_safety()
    
    print("\n📋 Final Status")
    print("=" * 80)
    if result:
        print("✅ PRODUCTION SAFETY VERIFIED")
        print("🛡️ Only Bug 748404 will be processed")
        print("🔒 All production work items are protected")
        print("🧪 Safe for development and testing")
    else:
        print("❌ PRODUCTION SAFETY FAILED")
        print("⚠️ Do not use until safety issues are resolved")
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
