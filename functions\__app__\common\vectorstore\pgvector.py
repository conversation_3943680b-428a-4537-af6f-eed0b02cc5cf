"""
PostgreSQL + pgvector Vector Store Implementation
Provides vector search capabilities using PostgreSQL with pgvector extension.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base import VectorStore, VectorDoc, SearchHit, SearchFilters, cosine_similarity

logger = logging.getLogger(__name__)


class PgVectorClient:
    """PostgreSQL + pgvector implementation of VectorStore protocol."""
    
    def __init__(self, config):
        self.config = config
        self.connection_string = config.PG_CONN
        self.table_name = "vector_documents"
        self._pool = None
    
    async def _get_pool(self):
        """Get connection pool (lazy initialization)."""
        if self._pool is None:
            try:
                import asyncpg
                self._pool = await asyncpg.create_pool(self.connection_string)
            except ImportError:
                raise ImportError("asyncpg package required for PostgreSQL vector store")
        return self._pool
    
    async def upsert(self, doc: VectorDoc) -> None:
        """Insert or update a document."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                # Convert vector to pgvector format
                vector_str = f"[{','.join(map(str, doc.vector))}]"
                
                await conn.execute("""
                    INSERT INTO vector_documents (id, text, vector, metadata, created_at, updated_at)
                    VALUES ($1, $2, $3::vector, $4, NOW(), NOW())
                    ON CONFLICT (id) 
                    DO UPDATE SET 
                        text = EXCLUDED.text,
                        vector = EXCLUDED.vector,
                        metadata = EXCLUDED.metadata,
                        updated_at = NOW()
                """, doc.id, doc.text, vector_str, json.dumps(doc.metadata))
                
                logger.debug(f"Upserted document {doc.id} to PostgreSQL")
                
            except Exception as e:
                logger.error(f"Error upserting document {doc.id}: {e}")
                raise
    
    async def upsert_batch(self, docs: List[VectorDoc]) -> None:
        """Insert or update multiple documents in batch."""
        if not docs:
            return
        
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                # Prepare batch data
                batch_data = []
                for doc in docs:
                    vector_str = f"[{','.join(map(str, doc.vector))}]"
                    batch_data.append((
                        doc.id, 
                        doc.text, 
                        vector_str, 
                        json.dumps(doc.metadata)
                    ))
                
                # Execute batch upsert
                await conn.executemany("""
                    INSERT INTO vector_documents (id, text, vector, metadata, created_at, updated_at)
                    VALUES ($1, $2, $3::vector, $4, NOW(), NOW())
                    ON CONFLICT (id) 
                    DO UPDATE SET 
                        text = EXCLUDED.text,
                        vector = EXCLUDED.vector,
                        metadata = EXCLUDED.metadata,
                        updated_at = NOW()
                """, batch_data)
                
                logger.info(f"Upserted {len(docs)} documents to PostgreSQL")
                
            except Exception as e:
                logger.error(f"Error upserting batch of {len(docs)} documents: {e}")
                raise
    
    async def search(
        self, 
        query_vector: List[float], 
        k: int = 10,
        filters: Optional[SearchFilters] = None
    ) -> List[SearchHit]:
        """Search using vector similarity."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                # Convert query vector to pgvector format
                query_vector_str = f"[{','.join(map(str, query_vector))}]"
                
                # Build base query
                query = """
                    SELECT id, text, metadata, vector <=> $1::vector as distance
                    FROM vector_documents
                """
                params = [query_vector_str]
                
                # Add filters
                where_clauses = []
                if filters:
                    where_clauses, params = self._build_where_clauses(filters, params)
                
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)
                
                query += " ORDER BY vector <=> $1::vector LIMIT $" + str(len(params) + 1)
                params.append(k)
                
                # Execute query
                rows = await conn.fetch(query, *params)
                
                # Convert to SearchHit objects
                hits = []
                for row in rows:
                    # Convert distance to similarity score (0-1)
                    score = max(0.0, 1.0 - row['distance'])
                    
                    metadata = json.loads(row['metadata']) if row['metadata'] else {}
                    
                    hit = SearchHit(
                        id=row['id'],
                        text=row['text'],
                        score=score,
                        metadata=metadata
                    )
                    hits.append(hit)
                
                logger.debug(f"Vector search returned {len(hits)} results")
                return hits
                
            except Exception as e:
                logger.error(f"Error in vector search: {e}")
                raise
    
    async def search_hybrid(
        self,
        query_text: str,
        query_vector: List[float],
        k: int = 10,
        filters: Optional[SearchFilters] = None,
        alpha: float = 0.5
    ) -> List[SearchHit]:
        """Hybrid search combining text and vector similarity."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                # Convert query vector to pgvector format
                query_vector_str = f"[{','.join(map(str, query_vector))}]"
                
                # Build hybrid query using full-text search and vector similarity
                query = """
                    SELECT 
                        id, 
                        text, 
                        metadata,
                        vector <=> $1::vector as vector_distance,
                        ts_rank(to_tsvector('english', text), plainto_tsquery('english', $2)) as text_score
                    FROM vector_documents
                """
                params = [query_vector_str, query_text]
                
                # Add filters
                where_clauses = []
                if filters:
                    where_clauses, params = self._build_where_clauses(filters, params)
                
                # Add text search condition
                where_clauses.append(f"to_tsvector('english', text) @@ plainto_tsquery('english', ${len(params) - 1})")
                
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)
                
                # Calculate combined score and order
                query += f"""
                    ORDER BY (
                        {1 - alpha} * ts_rank(to_tsvector('english', text), plainto_tsquery('english', $2)) +
                        {alpha} * (1.0 - (vector <=> $1::vector))
                    ) DESC
                    LIMIT ${len(params) + 1}
                """
                params.append(k)
                
                # Execute query
                rows = await conn.fetch(query, *params)
                
                # Convert to SearchHit objects
                hits = []
                for row in rows:
                    # Calculate combined score
                    vector_score = max(0.0, 1.0 - row['vector_distance'])
                    text_score = float(row['text_score']) if row['text_score'] else 0.0
                    combined_score = (1 - alpha) * text_score + alpha * vector_score
                    
                    metadata = json.loads(row['metadata']) if row['metadata'] else {}
                    
                    hit = SearchHit(
                        id=row['id'],
                        text=row['text'],
                        score=combined_score,
                        metadata=metadata
                    )
                    hits.append(hit)
                
                logger.debug(f"Hybrid search returned {len(hits)} results")
                return hits
                
            except Exception as e:
                logger.error(f"Error in hybrid search: {e}")
                raise
    
    def _build_where_clauses(self, filters: SearchFilters, params: List[Any]) -> tuple[List[str], List[Any]]:
        """Build WHERE clauses from SearchFilters."""
        where_clauses = []
        
        if filters.project:
            params.append(filters.project)
            where_clauses.append(f"metadata->>'project' = ${len(params)}")
        
        if filters.work_item_type:
            params.append(filters.work_item_type)
            where_clauses.append(f"metadata->>'work_item_type' = ${len(params)}")
        
        if filters.state:
            params.append(filters.state)
            where_clauses.append(f"metadata->>'state' = ANY(${len(params)})")
        
        if filters.assigned_to:
            params.append(filters.assigned_to)
            where_clauses.append(f"metadata->>'assigned_to' = ${len(params)}")
        
        if filters.priority:
            params.append([str(p) for p in filters.priority])
            where_clauses.append(f"metadata->>'priority' = ANY(${len(params)})")
        
        if filters.created_after:
            params.append(filters.created_after.isoformat())
            where_clauses.append(f"(metadata->>'created_date')::timestamp >= ${len(params)}::timestamp")
        
        if filters.created_before:
            params.append(filters.created_before.isoformat())
            where_clauses.append(f"(metadata->>'created_date')::timestamp <= ${len(params)}::timestamp")
        
        if filters.exclude_ids:
            params.append(filters.exclude_ids)
            where_clauses.append(f"id != ALL(${len(params)})")
        
        return where_clauses, params
    
    async def get_by_id(self, doc_id: str) -> Optional[VectorDoc]:
        """Get document by ID."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                row = await conn.fetchrow(
                    "SELECT id, text, vector, metadata FROM vector_documents WHERE id = $1",
                    doc_id
                )
                
                if row:
                    # Convert vector from string to list
                    vector_str = str(row['vector'])
                    vector = [float(x) for x in vector_str.strip('[]').split(',')]
                    
                    metadata = json.loads(row['metadata']) if row['metadata'] else {}
                    
                    return VectorDoc(
                        id=row['id'],
                        text=row['text'],
                        vector=vector,
                        metadata=metadata
                    )
                
                return None
                
            except Exception as e:
                logger.error(f"Error getting document {doc_id}: {e}")
                return None
    
    async def delete(self, doc_id: str) -> None:
        """Delete document by ID."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                await conn.execute(
                    "DELETE FROM vector_documents WHERE id = $1",
                    doc_id
                )
                logger.debug(f"Deleted document {doc_id} from PostgreSQL")
                
            except Exception as e:
                logger.error(f"Error deleting document {doc_id}: {e}")
                raise
    
    async def delete_batch(self, doc_ids: List[str]) -> None:
        """Delete multiple documents by ID."""
        if not doc_ids:
            return
        
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                await conn.execute(
                    "DELETE FROM vector_documents WHERE id = ANY($1)",
                    doc_ids
                )
                logger.info(f"Deleted {len(doc_ids)} documents from PostgreSQL")
                
            except Exception as e:
                logger.error(f"Error deleting batch of {len(doc_ids)} documents: {e}")
                raise
    
    async def count(self, filters: Optional[SearchFilters] = None) -> int:
        """Count documents matching filters."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                query = "SELECT COUNT(*) FROM vector_documents"
                params = []
                
                # Add filters
                where_clauses = []
                if filters:
                    where_clauses, params = self._build_where_clauses(filters, params)
                
                if where_clauses:
                    query += " WHERE " + " AND ".join(where_clauses)
                
                result = await conn.fetchval(query, *params)
                return result
                
            except Exception as e:
                logger.error(f"Error counting documents: {e}")
                return 0
    
    async def initialize(self) -> None:
        """Initialize the PostgreSQL database and tables."""
        pool = await self._get_pool()
        
        async with pool.acquire() as conn:
            try:
                # Enable pgvector extension
                await conn.execute("CREATE EXTENSION IF NOT EXISTS vector")
                
                # Create table
                await conn.execute(f"""
                    CREATE TABLE IF NOT EXISTS {self.table_name} (
                        id TEXT PRIMARY KEY,
                        text TEXT NOT NULL,
                        vector vector(1536),  -- Adjust dimension as needed
                        metadata JSONB,
                        created_at TIMESTAMP DEFAULT NOW(),
                        updated_at TIMESTAMP DEFAULT NOW()
                    )
                """)
                
                # Create indexes
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS {self.table_name}_vector_idx 
                    ON {self.table_name} USING ivfflat (vector vector_cosine_ops)
                    WITH (lists = 100)
                """)
                
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS {self.table_name}_text_idx 
                    ON {self.table_name} USING gin(to_tsvector('english', text))
                """)
                
                await conn.execute(f"""
                    CREATE INDEX IF NOT EXISTS {self.table_name}_metadata_idx 
                    ON {self.table_name} USING gin(metadata)
                """)
                
                logger.info(f"Initialized PostgreSQL table '{self.table_name}' with pgvector")
                
            except Exception as e:
                logger.error(f"Error initializing PostgreSQL: {e}")
                raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of PostgreSQL."""
        try:
            pool = await self._get_pool()
            
            async with pool.acquire() as conn:
                # Check connection and get stats
                version = await conn.fetchval("SELECT version()")
                doc_count = await self.count()
                
                # Check if pgvector extension is available
                pgvector_available = await conn.fetchval(
                    "SELECT EXISTS(SELECT 1 FROM pg_extension WHERE extname = 'vector')"
                )
                
                return {
                    "status": "healthy",
                    "backend": "postgresql_pgvector",
                    "version": version,
                    "document_count": doc_count,
                    "pgvector_enabled": pgvector_available,
                    "table_name": self.table_name
                }
                
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "postgresql_pgvector",
                "error": str(e),
                "connection_string": self.connection_string.split('@')[0] + '@***'  # Hide credentials
            }
