#!/usr/bin/env python3
"""
Find all defects in the AI Testing - Defect Management iteration path.
Now that we know work items exist in the 752000+ range, let's scan more systematically.
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def scan_work_items_in_ranges(ado_client, ranges):
    """Scan work items in multiple ID ranges."""
    print(f"\n🔍 Scanning work items in multiple ranges...")
    
    # Get target iteration from environment or use default
    target_iteration = os.environ.get('TARGET_ITERATION', "Air4 Channels Testing\\AI Testing  - Defect Management")
    all_work_items = []
    ai_testing_items = []
    all_defects = []
    
    total_scanned = 0
    
    for start_id, end_id in ranges:
        print(f"\n📍 Scanning range {start_id} to {end_id}...")
        range_items = []
        
        for work_item_id in range(start_id, end_id + 1):
            try:
                item = await ado_client.get_work_item(work_item_id)
                if item:
                    all_work_items.append(item)
                    range_items.append(item)
                    
                    fields = item.get('fields', {})
                    iteration_path = fields.get('System.IterationPath', '')
                    work_item_type = fields.get('System.WorkItemType', '')
                    title = fields.get('System.Title', '')
                    
                    # Check if this matches our target iteration (exact match)
                    if iteration_path == target_iteration:
                        ai_testing_items.append(item)
                        print(f"   🎯 AI Testing Match: {work_item_id} - {work_item_type} - {title[:50]}")
                    
                    # Collect all defects/bugs regardless of iteration
                    if work_item_type.lower() in ['bug', 'defect']:
                        all_defects.append(item)
                        if iteration_path == target_iteration:
                            print(f"   🐛 AI Testing Defect: {work_item_id} - {title[:50]}")
                    
                total_scanned += 1
                
                # Progress indicator
                if total_scanned % 100 == 0:
                    print(f"   Progress: {total_scanned} items scanned, {len(ai_testing_items)} AI Testing matches, {len(all_defects)} total defects")
                    
            except Exception:
                # Work item doesn't exist or can't be accessed
                continue
        
        print(f"   Range {start_id}-{end_id}: Found {len(range_items)} work items")
    
    print(f"\n📊 SCAN SUMMARY:")
    print(f"   Total work items found: {len(all_work_items)}")
    print(f"   AI Testing iteration matches: {len(ai_testing_items)}")
    print(f"   Total defects/bugs found: {len(all_defects)}")
    
    return ai_testing_items, all_defects, all_work_items


async def analyze_and_display_results(ai_testing_items, all_defects, all_work_items):
    """Analyze and display the results."""
    
    if ai_testing_items:
        print(f"\n🎯 AI TESTING DEFECT MANAGEMENT ITEMS ({len(ai_testing_items)}):")
        print("=" * 120)
        print(f"{'ID':<8} {'Type':<12} {'State':<12} {'Priority':<8} {'Severity':<10} {'Assigned To':<20} {'Title'}")
        print("-" * 120)
        
        for item in ai_testing_items:
            fields = item.get('fields', {})
            work_item_id = item.get('id', 'N/A')
            title = fields.get('System.Title', 'No Title')[:40]
            state = fields.get('System.State', 'N/A')
            work_type = fields.get('System.WorkItemType', 'N/A')
            priority = fields.get('Microsoft.VSTS.Common.Priority', 'N/A')
            severity = fields.get('Microsoft.VSTS.Common.Severity', 'N/A')
            assigned_to = fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')[:20]
            
            print(f"{work_item_id:<8} {work_type:<12} {state:<12} {priority:<8} {severity:<10} {assigned_to:<20} {title}")
        
        # Show detailed info for each AI Testing item
        print(f"\n📋 DETAILED AI TESTING ITEMS:")
        print("=" * 120)
        
        for item in ai_testing_items:
            fields = item.get('fields', {})
            work_item_id = item.get('id')
            
            print(f"\n📄 Work Item {work_item_id}:")
            print(f"   Title: {fields.get('System.Title', 'N/A')}")
            print(f"   Type: {fields.get('System.WorkItemType', 'N/A')}")
            print(f"   State: {fields.get('System.State', 'N/A')}")
            print(f"   Priority: {fields.get('Microsoft.VSTS.Common.Priority', 'N/A')}")
            print(f"   Severity: {fields.get('Microsoft.VSTS.Common.Severity', 'N/A')}")
            print(f"   Assigned To: {fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')}")
            print(f"   Created: {fields.get('System.CreatedDate', 'N/A')}")
            print(f"   Changed: {fields.get('System.ChangedDate', 'N/A')}")
            print(f"   Area Path: {fields.get('System.AreaPath', 'N/A')}")
            print(f"   Iteration: {fields.get('System.IterationPath', 'N/A')}")
            print(f"   URL: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{work_item_id}")
            
            # Show description if available
            description = fields.get('System.Description', '')
            if description and description != 'N/A':
                print(f"   Description: {description[:200]}...")
    
    # Analyze all work items to understand the project structure
    if all_work_items:
        print(f"\n📊 PROJECT ANALYSIS ({len(all_work_items)} total items):")
        print("=" * 80)
        
        # Analyze work types
        work_types = {}
        states = {}
        iterations = {}
        areas = {}
        
        for item in all_work_items:
            fields = item.get('fields', {})
            
            work_type = fields.get('System.WorkItemType', 'Unknown')
            work_types[work_type] = work_types.get(work_type, 0) + 1
            
            state = fields.get('System.State', 'Unknown')
            states[state] = states.get(state, 0) + 1
            
            iteration = fields.get('System.IterationPath', 'Unknown')
            iterations[iteration] = iterations.get(iteration, 0) + 1
            
            area = fields.get('System.AreaPath', 'Unknown')
            areas[area] = areas.get(area, 0) + 1
        
        print(f"📋 Work Item Types:")
        for wtype, count in sorted(work_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   {wtype}: {count}")
        
        print(f"\n📊 States:")
        for state, count in sorted(states.items(), key=lambda x: x[1], reverse=True):
            print(f"   {state}: {count}")
        
        print(f"\n📍 Iteration Paths:")
        for iteration, count in sorted(iterations.items(), key=lambda x: x[1], reverse=True):
            print(f"   {iteration}: {count}")
            
        print(f"\n📍 Area Paths:")
        for area, count in sorted(areas.items(), key=lambda x: x[1], reverse=True):
            print(f"   {area}: {count}")


async def export_results(ai_testing_items, all_defects, all_work_items):
    """Export results to JSON file."""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"ai_testing_defects_{timestamp}.json"
    
    export_data = {
        "timestamp": timestamp,
        "target_iteration": "Air4 Channels Testing\\AI Testing  - Defect Management",
        "ai_testing_items_count": len(ai_testing_items),
        "total_defects_count": len(all_defects),
        "total_work_items_count": len(all_work_items),
        "ai_testing_items": [],
        "all_defects": [],
        "summary": {
            "work_types": {},
            "states": {},
            "iterations": {},
            "areas": {}
        }
    }
    
    # Export AI Testing items
    for item in ai_testing_items:
        fields = item.get('fields', {})
        export_data["ai_testing_items"].append({
            "id": item.get('id'),
            "title": fields.get('System.Title'),
            "type": fields.get('System.WorkItemType'),
            "state": fields.get('System.State'),
            "priority": fields.get('Microsoft.VSTS.Common.Priority'),
            "severity": fields.get('Microsoft.VSTS.Common.Severity'),
            "iteration_path": fields.get('System.IterationPath'),
            "area_path": fields.get('System.AreaPath'),
            "assigned_to": fields.get('System.AssignedTo', {}).get('displayName'),
            "created_date": fields.get('System.CreatedDate'),
            "changed_date": fields.get('System.ChangedDate'),
            "description": fields.get('System.Description'),
            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
        })
    
    # Export all defects
    for item in all_defects:
        fields = item.get('fields', {})
        export_data["all_defects"].append({
            "id": item.get('id'),
            "title": fields.get('System.Title'),
            "type": fields.get('System.WorkItemType'),
            "state": fields.get('System.State'),
            "iteration_path": fields.get('System.IterationPath'),
            "area_path": fields.get('System.AreaPath'),
            "assigned_to": fields.get('System.AssignedTo', {}).get('displayName'),
            "created_date": fields.get('System.CreatedDate'),
            "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
        })
    
    with open(filename, 'w', encoding='utf-8') as f:
        json.dump(export_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n💾 Results exported to: {filename}")
    return filename


async def main():
    """Main function."""
    print("🚀 Finding All AI Testing Defects")
    print("=" * 60)
    print("🎯 Target: Air4 Channels Testing\\AI Testing  - Defect Management")
    print("=" * 60)
    
    try:
        # Get configuration and create client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        ado_client = AdoClient(config)
        
        # Based on finding 752662, let's scan multiple ranges around that area
        # and also check some other common ranges
        scan_ranges = [
            (752000, 753000),  # Around the known work item
            (750000, 752000),  # Before the known range
            (753000, 755000),  # After the known range
            (700000, 750000),  # Earlier range
            (755000, 760000),  # Later range
        ]
        
        print(f"\n🔍 Will scan {sum(end - start + 1 for start, end in scan_ranges)} work item IDs across {len(scan_ranges)} ranges")
        
        # Scan for work items
        ai_testing_items, all_defects, all_work_items = await scan_work_items_in_ranges(ado_client, scan_ranges)
        
        # Analyze and display results
        await analyze_and_display_results(ai_testing_items, all_defects, all_work_items)
        
        # Export results
        filename = await export_results(ai_testing_items, all_defects, all_work_items)
        
        # Final summary
        print(f"\n🎉 FINAL RESULTS:")
        print("=" * 60)
        print(f"✅ AI Testing Defect Management items: {len(ai_testing_items)}")
        print(f"📊 Total defects/bugs found: {len(all_defects)}")
        print(f"📋 Total work items scanned: {len(all_work_items)}")
        print(f"💾 Results exported to: {filename}")
        
        if ai_testing_items:
            print(f"\n🎯 SUCCESS! Found {len(ai_testing_items)} items in the AI Testing - Defect Management iteration!")
            for item in ai_testing_items:
                fields = item.get('fields', {})
                work_item_id = item.get('id')
                title = fields.get('System.Title', 'No Title')[:60]
                work_type = fields.get('System.WorkItemType', 'Unknown')
                state = fields.get('System.State', 'Unknown')
                print(f"   • {work_item_id}: {work_type} ({state}) - {title}")
        else:
            print(f"\n⚠️ No items found in the exact AI Testing - Defect Management iteration")
            if all_defects:
                print(f"💡 However, found {len(all_defects)} total defects in the project")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
