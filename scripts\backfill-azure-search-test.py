#!/usr/bin/env python3
"""
Backfill Azure Search with 2000 historical records to test search functionality.
This script bypasses production safety for historical indexing only (no modifications to work items).
"""

import asyncio
import json
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

async def backfill_azure_search_test(max_records: int = 2000):
    """Backfill Azure Search with historical records to test search functionality."""
    print("🔍 Azure Search Backfill Test - Historical Records")
    print("=" * 70)
    print(f"Target: {max_records} historical work items")
    print("Purpose: Test Azure Search indexing and comprehensive field mapping")
    print()
    
    try:
        # Set environment variables from local.settings.json
        with open(os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json'), 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = value
        
        from __app__.common.models.schemas import WorkItem
        from __app__.common.adapters.ado_client import AdoClient
        from __app__.common.adapters.search_client import SearchClient
        from __app__.common.ai.embeddings import EmbeddingService
        from __app__.common.utils.config import get_config
        from __app__.workitem_created.handler import convert_ado_to_work_item
        
        # Initialize clients
        config = get_config()
        ado_client = AdoClient(config)
        search_client = SearchClient(config)
        embedding_service = EmbeddingService(config)
        
        print("✅ Initialized clients")
        print(f"   ADO Organization: {config.ADO_ORGANIZATION}")
        print(f"   ADO Project: {config.ADO_PROJECT}")
        print(f"   Search Service: {config.AZURE_SEARCH_SERVICE_NAME}")
        print(f"   Search Index: {search_client.index_name}")
        print()
        
        # Ensure search index exists with comprehensive schema
        print("📋 Creating/updating Azure Search index...")
        await search_client.ensure_index_exists()
        print("✅ Search index ready with comprehensive field schema")
        print()
        
        # Use the existing working function to get historical work items
        print("🔍 Using existing working function to get historical work items...")
        print(f"   Target: {max_records} work items")
        print(f"   Strategy: Use get_recent_bug_work_items with extended time range")

        from __app__.workitem_created.handler import get_recent_bug_work_items

        # Try different time ranges to get enough work items
        work_item_ids = []
        time_ranges = [24, 168, 720, 2160, 8760]  # 1 day, 1 week, 1 month, 3 months, 1 year

        for hours_back in time_ranges:
            if len(work_item_ids) >= max_records:
                break

            print(f"   📊 Trying {hours_back} hours back ({hours_back//24} days)...")

            try:
                # Get Bug work items using the working function
                bug_work_items = await get_recent_bug_work_items(ado_client, hours_back)

                # Extract IDs and add to our list
                for item in bug_work_items:
                    work_item_id = item.get('id')
                    if work_item_id and work_item_id not in work_item_ids:
                        work_item_ids.append(work_item_id)

                print(f"      Found {len(bug_work_items)} bugs, total unique: {len(work_item_ids)}")

                if len(work_item_ids) >= max_records:
                    work_item_ids = work_item_ids[:max_records]
                    break

            except Exception as e:
                print(f"      ⚠️ Error with {hours_back} hours: {e}")
                continue
        
        print(f"✅ Found {len(work_item_ids)} work items (limited to {max_records})")
        print()
        
        if not work_item_ids:
            print("❌ No work items found in the specified date range")
            return False
        
        # Process work items in batches
        batch_size = 50
        total_processed = 0
        total_failed = 0
        total_indexed = 0
        
        print("📤 Starting batch processing and indexing...")
        print("-" * 50)
        
        for i in range(0, len(work_item_ids), batch_size):
            batch_ids = work_item_ids[i:i + batch_size]
            batch_num = (i // batch_size) + 1
            total_batches = (len(work_item_ids) + batch_size - 1) // batch_size
            
            print(f"Batch {batch_num}/{total_batches}: Processing {len(batch_ids)} work items...")
            
            batch_processed = 0
            batch_failed = 0
            batch_indexed = 0
            
            for work_item_id in batch_ids:
                try:
                    # Fetch full work item data with all fields
                    work_item_data = await ado_client.get_work_item(work_item_id)
                    
                    if not work_item_data:
                        print(f"   ⚠️ Work item {work_item_id} not found")
                        batch_failed += 1
                        continue
                    
                    # Convert to WorkItem object
                    work_item = convert_ado_to_work_item(work_item_data)
                    batch_processed += 1
                    
                    # Generate embedding for the work item
                    try:
                        embedding = await embedding_service.embed_work_item(work_item)
                    except Exception as e:
                        print(f"   ⚠️ Failed to generate embedding for {work_item_id}: {e}")
                        embedding = None
                    
                    # Extract comprehensive ADO fields
                    ado_fields = work_item_data.get("fields", {})
                    
                    # Index with comprehensive fields (bypass production safety for historical data)
                    await index_historical_work_item(
                        search_client, 
                        work_item, 
                        embedding, 
                        ado_fields
                    )
                    batch_indexed += 1
                    
                    if batch_processed % 10 == 0:
                        print(f"   📊 Progress: {batch_processed}/{len(batch_ids)} processed, {batch_indexed} indexed")
                    
                except Exception as e:
                    batch_failed += 1
                    print(f"   ❌ Error processing work item {work_item_id}: {e}")
                    continue
            
            total_processed += batch_processed
            total_failed += batch_failed
            total_indexed += batch_indexed
            
            print(f"   ✅ Batch {batch_num} complete: {batch_processed} processed, {batch_indexed} indexed, {batch_failed} failed")
            
            # Small delay between batches to avoid overwhelming the services
            await asyncio.sleep(1)
        
        print("\n" + "=" * 70)
        print("📊 Backfill Results")
        print("=" * 70)
        print(f"Total work items found: {len(work_item_ids)}")
        print(f"Successfully processed: {total_processed}")
        print(f"Successfully indexed: {total_indexed}")
        print(f"Failed: {total_failed}")
        print(f"Success rate: {(total_indexed/len(work_item_ids)*100):.1f}%")
        print()
        
        # Test search functionality
        if total_indexed > 0:
            print("🔍 Testing Azure Search functionality...")
            await test_search_functionality(search_client, total_indexed)
        
        return total_indexed > 0
        
    except Exception as e:
        print(f"❌ Error during backfill: {e}")
        import traceback
        traceback.print_exc()
        return False

async def index_historical_work_item(
    search_client,
    work_item,
    embedding: Optional[List[float]],
    ado_fields: Dict[str, Any]
) -> None:
    """
    Index historical work item bypassing production safety.
    This is safe because we're only indexing for search, not modifying work items.
    """
    try:
        # Extract comprehensive fields
        document = search_client._extract_comprehensive_fields(work_item, ado_fields)
        
        # Add embeddings if available
        if embedding:
            document["title_vector"] = embedding
            document["content_vector"] = embedding
        
        # Upload the document directly
        result = search_client.search_client.upload_documents([document])
        
    except Exception as e:
        raise Exception(f"Failed to index work item {work_item.id}: {e}")

async def test_search_functionality(search_client, total_indexed: int):
    """Test various search capabilities after indexing."""
    print("-" * 50)
    
    try:
        # Test 1: Basic text search
        print("1. Testing basic text search...")
        results = await search_client.hybrid_search(
            query_text="bug authentication login",
            top=5
        )
        print(f"   ✅ Found {len(results)} results for 'bug authentication login'")
        
        # Test 2: Filtered search by work item type
        print("2. Testing filtered search by work item type...")
        results = await search_client.hybrid_search(
            query_text="*",
            filters="work_item_type eq 'Bug'",
            top=10
        )
        print(f"   ✅ Found {len(results)} Bug work items")
        
        # Test 3: Filtered search by state
        print("3. Testing filtered search by state...")
        results = await search_client.hybrid_search(
            query_text="*",
            filters="state eq 'Active'",
            top=10
        )
        print(f"   ✅ Found {len(results)} Active work items")
        
        # Test 4: Date range search
        print("4. Testing date range search...")
        last_month = (datetime.utcnow() - timedelta(days=30)).strftime("%Y-%m-%d")
        results = await search_client.hybrid_search(
            query_text="*",
            filters=f"created_date ge {last_month}T00:00:00Z",
            top=10
        )
        print(f"   ✅ Found {len(results)} work items created in last 30 days")
        
        # Test 5: Priority and business value search
        print("5. Testing priority and business value search...")
        results = await search_client.hybrid_search(
            query_text="*",
            filters="priority le 2",
            top=10
        )
        print(f"   ✅ Found {len(results)} high priority work items (priority <= 2)")
        
        # Test 6: Area path search
        print("6. Testing area path search...")
        results = await search_client.hybrid_search(
            query_text="*",
            filters="search.ismatch('*', 'area_path')",
            top=5
        )
        print(f"   ✅ Found {len(results)} work items with area paths")
        
        # Test 7: Semantic search
        print("7. Testing semantic search...")
        results = await search_client.semantic_search(
            query_text="performance issue slow response",
            top=5
        )
        print(f"   ✅ Found {len(results)} semantically similar results for performance issues")
        
        print()
        print("🎯 Azure Search Test Summary:")
        print(f"   ✅ {total_indexed} work items successfully indexed")
        print("   ✅ Basic text search working")
        print("   ✅ Filtered search working")
        print("   ✅ Date range filtering working")
        print("   ✅ Priority filtering working")
        print("   ✅ Area path search working")
        print("   ✅ Semantic search working")
        print("   ✅ Comprehensive field mapping working")
        print()
        print("🚀 Azure Search is ready for production use!")
        
    except Exception as e:
        print(f"   ❌ Error testing search functionality: {e}")

async def main():
    """Run the Azure Search backfill test."""
    print("🚀 Azure Search Backfill Test")
    print("=" * 80)
    print("Purpose: Index 2000 historical records to test Azure Search functionality")
    print("Safety: Only indexing for search - no work item modifications")
    print()
    
    # Get number of records from command line or default to 2000
    max_records = 2000
    if len(sys.argv) > 1:
        try:
            max_records = int(sys.argv[1])
        except ValueError:
            print("Invalid number of records specified, using default 2000")
    
    result = await backfill_azure_search_test(max_records)
    
    print("\n📋 Final Results")
    print("=" * 80)
    if result:
        print("✅ AZURE SEARCH BACKFILL SUCCESSFUL")
        print("🔍 Search index populated with historical data")
        print("📊 Comprehensive field mapping verified")
        print("🎯 Ready for assignment engine testing")
    else:
        print("❌ AZURE SEARCH BACKFILL FAILED")
        print("⚠️ Check configuration and try again")
    
    return result

if __name__ == "__main__":
    asyncio.run(main())
