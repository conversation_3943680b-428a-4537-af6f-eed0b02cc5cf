#!/usr/bin/env python3
"""
Test script to verify NO HALLUCINATION in assignee suggestions.
Tests that the triage service only suggests assignees from REAL historical data.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.common.utils.config import get_config
from __app__.common.services.triage import TriageService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_no_hallucination():
    """Test that NO HALLUCINATION occurs in assignee suggestions."""
    
    print("🚫 NO HALLUCINATION TEST")
    print("=" * 50)
    print("🎯 Goal: Verify assignee suggestions come from REAL data only")
    print("❌ FAIL if any fake/hallucinated assignees are suggested")
    
    try:
        config = get_config()
        triage_service = TriageService(config)
        
        # Test 1: With REAL historical data
        print(f"\n🧪 Test 1: With REAL Historical Data")
        print("-" * 40)
        
        real_similar_items = [
            {
                'id': 100001,
                'score': 0.95,
                'title': 'Authentication bug in login system',
                'assigned_to': 'Alice Johnson <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 1',
                'created_date': '2025-09-15T10:30:00Z',
                'priority': 2
            },
            {
                'id': 100002,
                'score': 0.87,
                'title': 'Login failure on mobile app',
                'assigned_to': 'Bob Smith <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Resolved',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 2',
                'created_date': '2025-09-20T14:15:00Z',
                'priority': 1
            },
            {
                'id': 100003,
                'score': 0.82,
                'title': 'User authentication timeout',
                'assigned_to': 'Alice Johnson <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 1',
                'created_date': '2025-09-25T09:45:00Z',
                'priority': 2
            }
        ]
        
        print(f"📊 Historical Data:")
        expected_assignees = set()
        for item in real_similar_items:
            assignee = item['assigned_to']
            name = assignee.split('<')[0].strip() if '<' in assignee else assignee
            expected_assignees.add(name)
            print(f"   • #{item['id']}: {assignee}")
        
        print(f"✅ Expected assignees: {expected_assignees}")
        
        suggestions = await triage_service.suggest_assignees(
            work_item_id=748404,
            title="New authentication bug",
            description="User cannot login to the system",
            work_item_type="Bug",
            similar_items=real_similar_items,
            k=3
        )
        
        print(f"\n🤖 Assignee Suggestions:")
        suggested_names = set()
        for i, suggestion in enumerate(suggestions, 1):
            name = suggestion['name']
            display_name = name.split('<')[0].strip() if '<' in name else name
            suggested_names.add(display_name)
            print(f"   {i}. {name} (Score: {suggestion['score']:.3f})")
            print(f"      Historical Count: {suggestion['historical_count']}")
            print(f"      Data Source: {suggestion['data_source']}")
        
        # Check for hallucination
        hallucinated = suggested_names - expected_assignees
        if hallucinated:
            print(f"\n❌ HALLUCINATION DETECTED!")
            print(f"   Fake assignees: {hallucinated}")
            print(f"   Expected: {expected_assignees}")
            print(f"   Suggested: {suggested_names}")
            return False
        else:
            print(f"\n✅ No hallucination - all suggestions from real data")
        
        # Test 2: With NO historical data
        print(f"\n🧪 Test 2: With NO Historical Data")
        print("-" * 40)
        
        empty_suggestions = await triage_service.suggest_assignees(
            work_item_id=748404,
            title="New authentication bug",
            description="User cannot login to the system",
            work_item_type="Bug",
            similar_items=[],  # NO historical data
            k=3
        )
        
        if empty_suggestions:
            print(f"❌ HALLUCINATION: Got {len(empty_suggestions)} suggestions with NO historical data!")
            for suggestion in empty_suggestions:
                print(f"   • {suggestion['name']} (HALLUCINATED)")
            return False
        else:
            print(f"✅ Correctly returned no suggestions with no historical data")
        
        # Test 3: With unassigned historical data
        print(f"\n🧪 Test 3: With Unassigned Historical Data")
        print("-" * 40)
        
        unassigned_items = [
            {
                'id': 100004,
                'score': 0.90,
                'title': 'Another authentication bug',
                'assigned_to': '',  # Unassigned
                'work_item_type': 'Bug',
                'state': 'New',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 3',
                'created_date': '2025-10-01T10:00:00Z',
                'priority': 3
            },
            {
                'id': 100005,
                'score': 0.85,
                'title': 'Login issue',
                'assigned_to': 'Unassigned',  # Explicitly unassigned
                'work_item_type': 'Bug',
                'state': 'New',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 3',
                'created_date': '2025-10-02T11:00:00Z',
                'priority': 3
            }
        ]
        
        unassigned_suggestions = await triage_service.suggest_assignees(
            work_item_id=748404,
            title="New authentication bug",
            description="User cannot login to the system",
            work_item_type="Bug",
            similar_items=unassigned_items,
            k=3
        )
        
        if unassigned_suggestions:
            print(f"❌ HALLUCINATION: Got {len(unassigned_suggestions)} suggestions from unassigned items!")
            for suggestion in unassigned_suggestions:
                print(f"   • {suggestion['name']} (HALLUCINATED)")
            return False
        else:
            print(f"✅ Correctly returned no suggestions from unassigned items")
        
        # Test 4: Mixed data (some assigned, some unassigned)
        print(f"\n🧪 Test 4: Mixed Data (Assigned + Unassigned)")
        print("-" * 40)
        
        mixed_items = [
            {
                'id': 100006,
                'score': 0.92,
                'title': 'Critical auth bug',
                'assigned_to': 'Charlie Brown <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 4',
                'created_date': '2025-10-05T10:00:00Z',
                'priority': 1
            },
            {
                'id': 100007,
                'score': 0.88,
                'title': 'Auth timeout',
                'assigned_to': '',  # Unassigned
                'work_item_type': 'Bug',
                'state': 'New',
                'area_path': 'Authentication',
                'iteration_path': 'Sprint 4',
                'created_date': '2025-10-06T11:00:00Z',
                'priority': 2
            }
        ]
        
        expected_mixed = {'Charlie Brown'}
        
        mixed_suggestions = await triage_service.suggest_assignees(
            work_item_id=748404,
            title="New authentication bug",
            description="User cannot login to the system",
            work_item_type="Bug",
            similar_items=mixed_items,
            k=3
        )
        
        suggested_mixed = set()
        for suggestion in mixed_suggestions:
            name = suggestion['name']
            display_name = name.split('<')[0].strip() if '<' in name else name
            suggested_mixed.add(display_name)
        
        hallucinated_mixed = suggested_mixed - expected_mixed
        if hallucinated_mixed:
            print(f"❌ HALLUCINATION in mixed data: {hallucinated_mixed}")
            return False
        else:
            print(f"✅ Only real assignees suggested from mixed data: {suggested_mixed}")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    
    print("🚫 NO HALLUCINATION TEST - Assignee Suggestions")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 CRITICAL: Verify NO fake assignees are suggested")
    print(f"✅ PASS: Only real historical assignees suggested")
    print(f"❌ FAIL: Any hallucinated/fake assignees detected")
    
    success = await test_no_hallucination()
    
    print(f"\n{'='*80}")
    print("🏁 FINAL RESULT")
    print(f"{'='*80}")
    
    if success:
        print("🎉 ✅ NO HALLUCINATION TEST PASSED!")
        print("✅ Assignee suggestions use REAL data only")
        print("✅ No fake/hallucinated assignees detected")
        print("✅ System correctly handles empty/unassigned data")
        print("✅ SAFE FOR PRODUCTION USE")
    else:
        print("💥 ❌ NO HALLUCINATION TEST FAILED!")
        print("⚠️ CRITICAL: Fake assignees detected!")
        print("🚫 DO NOT USE IN PRODUCTION")
        print("🔧 FIX REQUIRED: Remove hallucination from assignee suggestions")
    
    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
