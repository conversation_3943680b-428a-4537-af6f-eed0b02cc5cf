#!/usr/bin/env python3
"""
Test script for work item 748404 with the new REAL assignee suggestion system.
NO HALLUCINATION - Only real historical assignees should be suggested.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.common.utils.config import get_config
from __app__.common.services.triage import TriageService
from __app__.common.services.history import HistoryService
from __app__.common.adapters.ado_client import AdoClient
from __app__.common.vectorstore.azure_search import AzureSearchClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_workitem_748404_real_assignees():
    """Test work item 748404 with the new REAL assignee system."""
    
    print("🧪 Testing Work Item 748404 - REAL Assignee Suggestions")
    print("=" * 70)
    print("🚫 NO HALLUCINATION: Only real historical assignees allowed")
    print("✅ REAL DATA ONLY: Assignees from iteration paths and comments")
    
    try:
        config = get_config()

        # Initialize services
        ado_client = AdoClient(config)
        vector_store = AzureSearchClient(config)
        history_service = HistoryService(vector_store, ado_client, config)
        triage_service = TriageService(config)
        
        # Work item 748404 details
        work_item_id = 748404
        title = "Test - New defect"
        description = "Testing the assignee suggestion system with real data"
        work_item_type = "Bug"
        
        print(f"\n🎯 Work Item Details:")
        print(f"   ID: {work_item_id}")
        print(f"   Title: {title}")
        print(f"   Type: {work_item_type}")
        print(f"   Description: {description}")
        
        # 1. Find similar historical items using the history service
        print(f"\n🔍 Step 1: Finding Similar Historical Items...")
        
        work_item_dict = {
            'id': work_item_id,
            'fields': {
                'System.Title': title,
                'System.Description': description,
                'System.WorkItemType': work_item_type
            }
        }
        
        # Generate placeholder embedding vector (in real implementation, this would use proper embedding service)
        query_vector = [0.0] * 1536  # Placeholder vector
        
        similar_items = await history_service.find_similar_items(
            work_item=work_item_dict,
            query_vector=query_vector,
            k=10,
            include_resolved=True
        )
        
        print(f"   ✅ Found {len(similar_items)} similar items")
        
        if similar_items:
            print(f"   📊 Similar Items:")
            for i, item in enumerate(similar_items[:5], 1):  # Show top 5
                assignee = item.metadata.get('assigned_to', 'Unassigned')
                print(f"      {i}. #{item.id}: {item.metadata.get('title', 'No title')[:40]}...")
                print(f"         Assignee: {assignee}")
                print(f"         Score: {item.score:.3f}")
                print(f"         State: {item.metadata.get('state', 'Unknown')}")
        
        # Convert SearchHit objects to dict format for triage service
        similar_items_dict = []
        for item in similar_items:
            similar_items_dict.append({
                'id': item.id,
                'score': item.score,
                'title': item.metadata.get('title', ''),
                'assigned_to': item.metadata.get('assigned_to', ''),
                'work_item_type': item.metadata.get('work_item_type', ''),
                'state': item.metadata.get('state', ''),
                'area_path': item.metadata.get('area_path', ''),
                'iteration_path': item.metadata.get('iteration_path', ''),
                'created_date': item.metadata.get('created_date', ''),
                'priority': item.metadata.get('priority', 3)
            })
        
        # 2. Get triage recommendations using REAL data
        print(f"\n🤖 Step 2: Getting REAL Assignee Suggestions...")
        
        recommendations = await triage_service.get_triage_recommendations(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=similar_items_dict
        )
        
        print(f"   ✅ Triage recommendations generated")
        print(f"   📊 Data Source: {recommendations.get('data_source', 'unknown')}")
        
        # 3. Display assignee suggestions
        suggested_assignees = recommendations.get('suggested_assignees', [])
        print(f"\n👥 Assignee Suggestions (REAL DATA ONLY):")
        print(f"   Found {len(suggested_assignees)} suggestions")
        
        if suggested_assignees:
            for i, assignee in enumerate(suggested_assignees, 1):
                print(f"\n   {i}. {assignee['name']}")
                print(f"      Email: {assignee['email']}")
                print(f"      Score: {assignee['score']:.3f}")
                print(f"      Historical Count: {assignee['historical_count']}")
                print(f"      Current Workload: {assignee['current_workload']}")
                print(f"      Rationale: {assignee['rationale']}")
                print(f"      Data Source: {assignee['data_source']}")
        else:
            print("   ❌ No assignee suggestions found")
        
        # 4. Display priority recommendation
        priority_rec = recommendations.get('priority_recommendation', {})
        print(f"\n🎯 Priority Recommendation:")
        print(f"   Priority: {priority_rec.get('priority', 'N/A')}")
        print(f"   Confidence: {priority_rec.get('confidence', 0.0):.3f}")
        print(f"   Rationale: {priority_rec.get('rationale', 'N/A')}")
        
        # 5. Verification checks
        print(f"\n🔍 VERIFICATION CHECKS:")
        
        # Check data source
        if recommendations.get('data_source') == 'real_ado_history':
            print(f"   ✅ Data source confirmed as real ADO history")
        else:
            print(f"   ⚠️ Data source not confirmed: {recommendations.get('data_source')}")
        
        # Check for hallucination
        real_assignees = set()
        for item in similar_items_dict:
            assignee = item.get('assigned_to', '').strip()
            if assignee and assignee.lower() not in ['unassigned', '', 'none']:
                # Extract name part before email
                if '<' in assignee:
                    name = assignee.split('<')[0].strip()
                    real_assignees.add(name)
                else:
                    real_assignees.add(assignee)
        
        suggested_names = set()
        for assignee in suggested_assignees:
            name = assignee['name']
            if '<' in name:
                name = name.split('<')[0].strip()
            suggested_names.add(name)
        
        hallucinated = suggested_names - real_assignees
        if hallucinated:
            print(f"   ❌ HALLUCINATION DETECTED: {hallucinated}")
            print(f"   ⚠️ These names were NOT in the historical data!")
        else:
            print(f"   ✅ No hallucination detected - all suggestions from real data")
        
        # Check auto-assignment recommendation
        auto_assign = recommendations.get('auto_assign_recommended', False)
        print(f"   Auto-assignment recommended: {auto_assign}")
        
        # Summary
        print(f"\n📋 SUMMARY:")
        print(f"   • Similar items found: {len(similar_items)}")
        print(f"   • Assignee suggestions: {len(suggested_assignees)}")
        print(f"   • Data source: {recommendations.get('data_source', 'unknown')}")
        print(f"   • Hallucination check: {'✅ PASSED' if not hallucinated else '❌ FAILED'}")
        print(f"   • Auto-assignment: {'✅ Recommended' if auto_assign else '❌ Not recommended'}")
        
        if suggested_assignees and not hallucinated:
            top_assignee = suggested_assignees[0]
            print(f"   • Top suggestion: {top_assignee['name']} (Score: {top_assignee['score']:.3f})")
        
        return len(suggested_assignees) > 0 and not hallucinated
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    
    print("🧪 Work Item 748404 - REAL Assignee Test")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Goal: Test work item 748404 with REAL assignee suggestions")
    print(f"🚫 NO HALLUCINATION: Only real historical data allowed")
    
    success = await test_workitem_748404_real_assignees()
    
    print(f"\n{'='*80}")
    print("🏁 Test Results")
    print(f"{'='*80}")
    
    if success:
        print("🎉 TEST PASSED!")
        print("✅ Work item 748404 processed successfully")
        print("✅ Assignee suggestions use REAL data only")
        print("✅ No hallucination detected")
        print("✅ System ready for production use")
    else:
        print("❌ TEST FAILED!")
        print("⚠️ Issues detected with assignee suggestions")
        print("🚫 CRITICAL: Ensure no hallucination is occurring!")
    
    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
