"""
Unit tests for Logic App client.
"""

import pytest
import json
from unittest.mock import Async<PERSON><PERSON>, <PERSON><PERSON>, patch
import httpx

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.common.logicapp_client import LogicAppClient, LogicAppError, send_personal_card
from __app__.common.utils.config import Config


@pytest.fixture
def mock_config():
    """Create a mock configuration."""
    config = Mock(spec=Config)
    config.LOGICAPP_URL = "https://test-logic-app.azure.com/workflows/test/triggers/manual"
    return config


@pytest.fixture
def logic_app_client(mock_config):
    """Create a LogicAppClient instance with mock config."""
    return LogicAppClient(mock_config)


class TestLogicAppClient:
    """Test cases for LogicAppClient."""
    
    @pytest.mark.asyncio
    async def test_send_personal_card_202_response(self, logic_app_client):
        """Test successful async response (202)."""
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 202
        mock_response.headers = {"x-ms-workflow-run-id": "test-tracking-id-123"}
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response
            
            # Call the method
            result = await logic_app_client.send_personal_card(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test body content",
                work_item_id=12345,
                adaptive_card={"type": "AdaptiveCard", "version": "1.3"}
            )
            
            # Verify result
            assert result["status"] == "accepted"
            assert result["trackingId"] == "test-tracking-id-123"
            
            # Verify HTTP call
            mock_client.post.assert_called_once()
            call_args = mock_client.post.call_args
            assert call_args[0][0] == logic_app_client.logicapp_url
            
            # Verify payload
            payload = call_args[1]["json"]
            assert payload["To"] == "<EMAIL>"
            assert payload["Subject"] == "Test Subject"
            assert payload["Body"] == "Test body content"
            assert payload["work_item_id"] == 12345
            assert payload["adaptive_card"]["type"] == "AdaptiveCard"
    
    @pytest.mark.asyncio
    async def test_send_personal_card_200_response(self, logic_app_client):
        """Test successful sync response (200)."""
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "message": "Teams message sent successfully",
            "messageId": "msg-123"
        }
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response
            
            # Call the method
            result = await logic_app_client.send_personal_card(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test body content",
                work_item_id=12345
            )
            
            # Verify result
            assert result["status"] == "completed"
            assert result["message"] == "Teams message sent successfully"
            assert result["messageId"] == "msg-123"
    
    @pytest.mark.asyncio
    async def test_send_personal_card_without_adaptive_card(self, logic_app_client):
        """Test sending without adaptive card."""
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 202
        mock_response.headers = {"x-ms-workflow-run-id": "test-tracking-id-456"}
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response
            
            # Call the method without adaptive_card
            result = await logic_app_client.send_personal_card(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test body content",
                work_item_id=12345
            )
            
            # Verify result
            assert result["status"] == "accepted"
            assert result["trackingId"] == "test-tracking-id-456"
            
            # Verify payload doesn't include adaptive_card
            call_args = mock_client.post.call_args
            payload = call_args[1]["json"]
            assert "adaptive_card" not in payload
    
    @pytest.mark.asyncio
    async def test_send_personal_card_400_error(self, logic_app_client):
        """Test error response (400)."""
        
        # Mock HTTP response
        mock_response = Mock()
        mock_response.status_code = 400
        mock_response.json.return_value = {
            "error": "Invalid email address",
            "message": "The provided email address is not valid"
        }
        mock_response.text = "Bad Request"
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.return_value = mock_response
            
            # Call the method and expect exception
            with pytest.raises(LogicAppError) as exc_info:
                await logic_app_client.send_personal_card(
                    to_email="invalid-email",
                    subject="Test Subject",
                    body="Test body content",
                    work_item_id=12345
                )
            
            assert "Logic App request failed with status 400" in str(exc_info.value)
            assert "The provided email address is not valid" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_send_personal_card_timeout(self, logic_app_client):
        """Test timeout handling."""
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.side_effect = httpx.TimeoutException("Request timed out")
            
            # Call the method and expect exception
            with pytest.raises(LogicAppError) as exc_info:
                await logic_app_client.send_personal_card(
                    to_email="<EMAIL>",
                    subject="Test Subject",
                    body="Test body content",
                    work_item_id=12345
                )
            
            assert "Logic App request timed out" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_send_personal_card_network_error(self, logic_app_client):
        """Test network error handling."""
        
        with patch('httpx.AsyncClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value.__aenter__.return_value = mock_client
            mock_client.post.side_effect = httpx.ConnectError("Connection failed")
            
            # Call the method and expect exception
            with pytest.raises(LogicAppError) as exc_info:
                await logic_app_client.send_personal_card(
                    to_email="<EMAIL>",
                    subject="Test Subject",
                    body="Test body content",
                    work_item_id=12345
                )
            
            assert "Logic App request failed: Connection failed" in str(exc_info.value)
    
    def test_init_without_logicapp_url(self):
        """Test initialization without LOGICAPP_URL."""
        
        config = Mock(spec=Config)
        config.LOGICAPP_URL = ""
        
        with pytest.raises(ValueError) as exc_info:
            LogicAppClient(config)
        
        assert "LOGICAPP_URL not configured" in str(exc_info.value)


class TestSendPersonalCardFunction:
    """Test cases for the convenience function."""
    
    @pytest.mark.asyncio
    async def test_send_personal_card_function(self):
        """Test the convenience function."""
        
        with patch('__app__.common.logicapp_client.LogicAppClient') as mock_client_class:
            mock_client = AsyncMock()
            mock_client_class.return_value = mock_client
            mock_client.send_personal_card.return_value = {
                "status": "accepted",
                "trackingId": "test-123"
            }
            
            # Call the convenience function
            result = await send_personal_card(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test body content",
                work_item_id=12345,
                adaptive_card={"type": "AdaptiveCard"}
            )
            
            # Verify result
            assert result["status"] == "accepted"
            assert result["trackingId"] == "test-123"
            
            # Verify client was called correctly
            mock_client.send_personal_card.assert_called_once_with(
                to_email="<EMAIL>",
                subject="Test Subject",
                body="Test body content",
                work_item_id=12345,
                adaptive_card={"type": "AdaptiveCard"}
            )


if __name__ == "__main__":
    pytest.main([__file__])
