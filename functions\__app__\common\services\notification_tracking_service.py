"""
Notification Tracking Service
Manages tracking of sent notifications and handles timeouts.
"""

import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
import json

from ..utils.config import Config
from ..utils.logging import log_structured
from ..adapters.azure_devops_client import AzureDevOpsClient
from ..adapters.teams_client import TeamsClient

logger = logging.getLogger(__name__)


class NotificationTrackingService:
    """Service for tracking notifications and handling timeouts."""
    
    def __init__(self, config: Config):
        self.config = config
        self.azure_client = AzureDevOpsClient(config)
        self.teams_client = TeamsClient(config)
        
        # Storage for notification tracking (in production, use Azure Table Storage or Cosmos DB)
        self._notification_store = {}
    
    async def store_notification(
        self,
        work_item_id: int,
        notification_id: str,
        teams_message_id: Optional[str] = None,
        sent_timestamp: Optional[str] = None,
        status: str = "sent"
    ) -> bool:
        """
        Store notification tracking information.
        
        Args:
            work_item_id: Work item ID
            notification_id: Unique notification identifier
            teams_message_id: Teams message ID if available
            sent_timestamp: When notification was sent
            status: Current status of notification
            
        Returns:
            True if stored successfully
        """
        try:
            # Parse timestamp
            if sent_timestamp:
                timestamp = datetime.fromisoformat(sent_timestamp.replace('Z', '+00:00'))
            else:
                timestamp = datetime.utcnow()
            
            # Create tracking record
            tracking_record = {
                "work_item_id": work_item_id,
                "notification_id": notification_id,
                "teams_message_id": teams_message_id,
                "sent_timestamp": timestamp.isoformat(),
                "status": status,
                "timeout_handled": False,
                "created_at": datetime.utcnow().isoformat()
            }
            
            # Store in memory (replace with persistent storage in production)
            self._notification_store[notification_id] = tracking_record
            
            log_structured(
                logger,
                "info",
                f"Stored notification tracking for {notification_id}",
                extra={
                    "work_item_id": work_item_id,
                    "notification_id": notification_id,
                    "status": status
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to store notification tracking: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "notification_id": notification_id
                },
                exc_info=True
            )
            return False
    
    async def get_timed_out_notifications(self, timeout_minutes: int = 30) -> List[Dict[str, Any]]:
        """
        Get notifications that have timed out.
        
        Args:
            timeout_minutes: Timeout threshold in minutes
            
        Returns:
            List of timed out notification records
        """
        try:
            cutoff_time = datetime.utcnow() - timedelta(minutes=timeout_minutes)
            timed_out = []
            
            for notification_id, record in self._notification_store.items():
                # Skip if already handled
                if record.get("timeout_handled", False):
                    continue
                
                # Skip if not in sent status
                if record.get("status") != "sent":
                    continue
                
                # Check if timed out
                sent_time = datetime.fromisoformat(record["sent_timestamp"])
                if sent_time < cutoff_time:
                    timed_out.append(record)
            
            log_structured(
                logger,
                "info",
                f"Found {len(timed_out)} timed out notifications",
                extra={"timeout_minutes": timeout_minutes, "count": len(timed_out)}
            )
            
            return timed_out
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to get timed out notifications: {e}",
                exc_info=True
            )
            return []
    
    async def handle_notification_timeout(self, notification_record: Dict[str, Any]) -> bool:
        """
        Handle a notification that has timed out.
        
        Args:
            notification_record: The notification record that timed out
            
        Returns:
            True if handled successfully
        """
        try:
            work_item_id = notification_record["work_item_id"]
            notification_id = notification_record["notification_id"]
            
            log_structured(
                logger,
                "info",
                f"Handling timeout for notification {notification_id}",
                extra={
                    "work_item_id": work_item_id,
                    "notification_id": notification_id
                }
            )
            
            # Mark as timeout handled
            self._notification_store[notification_id]["timeout_handled"] = True
            self._notification_store[notification_id]["status"] = "timed_out"
            self._notification_store[notification_id]["timeout_handled_at"] = datetime.utcnow().isoformat()
            
            # Option 1: Send follow-up notification
            await self._send_timeout_follow_up(work_item_id, notification_record)
            
            # Option 2: Apply default action (e.g., assign to default team member)
            await self._apply_default_timeout_action(work_item_id, notification_record)
            
            # Option 3: Log for manual review
            await self._log_for_manual_review(work_item_id, notification_record)
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to handle notification timeout: {e}",
                extra={"notification_record": notification_record},
                exc_info=True
            )
            return False
    
    async def _send_timeout_follow_up(self, work_item_id: int, notification_record: Dict[str, Any]) -> bool:
        """Send a follow-up notification after timeout."""
        try:
            # Get work item details
            work_item = await self.azure_client.get_work_item(work_item_id)
            if not work_item:
                return False
            
            # Send simplified follow-up message
            follow_up_message = (
                f"⏰ **Follow-up: Work Item {work_item_id}**\n\n"
                f"**Title:** {work_item.title}\n"
                f"**Priority:** {work_item.priority}\n\n"
                f"The previous notification timed out. Please review this item.\n\n"
                f"[View Work Item](https://dev.azure.com/your-org/your-project/_workitems/edit/{work_item_id})"
            )
            
            success = await self.teams_client.send_simple_message(
                recipient="QA Team",
                subject=f"Follow-up: Work Item {work_item_id}",
                message=follow_up_message
            )
            
            if success:
                log_structured(
                    logger,
                    "info",
                    f"Sent timeout follow-up for work item {work_item_id}"
                )
            
            return success
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send timeout follow-up: {e}",
                extra={"work_item_id": work_item_id},
                exc_info=True
            )
            return False
    
    async def _apply_default_timeout_action(self, work_item_id: int, notification_record: Dict[str, Any]) -> bool:
        """Apply default action when notification times out."""
        try:
            # Get default assignee from config
            default_assignee = self.config.get('DEFAULT_TIMEOUT_ASSIGNEE', '<EMAIL>')
            
            # Update work item with default assignment
            update_data = {
                "System.AssignedTo": default_assignee,
                "System.Tags": "auto-assigned-timeout",
                "System.History": f"Auto-assigned due to notification timeout. Original notification ID: {notification_record['notification_id']}"
            }
            
            success = await self.azure_client.update_work_item(work_item_id, update_data)
            
            if success:
                log_structured(
                    logger,
                    "info",
                    f"Applied default timeout action for work item {work_item_id}",
                    extra={
                        "work_item_id": work_item_id,
                        "default_assignee": default_assignee
                    }
                )
            
            return success
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to apply default timeout action: {e}",
                extra={"work_item_id": work_item_id},
                exc_info=True
            )
            return False
    
    async def _log_for_manual_review(self, work_item_id: int, notification_record: Dict[str, Any]) -> bool:
        """Log timeout for manual review."""
        try:
            log_structured(
                logger,
                "warning",
                f"Notification timeout requires manual review",
                extra={
                    "work_item_id": work_item_id,
                    "notification_id": notification_record["notification_id"],
                    "sent_timestamp": notification_record["sent_timestamp"],
                    "timeout_action": "manual_review_required"
                }
            )
            
            return True
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to log for manual review: {e}",
                extra={"work_item_id": work_item_id},
                exc_info=True
            )
            return False
    
    async def mark_notification_responded(self, notification_id: str) -> bool:
        """Mark a notification as responded to."""
        try:
            if notification_id in self._notification_store:
                self._notification_store[notification_id]["status"] = "responded"
                self._notification_store[notification_id]["responded_at"] = datetime.utcnow().isoformat()
                
                log_structured(
                    logger,
                    "info",
                    f"Marked notification {notification_id} as responded"
                )
                return True
            
            return False
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to mark notification as responded: {e}",
                extra={"notification_id": notification_id},
                exc_info=True
            )
            return False
