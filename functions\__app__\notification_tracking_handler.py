"""
Notification Tracking Handler
Handles tracking of sent notifications and their responses for timeout management.
"""

import json
import logging
import azure.functions as func
from typing import Dict, Any, Optional
from datetime import datetime, timedelta

from .common.utils.config import get_config
from .common.utils.logging import setup_logging, log_structured
from .common.services.notification_tracking_service import NotificationTrackingService

# Initialize logging
setup_logging()
logger = logging.getLogger(__name__)


async def store_notification_tracking(req: func.HttpRequest) -> func.HttpResponse:
    """
    Store notification tracking information for timeout management.
    
    Expected payload:
    {
        "work_item_id": 12345,
        "notification_id": "teams-msg-id",
        "teams_message_id": "teams-msg-id",
        "sent_timestamp": "2024-01-01T12:00:00Z",
        "status": "sent"
    }
    """
    try:
        log_structured(
            logger,
            "info",
            "Processing notification tracking storage request"
        )

        # Parse request body
        try:
            request_data = req.get_json()
        except ValueError as e:
            log_structured(
                logger,
                "error",
                f"Invalid JSON in notification tracking request: {e}"
            )
            return func.HttpResponse(
                json.dumps({"error": "Invalid JSON payload"}),
                status_code=400,
                mimetype="application/json"
            )

        if not request_data:
            return func.HttpResponse(
                json.dumps({"error": "Empty payload"}),
                status_code=400,
                mimetype="application/json"
            )

        # Extract required fields
        work_item_id = request_data.get("work_item_id")
        notification_id = request_data.get("notification_id")
        teams_message_id = request_data.get("teams_message_id")
        sent_timestamp = request_data.get("sent_timestamp")
        status = request_data.get("status", "sent")

        if not all([work_item_id, notification_id]):
            return func.HttpResponse(
                json.dumps({"error": "Missing required fields: work_item_id, notification_id"}),
                status_code=400,
                mimetype="application/json"
            )

        # Initialize tracking service
        config = get_config()
        tracking_service = NotificationTrackingService(config)

        # Store notification tracking
        success = await tracking_service.store_notification(
            work_item_id=work_item_id,
            notification_id=notification_id,
            teams_message_id=teams_message_id,
            sent_timestamp=sent_timestamp,
            status=status
        )

        if success:
            log_structured(
                logger,
                "info",
                f"Stored notification tracking for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "notification_id": notification_id,
                    "status": status
                }
            )

            return func.HttpResponse(
                json.dumps({
                    "status": "success",
                    "message": "Notification tracking stored successfully",
                    "work_item_id": work_item_id,
                    "notification_id": notification_id
                }),
                status_code=200,
                mimetype="application/json"
            )
        else:
            return func.HttpResponse(
                json.dumps({
                    "status": "error",
                    "message": "Failed to store notification tracking"
                }),
                status_code=500,
                mimetype="application/json"
            )

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing notification tracking storage: {e}",
            exc_info=True
        )

        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "message": f"Internal server error: {str(e)}"
            }),
            status_code=500,
            mimetype="application/json"
        )


async def check_notification_timeouts(req: func.HttpRequest) -> func.HttpResponse:
    """
    Check for notifications that have timed out and handle them appropriately.
    This can be called by a timer trigger or manually.
    """
    try:
        log_structured(
            logger,
            "info",
            "Checking for notification timeouts"
        )

        # Initialize tracking service
        config = get_config()
        tracking_service = NotificationTrackingService(config)

        # Get timeout threshold from config (default 30 minutes)
        timeout_minutes = int(config.get('NOTIFICATION_TIMEOUT_MINUTES', 30))
        
        # Check for timed out notifications
        timed_out_notifications = await tracking_service.get_timed_out_notifications(
            timeout_minutes=timeout_minutes
        )

        processed_count = 0
        for notification in timed_out_notifications:
            try:
                # Handle timeout for this notification
                success = await tracking_service.handle_notification_timeout(notification)
                if success:
                    processed_count += 1
                    
                    log_structured(
                        logger,
                        "info",
                        f"Handled timeout for notification {notification['notification_id']}",
                        extra={
                            "work_item_id": notification["work_item_id"],
                            "notification_id": notification["notification_id"]
                        }
                    )
            except Exception as e:
                log_structured(
                    logger,
                    "error",
                    f"Error handling timeout for notification {notification.get('notification_id')}: {e}",
                    extra={"notification": notification},
                    exc_info=True
                )

        log_structured(
            logger,
            "info",
            f"Processed {processed_count} timed out notifications",
            extra={
                "total_found": len(timed_out_notifications),
                "processed": processed_count
            }
        )

        return func.HttpResponse(
            json.dumps({
                "status": "success",
                "message": f"Processed {processed_count} timed out notifications",
                "total_found": len(timed_out_notifications),
                "processed": processed_count
            }),
            status_code=200,
            mimetype="application/json"
        )

    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error checking notification timeouts: {e}",
            exc_info=True
        )

        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "message": f"Internal server error: {str(e)}"
            }),
            status_code=500,
            mimetype="application/json"
        )
