#!/usr/bin/env python3
"""
Test script for work item 748404 using the refactored AutoDefectTriage system.
This script tests the new event-driven architecture and safety guardrails.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.common.utils.config import get_config
from __app__.common.utils.logging import log_structured
from __app__.common.adapters.ado_client import AdoClient
from __app__.common.vectorstore import VectorStoreFactory
from __app__.common.services.store import IdempotencyStore
from __app__.common.services.history import HistoryService
from __app__.common.services.triage import TriageService
from __app__.common.services.ownership import OwnershipService
from __app__.common.services.notify import NotificationService
from __app__.common.adapters.teams_client import <PERSON>Client
from __app__.workitem_event.handler import workitem_event_handler

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_work_item_748404():
    """Test the complete triage pipeline with work item 748404."""
    
    print("🚀 Testing AutoDefectTriage with Work Item 748404")
    print("=" * 60)
    
    try:
        # 1. Test Configuration
        print("\n1️⃣ Testing Configuration...")
        config = get_config()
        
        print(f"   ✅ Safety Only Work Item ID: {config.SAFETY_ONLY_WORKITEM_ID}")
        print(f"   ✅ Read Only Mode: {config.READ_ONLY}")
        print(f"   ✅ Allow Comments in Read Only: {config.ALLOW_COMMENTS_IN_READ_ONLY}")
        print(f"   ✅ Allowed Projects: {config.get_allowed_projects()}")
        print(f"   ✅ Vector Backend: {config.VECTOR_BACKEND}")
        print(f"   ✅ Teams Mode: {config.get_teams_mode()}")
        
        # Verify work item 748404 is allowed
        if not config.is_work_item_allowed(748404):
            print("   ❌ Work item 748404 is not allowed by current safety settings!")
            return False
        else:
            print("   ✅ Work item 748404 is allowed by safety settings")
        
        # 2. Test ADO Client
        print("\n2️⃣ Testing ADO Client...")
        ado_client = AdoClient(config)
        
        work_item = await ado_client.get_work_item(748404)
        if not work_item:
            print("   ❌ Failed to fetch work item 748404")
            return False
        
        print(f"   ✅ Fetched work item 748404: {work_item.get('fields', {}).get('System.Title', 'Unknown')}")
        print(f"   📋 State: {work_item.get('fields', {}).get('System.State', 'Unknown')}")
        print(f"   👤 Assigned To: {work_item.get('fields', {}).get('System.AssignedTo', {}).get('displayName', 'Unassigned')}")
        print(f"   ⚡ Priority: {work_item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 'Unknown')}")
        
        # 3. Test Vector Store
        print("\n3️⃣ Testing Vector Store...")
        vector_store = VectorStoreFactory.create_vector_store(config)
        
        health_check = await vector_store.health_check()
        print(f"   📊 Vector Store Health: {health_check.get('status', 'unknown')}")
        print(f"   🔍 Backend: {health_check.get('backend', 'unknown')}")
        print(f"   📈 Document Count: {health_check.get('document_count', 0)}")
        
        if health_check.get('status') != 'healthy':
            print("   ⚠️ Vector store is not healthy, but continuing test...")
        
        # 4. Test Event Handler (Simulated ADO Service Hook)
        print("\n4️⃣ Testing Event Handler...")
        
        # Create a simulated ADO Service Hook event
        mock_event = {
            "eventType": "workitem.updated",
            "resource": {
                "id": 748404,
                "rev": 42,
                "fields": work_item.get('fields', {}),
                "url": f"https://dev.azure.com/{config.ADO_ORG}/{config.ADO_PROJECT}/_apis/wit/workItems/748404"
            },
            "resourceContainers": {
                "project": {
                    "id": "test-project-id"
                }
            }
        }
        
        # Test the event handler
        event_result = await workitem_event_handler(mock_event)
        print(f"   📨 Event Handler Result: {event_result.get('status', 'unknown')}")
        
        if event_result.get('status') == 'success':
            print("   ✅ Event processed successfully")
        else:
            print(f"   ⚠️ Event processing: {event_result.get('message', 'Unknown result')}")
        
        # 5. Test Individual Services
        print("\n5️⃣ Testing Individual Services...")
        
        # Test History Service
        print("   🔍 Testing History Service...")
        history_service = HistoryService(vector_store, ado_client, config)
        
        # For testing, we'll use a dummy vector (in real scenario, this would come from embedding service)
        dummy_vector = [0.1] * 1536  # Typical embedding dimension
        
        similar_items = await history_service.find_similar_items(
            work_item=work_item,
            query_vector=dummy_vector,
            k=5
        )
        print(f"      📊 Found {len(similar_items)} similar items")
        
        # Test Triage Service
        print("   🎯 Testing Triage Service...")
        triage_service = TriageService(ado_client, config)
        
        triage_results = await triage_service.get_triage_recommendations(
            work_item=work_item,
            similar_items=similar_items
        )
        
        assignee_suggestions = triage_results.get('assignee_suggestions', [])
        priority_suggestion = triage_results.get('priority_suggestion', {})
        
        print(f"      👥 Assignee suggestions: {len(assignee_suggestions)}")
        if assignee_suggestions:
            top_suggestion = assignee_suggestions[0]
            print(f"         🥇 Top: {top_suggestion.get('assignee', 'Unknown')} ({top_suggestion.get('confidence', 0):.2f} confidence)")
        
        print(f"      ⚡ Priority suggestion: {priority_suggestion.get('priority', 'Unknown')} ({priority_suggestion.get('confidence', 0):.2f} confidence)")
        
        # Test Ownership Service
        print("   👑 Testing Ownership Service...")
        ownership_service = OwnershipService(ado_client, config)
        
        if assignee_suggestions:
            candidates = [s['assignee'] for s in assignee_suggestions[:3]]
            ownership_summary = await ownership_service.get_ownership_summary(
                work_item=work_item,
                assignee_candidates=candidates
            )
            print(f"      📋 Ownership analysis for {len(candidates)} candidates")
            print(f"      🏠 Area owners: {len(ownership_summary.get('area_owners', []))}")
        
        # 6. Test Teams Notification (if enabled)
        print("\n6️⃣ Testing Teams Notification...")
        
        if config.TEAMS_NOTIFICATIONS_ENABLED:
            teams_client = TeamsClient(config)
            notify_service = NotificationService(teams_client, ado_client, config)
            
            # Get historical context
            historical_context = await history_service.get_historical_context(
                work_item=work_item,
                query_vector=dummy_vector
            )
            
            # Test notification (in read-only mode, this should be safe)
            notification_result = await notify_service.send_triage_notification(
                work_item=work_item,
                triage_results=triage_results,
                historical_context=historical_context
            )
            
            print(f"   📱 Teams notification sent: {notification_result.get('teams_sent', False)}")
            print(f"   💬 ADO comment added: {notification_result.get('ado_comment_added', False)}")
            
            if notification_result.get('errors'):
                print(f"   ⚠️ Notification errors: {notification_result['errors']}")
        else:
            print("   ⏭️ Teams notifications disabled")
        
        # 7. Test Idempotency
        print("\n7️⃣ Testing Idempotency...")
        store = IdempotencyStore(config)
        
        idempotency_key = f"748404:42"  # work_item_id:revision
        
        # Check if already processed
        already_processed = await store.is_processed(idempotency_key)
        print(f"   🔄 Already processed: {already_processed}")
        
        if not already_processed:
            await store.mark_processing(idempotency_key)
            print("   ⏳ Marked as processing")
            
            # Simulate successful processing
            await store.mark_completed(idempotency_key, {"status": "test_completed"})
            print("   ✅ Marked as completed")
        
        # Test duplicate processing
        duplicate_processed = await store.is_processed(idempotency_key)
        print(f"   🔄 Now shows as processed: {duplicate_processed}")
        
        print("\n🎉 All Tests Completed Successfully!")
        print("=" * 60)
        
        # Summary
        print("\n📊 Test Summary:")
        print(f"   • Work Item: 748404 - {work_item.get('fields', {}).get('System.Title', 'Unknown')}")
        print(f"   • Safety Mode: READ_ONLY={config.READ_ONLY}")
        print(f"   • Similar Items Found: {len(similar_items)}")
        print(f"   • Assignee Suggestions: {len(assignee_suggestions)}")
        print(f"   • Teams Notification: {config.TEAMS_NOTIFICATIONS_ENABLED}")
        print(f"   • Vector Store: {config.VECTOR_BACKEND} ({health_check.get('status', 'unknown')})")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def test_legacy_endpoint():
    """Test the legacy endpoint with work item 748404."""
    
    print("\n🔄 Testing Legacy Endpoint...")
    print("-" * 40)
    
    try:
        # Import the legacy handler
        from __app__.workitem_created.handler import main as legacy_handler
        
        # Create a mock HTTP request for the legacy endpoint
        class MockHttpRequest:
            def __init__(self, work_item_id):
                self.params = {'work_item_id': str(work_item_id)}
                self.method = 'GET'
            
            def get_json(self):
                return None
        
        mock_request = MockHttpRequest(748404)
        
        # Test the legacy handler
        response = await legacy_handler(mock_request)
        
        print(f"   📨 Legacy Handler Response Status: {response.status_code}")
        
        if response.status_code == 200:
            response_data = json.loads(response.get_body().decode())
            print(f"   ✅ Legacy handler result: {response_data.get('status', 'unknown')}")
            print(f"   📝 Message: {response_data.get('message', 'No message')}")
        else:
            print(f"   ⚠️ Legacy handler returned status {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Legacy endpoint test failed: {e}")
        return False


async def main():
    """Main test function."""
    
    print("🧪 AutoDefectTriage Work Item 748404 Test Suite")
    print("=" * 60)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Test the new refactored system
    new_system_success = await test_work_item_748404()
    
    # Test the legacy endpoint for compatibility
    legacy_success = await test_legacy_endpoint()
    
    print("\n" + "=" * 60)
    print("🏁 Test Suite Complete")
    print(f"   New System: {'✅ PASS' if new_system_success else '❌ FAIL'}")
    print(f"   Legacy Compatibility: {'✅ PASS' if legacy_success else '❌ FAIL'}")
    
    if new_system_success and legacy_success:
        print("\n🎉 All tests passed! Work item 748404 can be safely processed.")
    else:
        print("\n⚠️ Some tests failed. Please review the output above.")
    
    return new_system_success and legacy_success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
