"""
Contextual Message Generator for Intelligent Notifications.
Generates intelligent, actionable notification content using AI engines.
"""

import logging
from typing import List, Dict, Any, Optional, TYPE_CHECKING
from datetime import datetime, timedelta

if TYPE_CHECKING:
    from ..models import (
        NotificationContext,
        NotificationTrigger,
        AssignmentSuggestion,
        SimilarWorkItem,
        DuplicateCandidate,
        PriorityAnalysis,
        TriggerType,
        DeliveryMethod
    )
from ..models.schemas import WorkItem, SearchResult
from ..ai.duplicate import DuplicateDetector
from ..ai.assigner import AssignmentEngine
from ..ai.priority import PriorityEngine
from ..adapters.search_client import SearchClient
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class MessageGenerator:
    """
    Contextual message generator that creates intelligent notification content.
    
    Uses existing AI engines to provide:
    - Contextual explanations of why notification is sent
    - Assignment suggestions with reasoning
    - Priority analysis and recommendations
    - Duplicate detection results
    - Similar historical items and resolutions
    - Recommended next steps
    """
    
    def __init__(
        self,
        config: Config,
        duplicate_detector: DuplicateDetector,
        assignment_engine: AssignmentEngine,
        priority_engine: PriorityEngine,
        search_client: SearchClient
    ):
        self.config = config
        self.duplicate_detector = duplicate_detector
        self.assignment_engine = assignment_engine
        self.priority_engine = priority_engine
        self.search_client = search_client
        
    async def generate_notification_context(
        self,
        work_item: WorkItem,
        trigger: "NotificationTrigger"
    ) -> "NotificationContext":
        """
        Generate complete notification context with AI-powered insights.
        
        Args:
            work_item: Work item to generate context for
            trigger: Notification trigger
        
        Returns:
            Complete notification context
        """
        # Import here to avoid circular dependencies
        from ..models import NotificationContext

        try:
            log_structured(
                logger,
                "info",
                f"Generating notification context for work item {work_item.id}",
                extra={
                    "work_item_id": work_item.id,
                    "trigger_type": trigger.trigger_type
                }
            )
            
            # Run AI analysis in parallel where possible
            suggested_assignees = await self._get_assignment_suggestions(work_item)
            similar_items = await self._get_similar_items(work_item)
            duplicate_candidates = await self._get_duplicate_candidates(work_item)
            priority_analysis = await self._get_priority_analysis(work_item)
            
            # Generate contextual message
            message_content = self._generate_message_content(
                work_item, trigger, suggested_assignees, similar_items, 
                duplicate_candidates, priority_analysis
            )
            
            context = NotificationContext(
                work_item=work_item,
                trigger=trigger,
                suggested_assignees=suggested_assignees,
                similar_items=similar_items,
                priority_analysis=priority_analysis,
                duplicate_candidates=duplicate_candidates,
                message_content=message_content
            )
            
            log_structured(
                logger,
                "info",
                f"Generated notification context successfully",
                extra={
                    "work_item_id": work_item.id,
                    "suggested_assignees_count": len(suggested_assignees),
                    "similar_items_count": len(similar_items),
                    "duplicate_candidates_count": len(duplicate_candidates)
                }
            )
            
            return context
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error generating notification context: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            
            # Return minimal context on error
            return NotificationContext(
                work_item=work_item,
                trigger=trigger,
                message_content=f"Work item {work_item.id}: {work_item.title}"
            )
    
    async def _get_assignment_suggestions(self, work_item: WorkItem) -> List["AssignmentSuggestion"]:
        """Get assignment suggestions using the assignment engine with detailed reasoning."""
        try:
            # Get detailed assignment candidates for better reasoning
            candidates = await self.assignment_engine._get_assignment_candidates(work_item)

            suggestions = []
            for candidate in candidates[:3]:  # Top 3 candidates
                # Extract iteration-specific reasoning
                iteration_reasoning = []
                general_reasoning = []

                for reason in candidate.reasoning:
                    if "Iteration history:" in reason or "Bug experience:" in reason or "Task experience:" in reason or "Similar item:" in reason:
                        iteration_reasoning.append(reason)
                    else:
                        general_reasoning.append(reason)

                # Combine reasoning with iteration info first
                combined_reasoning = iteration_reasoning + general_reasoning
                reasoning_text = "; ".join(combined_reasoning) if combined_reasoning else "AI-based assignment suggestion"

                # Extract iteration name if available
                iteration_context = ""
                if work_item.iteration_path:
                    iteration_name = work_item.iteration_path.split('\\')[-1] if '\\' in work_item.iteration_path else work_item.iteration_path
                    iteration_context = f" (Iteration: {iteration_name})"

                suggestion = AssignmentSuggestion(
                    assignee_email=candidate.assignee,
                    assignee_name=candidate.assignee,
                    confidence_score=candidate.confidence_score,
                    reasoning=reasoning_text + iteration_context,
                    expertise_match=[],  # Could be enhanced with expertise areas
                    historical_success=0.8,  # Default value
                    current_load=0,  # Would be populated from team data
                    availability=1.0
                )
                suggestions.append(suggestion)

            log_structured(
                logger,
                "info",
                f"Generated {len(suggestions)} assignment suggestions with iteration context",
                extra={
                    "work_item_id": work_item.id,
                    "iteration_path": work_item.iteration_path,
                    "top_candidate": suggestions[0].assignee_name if suggestions else None,
                    "top_confidence": suggestions[0].confidence_score if suggestions else None
                }
            )

            return suggestions

        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error getting assignment suggestions: {e}",
                extra={"work_item_id": work_item.id}
            )
            return []
    
    async def _get_similar_items(self, work_item: WorkItem) -> List["SimilarWorkItem"]:
        """Get similar historical items using search."""
        try:
            # Search for similar items
            search_results = await self.search_client.search_similar_items(
                work_item,
                max_results=5,
                include_closed=True
            )
            
            similar_items = []
            for result in search_results:
                similar_item = SimilarWorkItem(
                    work_item_id=result.work_item_id,
                    title=result.title,
                    similarity_score=result.similarity_score,
                    resolution=result.resolution,
                    assignee=result.assignee,
                    resolution_time=result.resolution_time,
                    tags=result.tags or []
                )
                similar_items.append(similar_item)
            
            return similar_items
            
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error getting similar items: {e}",
                extra={"work_item_id": work_item.id}
            )
            return []
    
    async def _get_duplicate_candidates(self, work_item: WorkItem) -> List["DuplicateCandidate"]:
        """Get duplicate candidates using duplicate detector."""
        try:
            # Use existing duplicate detector
            duplicate_results = await self.duplicate_detector.find_duplicates(work_item)
            
            candidates = []
            for duplicate in duplicate_results:
                candidate = DuplicateCandidate(
                    work_item_id=duplicate.work_item_id,
                    title=duplicate.title,
                    similarity_score=duplicate.similarity_score,
                    state=duplicate.state,
                    assignee=duplicate.assignee,
                    created_date=duplicate.created_date,
                    duplicate_type=duplicate.duplicate_type or "content"
                )
                candidates.append(candidate)
            
            return candidates
            
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error getting duplicate candidates: {e}",
                extra={"work_item_id": work_item.id}
            )
            return []
    
    async def _get_priority_analysis(self, work_item: WorkItem) -> Optional["PriorityAnalysis"]:
        """Get priority analysis using priority engine."""
        try:
            # Use existing priority engine
            priority_result = await self.priority_engine.analyze_priority(work_item)
            
            if priority_result:
                analysis = PriorityAnalysis(
                    suggested_priority=priority_result.suggested_priority,
                    confidence_score=priority_result.confidence_score,
                    factors=priority_result.factors or {},
                    reasoning=priority_result.reasoning or "AI-based priority analysis",
                    customer_impact=priority_result.customer_impact or False,
                    security_impact=priority_result.security_impact or False,
                    blocking_impact=priority_result.blocking_impact or False
                )
                return analysis
            
            return None
            
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"Error getting priority analysis: {e}",
                extra={"work_item_id": work_item.id}
            )
            return None
    
    def _generate_message_content(
        self,
        work_item: WorkItem,
        trigger: "NotificationTrigger",
        suggested_assignees: List["AssignmentSuggestion"],
        similar_items: List["SimilarWorkItem"],
        duplicate_candidates: List["DuplicateCandidate"],
        priority_analysis: Optional["PriorityAnalysis"]
    ) -> str:
        """Generate contextual message content."""
        
        # Start with trigger explanation
        message_parts = [self._get_trigger_explanation(work_item, trigger)]
        
        # Add work item summary
        message_parts.append(self._get_work_item_summary(work_item))
        
        # Add priority analysis if available
        if priority_analysis:
            message_parts.append(self._get_priority_section(priority_analysis))
        
        # Add assignment suggestions
        if suggested_assignees:
            message_parts.append(self._get_assignment_section(suggested_assignees))
        
        # Add duplicate information
        if duplicate_candidates:
            message_parts.append(self._get_duplicate_section(duplicate_candidates))
        
        # Add similar items for context
        if similar_items:
            message_parts.append(self._get_similar_items_section(similar_items))
        
        # Add recommended next steps
        message_parts.append(self._get_next_steps_section(work_item, trigger))
        
        return "\n\n".join(message_parts)
    
    def _get_trigger_explanation(self, work_item: WorkItem, trigger: "NotificationTrigger") -> str:
        """Generate explanation of why notification was triggered."""
        # Import here to avoid circular dependencies
        from ..models import TriggerType

        if trigger.trigger_type == TriggerType.CRITICAL_CREATED:
            return f"🚨 **Critical Work Item Alert**\nThis work item requires immediate attention due to its critical priority and impact."
        
        elif trigger.trigger_type == TriggerType.AGING:
            age_hours = trigger.trigger_conditions.get("age_hours", 0)
            threshold_hours = trigger.trigger_conditions.get("threshold_hours", 0)
            return f"⏰ **Aging Work Item Alert**\nThis work item has been open for {age_hours:.1f} hours, exceeding the {threshold_hours:.1f} hour threshold."
        
        elif trigger.trigger_type == TriggerType.SECURITY_ALERT:
            return f"🔒 **Security Alert**\nThis work item contains security-related content that requires immediate review."
        
        elif trigger.trigger_type == TriggerType.ESCALATION:
            return f"📈 **Escalation Alert**\nThis work item has been escalated due to lack of progress or assignment."
        
        else:
            return f"📋 **Work Item Notification**\nThis work item requires your attention."
    
    def _get_work_item_summary(self, work_item: WorkItem) -> str:
        """Generate work item summary."""
        summary = f"**Work Item #{work_item.id}**: {work_item.title}\n"
        summary += f"**Priority**: P{work_item.priority}\n"
        summary += f"**State**: {work_item.state}\n"
        
        if work_item.assigned_to:
            summary += f"**Assigned to**: {work_item.assigned_to}\n"
        else:
            summary += f"**Status**: Unassigned\n"
        
        if work_item.created_date:
            age = datetime.utcnow() - work_item.created_date
            summary += f"**Age**: {age.days} days\n"
        
        return summary
    
    def _get_priority_section(self, priority_analysis: "PriorityAnalysis") -> str:
        """Generate priority analysis section."""
        section = f"**🎯 Priority Analysis**\n"
        section += f"Suggested Priority: P{priority_analysis.suggested_priority} (Confidence: {priority_analysis.confidence_score:.1%})\n"
        
        if priority_analysis.reasoning:
            section += f"Reasoning: {priority_analysis.reasoning}\n"
        
        impacts = []
        if priority_analysis.customer_impact:
            impacts.append("Customer Impact")
        if priority_analysis.security_impact:
            impacts.append("Security Impact")
        if priority_analysis.blocking_impact:
            impacts.append("Blocking Impact")
        
        if impacts:
            section += f"Impact Areas: {', '.join(impacts)}\n"
        
        return section
    
    def _get_assignment_section(self, suggested_assignees: List["AssignmentSuggestion"]) -> str:
        """Generate assignment suggestions section with iteration context."""
        section = "**👤 Assignment Suggestions**\n"

        for i, suggestion in enumerate(suggested_assignees[:3], 1):  # Top 3 suggestions
            section += f"{i}. **{suggestion.assignee_name}** (Confidence: {suggestion.confidence_score:.1%})\n"

            if suggestion.reasoning:
                # Parse reasoning to highlight iteration-based insights
                reasoning_parts = suggestion.reasoning.split(';')
                iteration_parts = []
                other_parts = []

                for part in reasoning_parts:
                    part = part.strip()
                    if "Iteration history:" in part or "Bug experience:" in part or "Task experience:" in part:
                        iteration_parts.append(part)
                    elif "Similar item:" in part:
                        # Format similar items nicely
                        other_parts.append(f"   📋 {part}")
                    else:
                        other_parts.append(f"   • {part}")

                # Show iteration context first if available
                if iteration_parts:
                    section += f"   🔄 **Iteration Experience**: {'; '.join(iteration_parts)}\n"

                # Show other reasoning
                if other_parts:
                    for part in other_parts[:3]:  # Limit to 3 additional reasons
                        section += f"{part}\n"
                section += f"   Reason: {suggestion.reasoning}\n"
            if suggestion.expertise_match:
                section += f"   Expertise: {', '.join(suggestion.expertise_match)}\n"
        
        return section
    
    def _get_duplicate_section(self, duplicate_candidates: List["DuplicateCandidate"]) -> str:
        """Generate duplicate candidates section."""
        section = "**🔍 Potential Duplicates**\n"
        
        for candidate in duplicate_candidates[:3]:  # Top 3 duplicates
            section += f"• **#{candidate.work_item_id}**: {candidate.title} "
            section += f"(Similarity: {candidate.similarity_score:.1%}, State: {candidate.state})\n"
        
        return section
    
    def _get_similar_items_section(self, similar_items: List["SimilarWorkItem"]) -> str:
        """Generate similar items section."""
        section = "**📚 Similar Historical Items**\n"
        
        for item in similar_items[:3]:  # Top 3 similar items
            section += f"• **#{item.work_item_id}**: {item.title} "
            section += f"(Similarity: {item.similarity_score:.1%})\n"
            
            if item.resolution:
                section += f"  Resolution: {item.resolution}\n"
            if item.assignee:
                section += f"  Resolved by: {item.assignee}\n"
            if item.resolution_time:
                days = item.resolution_time.days
                section += f"  Resolution time: {days} days\n"
        
        return section
    
    def _get_next_steps_section(self, work_item: WorkItem, trigger: "NotificationTrigger") -> str:
        """Generate recommended next steps."""
        section = "**🎯 Recommended Next Steps**\n"
        
        if not work_item.assigned_to:
            section += "• Assign to appropriate team member\n"
        
        if trigger.trigger_type == TriggerType.CRITICAL_CREATED:
            section += "• Review and triage immediately\n"
            section += "• Assess impact and priority\n"
            section += "• Communicate with stakeholders\n"
        
        elif trigger.trigger_type == TriggerType.AGING:
            section += "• Review current status and progress\n"
            section += "• Update work item with latest information\n"
            section += "• Consider reassignment if needed\n"
        
        elif trigger.trigger_type == TriggerType.SECURITY_ALERT:
            section += "• Conduct security assessment\n"
            section += "• Involve security team if needed\n"
            section += "• Document security implications\n"
        
        section += "• Update work item status and comments\n"
        
        return section
