"""
Aging Issues Summary Email Service
Generates and sends summary emails for aging work items that need attention.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from collections import defaultdict

from ..models.schemas import WorkItem
from ..adapters.ado_client import AdoClient
from ..utils.config import Config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class AgingIssueSummary:
    """Data class for aging issue summary statistics."""
    
    def __init__(self):
        self.total_aging_items = 0
        self.critical_aging_items = 0
        self.high_aging_items = 0
        self.medium_aging_items = 0
        self.low_aging_items = 0
        self.unassigned_aging_items = 0
        self.oldest_item_age_days = 0
        self.oldest_item_id = None
        self.oldest_item_title = ""
        self.aging_by_area = {}
        self.aging_by_assignee = {}
        self.aging_items_by_priority = {}


class AgingSummaryEmailService:
    """Service for generating and sending aging issues summary emails."""
    
    def __init__(self, config: Config):
        self.config = config
        self.ado_client = AdoClient(config)
        
        # Email configuration
        self.email_logic_app_url = getattr(config, 'Email_LOGIC_APP_URL', None)

        # Parse recipients from config (can be string or list)
        recipients_config = getattr(config, 'AGING_SUMMARY_RECIPIENTS', '<EMAIL>,<EMAIL>')
        if isinstance(recipients_config, str):
            # Split by comma and clean up
            self.default_recipients = [email.strip() for email in recipients_config.split(',') if email.strip()]
        else:
            self.default_recipients = recipients_config
        
        # Aging thresholds (in hours)
        self.aging_thresholds = {
            1: 4,    # P1: 4 hours
            2: 24,   # P2: 24 hours  
            3: 72,   # P3: 72 hours
            4: 168   # P4: 168 hours (1 week)
        }
    
    async def generate_and_send_aging_summary(self, recipients: Optional[List[str]] = None) -> bool:
        """
        Generate and send aging issues summary email.
        
        Args:
            recipients: List of email recipients. If None, uses default recipients.
            
        Returns:
            True if email was sent successfully, False otherwise
        """
        try:
            log_structured(
                logger,
                "info",
                "Starting aging issues summary email generation"
            )
            
            # Get aging work items
            aging_items = await self._get_aging_work_items()
            
            if not aging_items:
                log_structured(
                    logger,
                    "info",
                    "No aging work items found - sending 'all clear' email"
                )
                return await self._send_all_clear_email(recipients)
            
            # Generate summary statistics
            summary = self._generate_aging_summary(aging_items)
            
            # Generate HTML email content
            email_html = self._generate_email_html(summary, aging_items)
            
            # Send email
            return await self._send_summary_email(email_html, summary, recipients)
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error generating aging summary email: {e}",
                exc_info=True
            )
            return False
    
    async def _get_aging_work_items(self) -> List[WorkItem]:
        """Get work items that are aging beyond their thresholds."""
        try:
            # Calculate time thresholds
            now = datetime.utcnow()
            
            # WIQL query with date filter to avoid 20000 item limit
            # Focus on work items from the last 6 months (most likely to be aging)
            six_months_ago = (now - timedelta(days=180)).strftime('%Y-%m-%d')

            wiql_query = f"""
            SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State],
                   [System.AssignedTo], [System.CreatedDate], [Microsoft.VSTS.Common.Priority],
                   [System.AreaPath], [System.Description]
            FROM WorkItems
            WHERE [System.WorkItemType] IN ('Bug', 'Epic', 'Feature', 'Story', 'User Story', 'Task')
              AND [System.State] IN ('New', 'Active', 'Committed', 'In Progress', 'To Do')
              AND [System.CreatedDate] >= '{six_months_ago}'
            ORDER BY [System.CreatedDate] DESC
            """
            
            work_items_data = await self.ado_client.query_work_items(wiql_query)
            
            # Convert to WorkItem objects and filter for aging
            aging_items = []
            for item_data in work_items_data:
                try:
                    work_item = self._convert_ado_to_work_item(item_data)
                    
                    # Check if item is aging beyond threshold
                    if self._is_aging_beyond_threshold(work_item):
                        aging_items.append(work_item)
                        
                except Exception as e:
                    log_structured(
                        logger,
                        "warning",
                        f"Error processing work item {item_data.get('id', 'unknown')}: {e}"
                    )
                    continue
            
            log_structured(
                logger,
                "info",
                f"Found {len(aging_items)} aging work items out of {len(work_items_data)} total open items"
            )
            
            return aging_items
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error getting aging work items: {e}",
                exc_info=True
            )
            return []
    
    def _convert_ado_to_work_item(self, ado_work_item: Dict[str, Any]) -> WorkItem:
        """Convert ADO work item to WorkItem object."""
        fields = ado_work_item.get("fields", {})
        
        # Parse dates
        created_date = None
        if fields.get("System.CreatedDate"):
            try:
                created_date = datetime.fromisoformat(fields["System.CreatedDate"].replace('Z', '+00:00')).replace(tzinfo=None)
            except:
                pass
        
        changed_date = None
        if fields.get("System.ChangedDate"):
            try:
                changed_date = datetime.fromisoformat(fields["System.ChangedDate"].replace('Z', '+00:00')).replace(tzinfo=None)
            except:
                pass
        
        return WorkItem(
            id=ado_work_item.get("id", 0),
            title=fields.get("System.Title", ""),
            description=fields.get("System.Description", ""),
            work_item_type=fields.get("System.WorkItemType", ""),
            state=fields.get("System.State", ""),
            assigned_to=fields.get("System.AssignedTo", {}).get("displayName", "") if fields.get("System.AssignedTo") else "",
            priority=int(fields.get("Microsoft.VSTS.Common.Priority", 3)),
            severity=fields.get("Microsoft.VSTS.Common.Severity", ""),
            area_path=fields.get("System.AreaPath", ""),
            iteration_path=fields.get("System.IterationPath", ""),
            created_date=created_date.isoformat() if created_date else None,
            changed_date=changed_date.isoformat() if changed_date else None,
            tags=fields.get("System.Tags", ""),
            repro_steps=fields.get("Microsoft.VSTS.TCM.ReproSteps", ""),
            system_info=fields.get("Microsoft.VSTS.TCM.SystemInfo", "")
        )
    
    def _is_aging_beyond_threshold(self, work_item: WorkItem) -> bool:
        """Check if work item is aging beyond its priority threshold."""
        if not work_item.created_date:
            return False

        try:
            created_date = datetime.fromisoformat(work_item.created_date)
            age = datetime.utcnow() - created_date
            threshold_hours = self.aging_thresholds.get(work_item.priority, 168)  # Default 1 week
            threshold = timedelta(hours=threshold_hours)

            return age > threshold
        except (ValueError, TypeError):
            return False
    
    def _generate_aging_summary(self, aging_items: List[WorkItem]) -> AgingIssueSummary:
        """Generate summary statistics for aging items."""
        summary = AgingIssueSummary()
        summary.total_aging_items = len(aging_items)
        
        # Initialize counters
        priority_counts = defaultdict(int)
        area_counts = defaultdict(int)
        assignee_counts = defaultdict(int)
        
        oldest_age = timedelta(0)
        
        for item in aging_items:
            # Count by priority
            priority_counts[item.priority] += 1
            
            # Count by area
            area = item.area_path.split('\\')[-1] if item.area_path else "Unknown"
            area_counts[area] += 1
            
            # Count by assignee
            assignee = item.assigned_to if item.assigned_to else "Unassigned"
            assignee_counts[assignee] += 1
            
            # Track oldest item
            if item.created_date:
                try:
                    created_date = datetime.fromisoformat(item.created_date)
                    age = datetime.utcnow() - created_date
                    if age > oldest_age:
                        oldest_age = age
                        summary.oldest_item_age_days = age.days
                        summary.oldest_item_id = item.id
                        summary.oldest_item_title = item.title
                except (ValueError, TypeError):
                    pass
        
        # Set priority counts
        summary.critical_aging_items = priority_counts[1]
        summary.high_aging_items = priority_counts[2]
        summary.medium_aging_items = priority_counts[3]
        summary.low_aging_items = priority_counts[4]
        summary.unassigned_aging_items = assignee_counts["Unassigned"]
        
        # Set dictionaries
        summary.aging_by_area = dict(area_counts)
        summary.aging_by_assignee = dict(assignee_counts)
        summary.aging_items_by_priority = {
            1: [item for item in aging_items if item.priority == 1],
            2: [item for item in aging_items if item.priority == 2],
            3: [item for item in aging_items if item.priority == 3],
            4: [item for item in aging_items if item.priority == 4]
        }
        
        return summary

    def _generate_email_html(self, summary: AgingIssueSummary, aging_items: List[WorkItem]) -> str:
        """Generate HTML email content for aging summary."""

        # Get current date for report
        report_date = datetime.utcnow().strftime('%B %d, %Y')

        # Priority colors and labels
        priority_info = {
            1: {"label": "Critical", "color": "#dc3545", "icon": "🔴"},
            2: {"label": "High", "color": "#fd7e14", "icon": "🟠"},
            3: {"label": "Medium", "color": "#ffc107", "icon": "🟡"},
            4: {"label": "Low", "color": "#28a745", "icon": "🟢"}
        }

        html = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Aging Issues Summary Report</title>
            <style>
                body {{
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                    line-height: 1.6;
                    color: #333;
                    margin: 0;
                    padding: 0;
                    background-color: #f5f5f5;
                }}
                .container {{
                    max-width: 800px;
                    margin: 0 auto;
                    background-color: #ffffff;
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                }}
                .header {{
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    color: white;
                    padding: 30px;
                    text-align: center;
                }}
                .header h1 {{
                    margin: 0;
                    font-size: 28px;
                    font-weight: 300;
                }}
                .header p {{
                    margin: 10px 0 0 0;
                    opacity: 0.9;
                    font-size: 16px;
                }}
                .summary-stats {{
                    padding: 30px;
                    background-color: #f8f9fa;
                    border-bottom: 1px solid #dee2e6;
                }}
                .stats-grid {{
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                    gap: 20px;
                    margin-top: 20px;
                }}
                .stat-card {{
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    text-align: center;
                    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                }}
                .stat-number {{
                    font-size: 32px;
                    font-weight: bold;
                    color: #667eea;
                    margin-bottom: 5px;
                }}
                .stat-label {{
                    font-size: 14px;
                    color: #6c757d;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }}
                .priority-section {{
                    padding: 30px;
                }}
                .priority-header {{
                    display: flex;
                    align-items: center;
                    margin-bottom: 15px;
                    padding: 15px;
                    border-radius: 8px;
                    color: white;
                    font-weight: bold;
                }}
                .work-item {{
                    background: #f8f9fa;
                    margin: 10px 0;
                    padding: 15px;
                    border-radius: 6px;
                    border-left: 4px solid #667eea;
                }}
                .work-item-title {{
                    font-weight: bold;
                    color: #495057;
                    margin-bottom: 5px;
                }}
                .work-item-meta {{
                    font-size: 12px;
                    color: #6c757d;
                    display: grid;
                    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                    gap: 10px;
                }}
                .footer {{
                    background-color: #343a40;
                    color: white;
                    padding: 20px;
                    text-align: center;
                    font-size: 12px;
                }}
                .alert-box {{
                    background-color: #fff3cd;
                    border: 1px solid #ffeaa7;
                    color: #856404;
                    padding: 15px;
                    border-radius: 6px;
                    margin: 20px 0;
                }}
                @media (max-width: 600px) {{
                    .stats-grid {{
                        grid-template-columns: 1fr;
                    }}
                    .work-item-meta {{
                        grid-template-columns: 1fr;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="container">
                <!-- Header -->
                <div class="header">
                    <h1>⏰ Aging Issues Summary Report</h1>
                    <p>Auto Defect Triage System - {report_date}</p>
                </div>

                <!-- Summary Statistics -->
                <div class="summary-stats">
                    <h2 style="margin-top: 0; color: #495057;">📊 Summary Statistics</h2>

                    {self._generate_alert_section(summary)}

                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-number">{summary.total_aging_items}</div>
                            <div class="stat-label">Total Aging Items</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{summary.critical_aging_items + summary.high_aging_items}</div>
                            <div class="stat-label">Critical & High Priority</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{summary.unassigned_aging_items}</div>
                            <div class="stat-label">Unassigned Items</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{summary.oldest_item_age_days}</div>
                            <div class="stat-label">Oldest Item (Days)</div>
                        </div>
                    </div>
                </div>
        """

        # Add priority sections
        for priority in [1, 2, 3, 4]:
            items = summary.aging_items_by_priority.get(priority, [])
            if items:
                info = priority_info[priority]
                html += f"""
                <div class="priority-section">
                    <div class="priority-header" style="background-color: {info['color']};">
                        <span style="margin-right: 10px; font-size: 20px;">{info['icon']}</span>
                        <span>P{priority} - {info['label']} Priority ({len(items)} items)</span>
                    </div>
                """

                # Show top 10 items for each priority
                for item in items[:10]:
                    age_days = 0
                    if item.created_date:
                        try:
                            created_date = datetime.fromisoformat(item.created_date)
                            age_days = (datetime.utcnow() - created_date).days
                        except (ValueError, TypeError):
                            age_days = 0
                    html += f"""
                    <div class="work-item">
                        <div class="work-item-title">#{item.id}: {item.title}</div>
                        <div class="work-item-meta">
                            <span><strong>Age:</strong> {age_days} days</span>
                            <span><strong>State:</strong> {item.state}</span>
                            <span><strong>Assigned:</strong> {item.assigned_to or 'Unassigned'}</span>
                            <span><strong>Area:</strong> {item.area_path.split('\\')[-1] if item.area_path else 'Unknown'}</span>
                        </div>
                    </div>
                    """

                if len(items) > 10:
                    html += f"""
                    <div style="text-align: center; margin: 15px 0; color: #6c757d; font-style: italic;">
                        ... and {len(items) - 10} more {info['label'].lower()} priority items
                    </div>
                    """

                html += "</div>"

        # Footer
        html += f"""
                <!-- Footer -->
                <div class="footer">
                    <p>This report was generated automatically by the Auto Defect Triage System</p>
                    <p>Report generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC</p>
                    <p>For questions or issues, please contact the development team</p>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def _generate_alert_section(self, summary: AgingIssueSummary) -> str:
        """Generate alert section based on aging statistics."""
        alerts = []

        # Critical alerts
        if summary.critical_aging_items > 0:
            alerts.append(f"🚨 {summary.critical_aging_items} critical priority items are aging")

        if summary.unassigned_aging_items > 5:
            alerts.append(f"⚠️ {summary.unassigned_aging_items} aging items are unassigned")

        if summary.oldest_item_age_days > 30:
            alerts.append(f"📅 Oldest item has been open for {summary.oldest_item_age_days} days")

        if not alerts:
            return ""

        alert_html = '<div class="alert-box">'
        alert_html += '<strong>⚠️ Attention Required:</strong><br>'
        alert_html += '<br>'.join(alerts)
        alert_html += '</div>'

        return alert_html

    async def _send_summary_email(self, email_html: str, summary: AgingIssueSummary, recipients: Optional[List[str]] = None) -> bool:
        """Send the aging summary email."""
        try:
            if not self.email_logic_app_url:
                log_structured(
                    logger,
                    "warning",
                    "Email Logic App URL not configured - cannot send aging summary email"
                )
                return False

            # Use default recipients if none provided
            if not recipients:
                recipients = self.default_recipients

            # Generate subject line
            subject = f"⏰ Aging Issues Summary - {summary.total_aging_items} items need attention"
            if summary.critical_aging_items > 0:
                subject = f"🚨 URGENT: {subject} ({summary.critical_aging_items} critical)"

            # Prepare email payload for Logic App
            # Based on Email Logic App conditional logic: if attachmentName is empty AND Attachments is true, sends simple email
            # Otherwise sends email with Virgin Atlantic branded template
            email_payload = {
                "Body": email_html,
                "Subject": subject,
                "To": "; ".join(recipients),
                "Attachments": True,  # Enable attachments for branded template
                "attachmentName": "",  # Empty to trigger simple email path
            }

            # Send to Logic App
            import httpx
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.email_logic_app_url,
                    json=email_payload
                )
                response.raise_for_status()

            log_structured(
                logger,
                "info",
                f"Aging summary email sent successfully to {len(recipients)} recipients",
                extra={
                    "recipients_count": len(recipients),
                    "total_aging_items": summary.total_aging_items,
                    "critical_items": summary.critical_aging_items
                }
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending aging summary email: {e}",
                exc_info=True
            )
            return False

    async def _send_all_clear_email(self, recipients: Optional[List[str]] = None) -> bool:
        """Send 'all clear' email when no aging items are found."""
        try:
            if not self.email_logic_app_url:
                return False

            if not recipients:
                recipients = self.default_recipients

            # Generate simple all-clear email
            report_date = datetime.utcnow().strftime('%B %d, %Y')

            html = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Aging Issues Report - All Clear</title>
                <style>
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        margin: 0;
                        padding: 0;
                        background-color: #f5f5f5;
                    }}
                    .container {{
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    }}
                    .header {{
                        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
                        color: white;
                        padding: 30px;
                        text-align: center;
                    }}
                    .content {{
                        padding: 30px;
                        text-align: center;
                    }}
                    .success-icon {{
                        font-size: 64px;
                        margin-bottom: 20px;
                    }}
                    .footer {{
                        background-color: #343a40;
                        color: white;
                        padding: 20px;
                        text-align: center;
                        font-size: 12px;
                    }}
                </style>
            </head>
            <body>
                <div class="container">
                    <div class="header">
                        <h1>✅ Aging Issues Report</h1>
                        <p>Auto Defect Triage System - {report_date}</p>
                    </div>

                    <div class="content">
                        <div class="success-icon">🎉</div>
                        <h2 style="color: #28a745;">All Clear!</h2>
                        <p style="font-size: 18px; color: #6c757d;">
                            Great news! There are currently no work items aging beyond their priority thresholds.
                        </p>
                        <p style="color: #6c757d;">
                            All open work items are within their expected aging limits based on priority levels.
                        </p>
                    </div>

                    <div class="footer">
                        <p>This report was generated automatically by the Auto Defect Triage System</p>
                        <p>Report generated on {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC</p>
                    </div>
                </div>
            </body>
            </html>
            """

            email_payload = {
                "Body": html,
                "Subject": "✅ Aging Issues Report - All Clear",
                "To": "; ".join(recipients),
                "Attachments": True,
                "attachmentName": "",
            }

            import httpx
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.email_logic_app_url,
                    json=email_payload
                )
                response.raise_for_status()

            log_structured(
                logger,
                "info",
                "All clear aging summary email sent successfully"
            )

            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending all clear email: {e}",
                exc_info=True
            )
            return False
