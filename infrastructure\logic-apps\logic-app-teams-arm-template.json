{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#", "contentVersion": "*******", "parameters": {"logicAppName": {"type": "string", "defaultValue": "teams-notification-logic-app", "metadata": {"description": "Name of the Teams Logic App"}}, "location": {"type": "string", "defaultValue": "[resourceGroup().location]", "metadata": {"description": "Location for all resources"}}, "teamsWebhookUrl": {"type": "string", "defaultValue": "", "metadata": {"description": "Teams webhook URL for fallback notifications"}}, "functionAppUrl": {"type": "string", "defaultValue": "https://your-function-app.azurewebsites.net", "metadata": {"description": "Function App URL for response processing"}}, "functionAppKey": {"type": "securestring", "defaultValue": "", "metadata": {"description": "Function App key for authentication"}}}, "variables": {"teamsConnectionName": "teams-connection"}, "resources": [{"type": "Microsoft.Web/connections", "apiVersion": "2016-06-01", "name": "[variables('teamsConnectionName')]", "location": "[parameters('location')]", "properties": {"displayName": "[variables('teamsConnectionName')]", "api": {"id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', parameters('location'), 'teams')]"}}}, {"type": "Microsoft.Logic/workflows", "apiVersion": "2019-05-01", "name": "[parameters('logicAppName')]", "location": "[parameters('location')]", "dependsOn": ["[resourceId('Microsoft.Web/connections', variables('teamsConnectionName'))]"], "properties": {"state": "Enabled", "definition": {"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "*******", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}, "teams_webhook_url": {"type": "string", "defaultValue": "[parameters('teamsWebhookUrl')]"}, "function_app_url": {"type": "string", "defaultValue": "[parameters('functionAppUrl')]"}, "function_app_key": {"type": "securestring", "defaultValue": "[parameters('functionAppKey')]"}}, "triggers": {"When_a_HTTP_request_is_received": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"To": {"type": "string"}, "Subject": {"type": "string"}, "Body": {"type": "string"}, "Attachments": {"type": "string"}, "attachmentName": {"type": "string"}, "work_item_id": {"type": "integer"}, "adaptive_card": {"type": "object"}}}}}}, "actions": {"Initialize_work_item_id": {"type": "InitializeVariable", "inputs": {"variables": [{"name": "work_item_id", "type": "integer", "value": "@triggerBody()?['work_item_id']"}]}, "runAfter": {}}, "Check_if_adaptive_card_provided": {"type": "If", "expression": {"and": [{"not": {"equals": ["@triggerBody()?['adaptive_card']", "@null"]}}]}, "actions": {"Post_adaptive_card_and_wait_for_a_response": {"type": "ApiConnectionWebhook", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['teams']['connectionId']"}}, "path": "/v1.0/teams/@{encodeURIComponent('your-team-id')}/channels/@{encodeURIComponent('your-channel-id')}/messages/awaitresponse", "body": {"messageBody": "@triggerBody()?['adaptive_card']", "updateMessage": "Thanks for your response!", "recipient": {"to": "@triggerBody()?['To']"}}}, "runAfter": {}}, "Process_adaptive_card_response": {"type": "Http", "inputs": {"method": "POST", "uri": "@{parameters('function_app_url')}/api/process_logic_app_response?work_item_id=@{variables('work_item_id')}", "headers": {"Content-Type": "application/json", "x-functions-key": "@parameters('function_app_key')"}, "body": {"work_item_id": "@variables('work_item_id')", "replyText": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['replyText']}", "priority": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['priority']}", "action": "@{body('Post_adaptive_card_and_wait_for_a_response')?['data']?['action']}", "user_email": "@{body('Post_adaptive_card_and_wait_for_a_response')?['responder']?['email']}", "user_name": "@{body('Post_adaptive_card_and_wait_for_a_response')?['responder']?['displayName']}", "response_timestamp": "@utcNow()"}}, "runAfter": {"Post_adaptive_card_and_wait_for_a_response": ["Succeeded"]}}}, "else": {"actions": {"Send_simple_teams_message": {"type": "Http", "inputs": {"method": "POST", "uri": "@parameters('teams_webhook_url')", "headers": {"Content-Type": "application/json"}, "body": {"text": "@triggerBody()?['Body']", "title": "@triggerBody()?['Subject']"}}}}}, "runAfter": {"Initialize_work_item_id": ["Succeeded"]}}, "Response": {"type": "Response", "kind": "Http", "inputs": {"statusCode": 200, "body": {"status": "success", "message": "Teams notification processed", "work_item_id": "@variables('work_item_id')", "timestamp": "@utcNow()"}}, "runAfter": {"Check_if_adaptive_card_provided": ["Succeeded", "Failed"]}}}}, "parameters": {"$connections": {"value": {"teams": {"connectionId": "[resourceId('Microsoft.Web/connections', variables('teamsConnectionName'))]", "connectionName": "[variables('teamsConnectionName')]", "id": "[subscriptionResourceId('Microsoft.Web/locations/managedApis', parameters('location'), 'teams')]"}}}}}}], "outputs": {"logicAppUrl": {"type": "string", "value": "[listCallbackURL(concat(resourceId('Microsoft.Logic/workflows', parameters('logicAppName')), '/triggers/When_a_HTTP_request_is_received'), '2019-05-01').value]"}}}