"""
Historical Work Item Analysis Service
Provides hybrid search and similarity analysis for work item triage.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta

from ..vectorstore import VectorStore, SearchFilters, SearchHit
from ..adapters.ado_client import AdoClient
from ..utils.config import get_config

logger = logging.getLogger(__name__)


class HistoryService:
    """Service for analyzing historical work items and finding similar cases."""
    
    def __init__(self, vector_store: VectorStore, ado_client: AdoClient, config=None):
        self.vector_store = vector_store
        self.ado_client = ado_client
        self.config = config or get_config()
    
    async def find_similar_items(
        self,
        work_item: Dict[str, Any],
        query_vector: List[float],
        k: int = 10,
        include_resolved: bool = True,
        min_score: float = 0.5
    ) -> List[SearchHit]:
        """
        Find similar historical work items using hybrid search.
        
        Args:
            work_item: Current work item to find similarities for
            query_vector: Embedding vector for the work item
            k: Number of similar items to return
            include_resolved: Whether to include resolved/closed items
            min_score: Minimum similarity score threshold
            
        Returns:
            List of similar work items with metadata
        """
        try:
            # Extract work item details
            work_item_id = work_item.get('id')
            title = work_item.get('fields', {}).get('System.Title', '')
            description = work_item.get('fields', {}).get('System.Description', '')
            project = work_item.get('fields', {}).get('System.TeamProject', '')
            work_item_type = work_item.get('fields', {}).get('System.WorkItemType', '')
            
            # Build search query text
            query_text = f"{title} {description}".strip()
            
            # Build filters
            filters = SearchFilters(
                project=project if self.config.is_project_allowed(project) else None,
                work_item_type=work_item_type,
                exclude_ids=[str(work_item_id)] if work_item_id else None
            )
            
            # Exclude active states if not including resolved items
            if not include_resolved:
                filters.state = ['Closed', 'Resolved', 'Done', 'Completed']
            
            # Perform hybrid search
            similar_items = await self.vector_store.search_hybrid(
                query_text=query_text,
                query_vector=query_vector,
                k=k * 2,  # Get more results to filter
                filters=filters,
                alpha=0.6  # Favor vector similarity slightly
            )
            
            # Filter by minimum score and limit results
            filtered_items = [
                item for item in similar_items 
                if item.score >= min_score
            ][:k]
            
            # Enrich with additional ADO data if needed
            enriched_items = await self._enrich_similar_items(filtered_items)
            
            logger.info(
                f"Found {len(enriched_items)} similar items for work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "similar_count": len(enriched_items),
                    "min_score": min_score
                }
            )
            
            return enriched_items
            
        except Exception as e:
            logger.error(f"Error finding similar items: {e}")
            return []
    
    async def analyze_resolution_patterns(
        self,
        similar_items: List[SearchHit],
        lookback_days: int = 90
    ) -> Dict[str, Any]:
        """
        Analyze resolution patterns from similar historical items.
        
        Args:
            similar_items: List of similar work items
            lookback_days: How far back to look for patterns
            
        Returns:
            Analysis of resolution patterns and recommendations
        """
        try:
            if not similar_items:
                return {
                    "total_items": 0,
                    "resolution_patterns": {},
                    "common_assignees": [],
                    "avg_resolution_time": None,
                    "recommendations": []
                }
            
            # Filter items within lookback period
            cutoff_date = datetime.utcnow() - timedelta(days=lookback_days)
            recent_items = []
            
            for item in similar_items:
                created_date_str = item.metadata.get('created_date')
                if created_date_str:
                    try:
                        created_date = datetime.fromisoformat(created_date_str.replace('Z', '+00:00'))
                        if created_date >= cutoff_date:
                            recent_items.append(item)
                    except (ValueError, TypeError):
                        # Include item if date parsing fails
                        recent_items.append(item)
                else:
                    recent_items.append(item)
            
            # Analyze resolution patterns
            resolution_patterns = {}
            assignee_counts = {}
            resolution_times = []
            
            for item in recent_items:
                # Count final states
                final_state = item.metadata.get('state', 'Unknown')
                resolution_patterns[final_state] = resolution_patterns.get(final_state, 0) + 1
                
                # Count assignees
                assignee = item.metadata.get('assigned_to', 'Unassigned')
                if assignee and assignee != 'Unassigned':
                    assignee_counts[assignee] = assignee_counts.get(assignee, 0) + 1
                
                # Calculate resolution time if available
                created_date_str = item.metadata.get('created_date')
                resolved_date_str = item.metadata.get('resolved_date')
                
                if created_date_str and resolved_date_str:
                    try:
                        created_date = datetime.fromisoformat(created_date_str.replace('Z', '+00:00'))
                        resolved_date = datetime.fromisoformat(resolved_date_str.replace('Z', '+00:00'))
                        resolution_time = (resolved_date - created_date).total_seconds() / 3600  # hours
                        resolution_times.append(resolution_time)
                    except (ValueError, TypeError):
                        pass
            
            # Calculate statistics
            avg_resolution_time = sum(resolution_times) / len(resolution_times) if resolution_times else None
            
            # Get top assignees
            common_assignees = sorted(
                assignee_counts.items(), 
                key=lambda x: x[1], 
                reverse=True
            )[:5]
            
            # Generate recommendations
            recommendations = self._generate_resolution_recommendations(
                resolution_patterns, 
                common_assignees, 
                avg_resolution_time
            )
            
            return {
                "total_items": len(recent_items),
                "resolution_patterns": resolution_patterns,
                "common_assignees": [{"name": name, "count": count} for name, count in common_assignees],
                "avg_resolution_time_hours": avg_resolution_time,
                "recommendations": recommendations,
                "lookback_days": lookback_days
            }
            
        except Exception as e:
            logger.error(f"Error analyzing resolution patterns: {e}")
            return {
                "total_items": 0,
                "resolution_patterns": {},
                "common_assignees": [],
                "avg_resolution_time": None,
                "recommendations": [],
                "error": str(e)
            }
    
    async def find_duplicate_candidates(
        self,
        work_item: Dict[str, Any],
        query_vector: List[float],
        similarity_threshold: float = 0.85
    ) -> List[SearchHit]:
        """
        Find potential duplicate work items based on high similarity.
        
        Args:
            work_item: Current work item to check for duplicates
            query_vector: Embedding vector for the work item
            similarity_threshold: Minimum similarity score for duplicates
            
        Returns:
            List of potential duplicate work items
        """
        try:
            # Find similar items with high threshold
            similar_items = await self.find_similar_items(
                work_item=work_item,
                query_vector=query_vector,
                k=20,  # Check more items for duplicates
                include_resolved=False,  # Only check active items
                min_score=similarity_threshold
            )
            
            # Additional filtering for duplicates
            duplicates = []
            work_item_title = work_item.get('fields', {}).get('System.Title', '').lower()
            
            for item in similar_items:
                item_title = item.metadata.get('title', '').lower()
                
                # Check for exact title matches or very high similarity
                if (item.score >= similarity_threshold and 
                    (item_title == work_item_title or item.score >= 0.95)):
                    duplicates.append(item)
            
            logger.info(
                f"Found {len(duplicates)} potential duplicates",
                extra={
                    "work_item_id": work_item.get('id'),
                    "duplicate_count": len(duplicates),
                    "threshold": similarity_threshold
                }
            )
            
            return duplicates
            
        except Exception as e:
            logger.error(f"Error finding duplicate candidates: {e}")
            return []
    
    async def _enrich_similar_items(self, similar_items: List[SearchHit]) -> List[SearchHit]:
        """Enrich similar items with additional ADO data if needed."""
        # For now, return items as-is
        # TODO: Fetch additional work item details if metadata is incomplete
        return similar_items
    
    def _generate_resolution_recommendations(
        self,
        resolution_patterns: Dict[str, int],
        common_assignees: List[Tuple[str, int]],
        avg_resolution_time: Optional[float]
    ) -> List[str]:
        """Generate recommendations based on resolution patterns."""
        recommendations = []
        
        # Resolution pattern recommendations
        if resolution_patterns:
            most_common_resolution = max(resolution_patterns.items(), key=lambda x: x[1])
            if most_common_resolution[1] > 1:
                recommendations.append(
                    f"Similar items are typically resolved as '{most_common_resolution[0]}' "
                    f"({most_common_resolution[1]} out of {sum(resolution_patterns.values())} cases)"
                )
        
        # Assignee recommendations
        if common_assignees:
            top_assignee = common_assignees[0]
            if top_assignee[1] > 1:
                recommendations.append(
                    f"Consider assigning to {top_assignee[0]} who has handled "
                    f"{top_assignee[1]} similar items"
                )
        
        # Timeline recommendations
        if avg_resolution_time:
            if avg_resolution_time < 24:
                recommendations.append(
                    f"Similar items typically resolve quickly (avg: {avg_resolution_time:.1f} hours)"
                )
            elif avg_resolution_time > 168:  # 1 week
                recommendations.append(
                    f"Similar items typically take longer to resolve (avg: {avg_resolution_time/24:.1f} days)"
                )
        
        return recommendations
    
    async def get_historical_context(
        self,
        work_item: Dict[str, Any],
        query_vector: List[float]
    ) -> Dict[str, Any]:
        """
        Get comprehensive historical context for a work item.
        
        Args:
            work_item: Current work item
            query_vector: Embedding vector for the work item
            
        Returns:
            Complete historical analysis including similar items, patterns, and duplicates
        """
        try:
            # Find similar items
            similar_items = await self.find_similar_items(
                work_item=work_item,
                query_vector=query_vector,
                k=15,
                include_resolved=True
            )
            
            # Analyze resolution patterns
            resolution_analysis = await self.analyze_resolution_patterns(similar_items)
            
            # Check for duplicates
            duplicates = await self.find_duplicate_candidates(
                work_item=work_item,
                query_vector=query_vector
            )
            
            return {
                "similar_items": [
                    {
                        "id": item.id,
                        "title": item.metadata.get('title', ''),
                        "score": item.score,
                        "state": item.metadata.get('state', ''),
                        "assigned_to": item.metadata.get('assigned_to', ''),
                        "created_date": item.metadata.get('created_date', '')
                    }
                    for item in similar_items
                ],
                "resolution_analysis": resolution_analysis,
                "potential_duplicates": [
                    {
                        "id": item.id,
                        "title": item.metadata.get('title', ''),
                        "score": item.score,
                        "state": item.metadata.get('state', ''),
                        "assigned_to": item.metadata.get('assigned_to', '')
                    }
                    for item in duplicates
                ],
                "summary": {
                    "similar_count": len(similar_items),
                    "duplicate_count": len(duplicates),
                    "has_historical_data": len(similar_items) > 0
                }
            }
            
        except Exception as e:
            logger.error(f"Error getting historical context: {e}")
            return {
                "similar_items": [],
                "resolution_analysis": {},
                "potential_duplicates": [],
                "summary": {
                    "similar_count": 0,
                    "duplicate_count": 0,
                    "has_historical_data": False
                },
                "error": str(e)
            }
