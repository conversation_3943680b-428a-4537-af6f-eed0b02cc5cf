#!/usr/bin/env python3
"""
Test script to verify the workitem_created handler uses REAL data only.
NO HALLUCINATION - Tests that the fixed handler doesn't suggest fake assignees.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.workitem_created.handler import get_clients, run_triage_pipeline, convert_ado_to_work_item
from __app__.common.utils.config import get_config

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_workitem_created_handler():
    """Test that the workitem_created handler uses REAL data only."""
    
    print("🚫 WORKITEM CREATED HANDLER - NO HALLUCINATION TEST")
    print("=" * 70)
    print("🎯 Goal: Verify workitem_created handler uses REAL assignees only")
    print("❌ FAIL if any fake/hallucinated assignees are suggested")
    
    try:
        config = get_config()
        
        # Initialize clients using the handler's method
        clients = get_clients()
        
        print(f"\n📋 Initialized Clients:")
        print(f"   • ADO Client: {type(clients['ado']).__name__}")
        print(f"   • Search Client: {type(clients['search']).__name__}")
        print(f"   • Teams Client: {type(clients['teams']).__name__}")
        print(f"   • Duplicate Detector: {type(clients['duplicate']).__name__}")
        print(f"   • Triage Service: {type(clients['triage']).__name__}")
        print(f"   • History Service: {type(clients['history']).__name__}")
        print(f"   • Priority Engine: {type(clients['priority']).__name__}")
        
        # Create a test work item (simulating work item 748404)
        test_work_item_data = {
            "id": 748404,
            "fields": {
                "System.Title": "Test - New defect",
                "System.Description": "Testing the assignee suggestion system with real data",
                "System.WorkItemType": "Bug",
                "System.State": "New",
                "System.AreaPath": "Air4 Channels Testing\\AI Testing - Defect Management",
                "System.IterationPath": "Air4 Channels Testing\\AI Testing - Defect Management\\Sprint 1",
                "System.AssignedTo": "",
                "System.CreatedDate": "2025-10-24T10:00:00Z",
                "System.ChangedDate": "2025-10-24T10:00:00Z",
                "Microsoft.VSTS.Common.Priority": 2,
                "Microsoft.VSTS.Common.Severity": "3 - Medium"
            }
        }
        
        # Convert to WorkItem object
        work_item = convert_ado_to_work_item(test_work_item_data)
        
        print(f"\n🎯 Test Work Item:")
        print(f"   ID: {work_item.id}")
        print(f"   Title: {work_item.title}")
        print(f"   Type: {work_item.work_item_type}")
        print(f"   State: {work_item.state}")
        print(f"   Area Path: {work_item.area_path}")
        print(f"   Iteration Path: {work_item.iteration_path}")
        
        # Run the triage pipeline
        print(f"\n🤖 Running Triage Pipeline...")
        triage_result = await run_triage_pipeline(work_item, clients)
        
        if not triage_result:
            print(f"❌ No triage result returned")
            return False
        
        print(f"✅ Triage pipeline completed")
        
        # Analyze the results
        print(f"\n📊 Triage Results:")
        print(f"   Work Item ID: {triage_result.work_item_id}")
        print(f"   Assigned To: {triage_result.assigned_to}")
        print(f"   Priority: {triage_result.priority}")
        print(f"   Confidence Score: {triage_result.confidence_score:.3f}")
        print(f"   Reasoning: {triage_result.reasoning}")
        print(f"   Duplicates Found: {len(triage_result.duplicates)}")
        print(f"   Suggestions Count: {len(triage_result.suggestions)}")
        
        # Check suggestions for hallucination
        print(f"\n👥 Assignee Suggestions Analysis:")
        
        # Known fake assignees from the old AssignmentEngine
        fake_assignees = {
            "<EMAIL>",
            "<EMAIL>", 
            "<EMAIL>"
        }
        
        hallucination_detected = False
        
        if triage_result.suggestions:
            for i, suggestion in enumerate(triage_result.suggestions, 1):
                assignee = suggestion.get('assignee', '')
                confidence = suggestion.get('confidence', 0.0)
                reasoning = suggestion.get('reasoning', '')
                
                print(f"\n   {i}. {assignee}")
                print(f"      Confidence: {confidence:.3f}")
                print(f"      Reasoning: {reasoning}")
                
                # Check if this is a known fake assignee
                if assignee in fake_assignees:
                    print(f"      ❌ HALLUCINATION DETECTED: This is a fake assignee!")
                    hallucination_detected = True
                else:
                    print(f"      ✅ Not a known fake assignee")
        else:
            print(f"   ❌ No suggestions found")
        
        # Check assigned_to field
        if triage_result.assigned_to:
            print(f"\n🎯 Assigned To Analysis:")
            print(f"   Assigned To: {triage_result.assigned_to}")
            
            if triage_result.assigned_to in fake_assignees:
                print(f"   ❌ HALLUCINATION DETECTED: Assigned to fake assignee!")
                hallucination_detected = True
            else:
                print(f"   ✅ Not a known fake assignee")
        
        # Final verification
        print(f"\n🔍 HALLUCINATION CHECK:")
        
        if hallucination_detected:
            print(f"   ❌ HALLUCINATION DETECTED!")
            print(f"   ⚠️ The handler is still suggesting fake assignees")
            print(f"   🚫 Known fake assignees: {fake_assignees}")
            return False
        else:
            print(f"   ✅ NO HALLUCINATION DETECTED")
            print(f"   ✅ No known fake assignees found in suggestions")
            print(f"   ✅ Handler appears to be using real data")
        
        # Check data source
        if "real" in triage_result.reasoning.lower() or "historical" in triage_result.reasoning.lower():
            print(f"   ✅ Reasoning indicates real/historical data source")
        else:
            print(f"   ⚠️ Reasoning doesn't clearly indicate real data source")
        
        return not hallucination_detected
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    
    print("🚫 WORKITEM CREATED HANDLER - NO HALLUCINATION TEST")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 CRITICAL: Verify workitem_created handler doesn't suggest fake assignees")
    print(f"✅ PASS: Only real historical assignees suggested")
    print(f"❌ FAIL: Any known fake assignees detected")
    
    success = await test_workitem_created_handler()
    
    print(f"\n{'='*80}")
    print("🏁 FINAL RESULT")
    print(f"{'='*80}")
    
    if success:
        print("🎉 ✅ WORKITEM CREATED HANDLER TEST PASSED!")
        print("✅ Handler uses REAL data only")
        print("✅ No known fake assignees detected")
        print("✅ Old AssignmentEngine successfully replaced")
        print("✅ New TriageService working correctly")
        print("✅ SAFE FOR PRODUCTION USE")
    else:
        print("💥 ❌ WORKITEM CREATED HANDLER TEST FAILED!")
        print("⚠️ CRITICAL: Fake assignees still being suggested!")
        print("🚫 Handler still using old AssignmentEngine")
        print("🔧 FIX REQUIRED: Complete migration to TriageService")
        print("🚫 DO NOT USE IN PRODUCTION")
    
    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
