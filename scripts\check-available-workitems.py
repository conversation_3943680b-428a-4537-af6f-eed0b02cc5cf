#!/usr/bin/env python3
"""
Check Available Work Items
=========================

This script checks what work items are available in the ADO project
and finds a suitable test work item.

Usage:
    python check-available-workitems.py
"""

import asyncio
import json
import sys
import os

# Add the functions directory to the path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'functions'))

async def check_ado_connection():
    """Check ADO connection and list available work items."""
    print("🔍 Checking ADO Connection and Available Work Items...")
    
    try:
        from __app__.common.utils.config import get_config
        from __app__.common.adapters.ado_client import AdoClient
        
        config = get_config()
        print(f"   ADO Organization: {config.ADO_ORGANIZATION}")
        print(f"   ADO Project: {config.ADO_PROJECT}")
        print(f"   PAT Token: {'SET' if config.ADO_PAT_TOKEN else 'NOT SET'}")
        
        ado_client = AdoClient(config)
        
        # Query for recent work items
        wiql_query = """
        SELECT [System.Id], [System.Title], [System.State], [System.WorkItemType], [Microsoft.VSTS.Common.Priority]
        FROM WorkItems
        WHERE [System.TeamProject] = @project
        ORDER BY [System.Id] DESC
        """
        
        print(f"\n   Executing WIQL query...")
        work_items = await ado_client.query_work_items(wiql_query)
        
        if work_items:
            print(f"✅ Found {len(work_items)} work items")
            print(f"\n📋 Available Work Items:")
            print(f"{'ID':<8} {'Type':<12} {'State':<12} {'Priority':<8} {'Title'}")
            print("-" * 80)
            
            for item in work_items[:20]:  # Show first 20
                fields = item.get('fields', {})
                work_item_id = item.get('id', 'N/A')
                title = fields.get('System.Title', 'No Title')[:40]
                state = fields.get('System.State', 'N/A')
                work_type = fields.get('System.WorkItemType', 'N/A')
                priority = fields.get('Microsoft.VSTS.Common.Priority', 'N/A')
                
                print(f"{work_item_id:<8} {work_type:<12} {state:<12} P{priority:<7} {title}")
            
            # Find a suitable test work item (Bug type, preferably)
            test_candidates = []
            for item in work_items:
                fields = item.get('fields', {})
                work_type = fields.get('System.WorkItemType', '')
                state = fields.get('System.State', '')
                
                if work_type.lower() == 'bug' and state.lower() in ['new', 'active', 'committed']:
                    test_candidates.append(item)
            
            if test_candidates:
                best_candidate = test_candidates[0]
                candidate_id = best_candidate.get('id')
                candidate_title = best_candidate.get('fields', {}).get('System.Title', '')
                
                print(f"\n🎯 Recommended test work item:")
                print(f"   ID: {candidate_id}")
                print(f"   Title: {candidate_title}")
                print(f"   Type: {best_candidate.get('fields', {}).get('System.WorkItemType', 'N/A')}")
                print(f"   State: {best_candidate.get('fields', {}).get('System.State', 'N/A')}")
                
                return candidate_id
            else:
                print(f"\n⚠️ No suitable Bug work items found for testing")
                if work_items:
                    first_item = work_items[0]
                    first_id = first_item.get('id')
                    print(f"   Using first available work item: {first_id}")
                    return first_id
                return None
        else:
            print("❌ No work items found")
            return None
            
    except Exception as e:
        print(f"❌ Error checking work items: {e}")
        print(f"   Error type: {type(e).__name__}")
        return None

async def test_work_item_access(work_item_id):
    """Test accessing a specific work item."""
    print(f"\n🔍 Testing Access to Work Item {work_item_id}...")
    
    try:
        from __app__.common.utils.config import get_config
        from __app__.common.adapters.ado_client import AdoClient
        
        config = get_config()
        ado_client = AdoClient(config)
        
        work_item = await ado_client.get_work_item(work_item_id)
        
        if work_item:
            fields = work_item.get('fields', {})
            print(f"✅ Successfully accessed work item {work_item_id}")
            print(f"   Title: {fields.get('System.Title', 'N/A')}")
            print(f"   Type: {fields.get('System.WorkItemType', 'N/A')}")
            print(f"   State: {fields.get('System.State', 'N/A')}")
            print(f"   Priority: P{fields.get('Microsoft.VSTS.Common.Priority', 'N/A')}")
            print(f"   Revision: {work_item.get('rev', 'N/A')}")
            return True
        else:
            print(f"❌ Work item {work_item_id} not found or not accessible")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing work item {work_item_id}: {e}")
        return False

async def test_work_item_update(work_item_id):
    """Test updating a specific work item."""
    print(f"\n🔍 Testing Update to Work Item {work_item_id}...")
    
    try:
        from __app__.common.utils.config import get_config
        from __app__.common.adapters.ado_client import AdoClient
        from datetime import datetime
        
        config = get_config()
        ado_client = AdoClient(config)
        
        # Test with a simple comment update
        test_comment = f"🧪 Test update from script at {datetime.utcnow().isoformat()}"
        updates = {
            "System.History": test_comment
        }
        
        print(f"   Adding test comment: {test_comment}")
        updated_work_item = await ado_client.update_work_item(work_item_id, updates)
        
        if updated_work_item:
            new_rev = updated_work_item.get('rev')
            print(f"✅ Successfully updated work item {work_item_id}")
            print(f"   New revision: {new_rev}")
            return True
        else:
            print(f"❌ Update failed - returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error updating work item {work_item_id}: {e}")
        return False

async def main():
    """Main function."""
    print("🚀 ADO Work Items Check")
    print("=" * 50)
    
    # Check available work items
    test_work_item_id = await check_ado_connection()
    
    if test_work_item_id:
        # Test access
        access_success = await test_work_item_access(test_work_item_id)
        
        if access_success:
            # Test update
            update_success = await test_work_item_update(test_work_item_id)
            
            if update_success:
                print(f"\n🎉 Work item {test_work_item_id} is ready for testing!")
                print(f"\nTo test the response processing, update your test scripts to use:")
                print(f"   work_item_id = {test_work_item_id}")
            else:
                print(f"\n⚠️ Cannot update work item {test_work_item_id}")
        else:
            print(f"\n⚠️ Cannot access work item {test_work_item_id}")
    else:
        print(f"\n❌ No suitable work items found for testing")

if __name__ == "__main__":
    asyncio.run(main())
