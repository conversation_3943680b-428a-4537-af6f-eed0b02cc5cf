"""
Vector Store Base Protocol and Models
Defines the common interface for all vector store implementations.
"""

from typing import Protocol, List, Dict, Any, Optional, Union
from dataclasses import dataclass
from datetime import datetime


@dataclass
class VectorDoc:
    """Document to be stored in vector database."""
    
    id: str
    text: str
    vector: List[float]
    metadata: Dict[str, Any]
    
    def __post_init__(self):
        """Validate document after initialization."""
        if not self.id:
            raise ValueError("Document ID cannot be empty")
        if not self.text:
            raise ValueError("Document text cannot be empty")
        if not self.vector:
            raise ValueError("Document vector cannot be empty")


@dataclass
class SearchHit:
    """Search result from vector database."""
    
    id: str
    text: str
    score: float
    metadata: Dict[str, Any]
    
    @property
    def work_item_id(self) -> Optional[int]:
        """Extract work item ID from metadata."""
        return self.metadata.get('work_item_id')
    
    @property
    def title(self) -> str:
        """Extract title from metadata."""
        return self.metadata.get('title', '')
    
    @property
    def assigned_to(self) -> str:
        """Extract assignee from metadata."""
        return self.metadata.get('assigned_to', '')
    
    @property
    def state(self) -> str:
        """Extract state from metadata."""
        return self.metadata.get('state', '')
    
    @property
    def priority(self) -> int:
        """Extract priority from metadata."""
        return self.metadata.get('priority', 3)


@dataclass
class SearchFilters:
    """Filters for vector search."""
    
    project: Optional[str] = None
    work_item_type: Optional[str] = None
    state: Optional[List[str]] = None
    assigned_to: Optional[str] = None
    priority: Optional[List[int]] = None
    created_after: Optional[datetime] = None
    created_before: Optional[datetime] = None
    exclude_ids: Optional[List[str]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert filters to dictionary for backend-specific processing."""
        filters = {}
        
        if self.project:
            filters['project'] = self.project
        if self.work_item_type:
            filters['work_item_type'] = self.work_item_type
        if self.state:
            filters['state'] = self.state
        if self.assigned_to:
            filters['assigned_to'] = self.assigned_to
        if self.priority:
            filters['priority'] = self.priority
        if self.created_after:
            filters['created_after'] = self.created_after.isoformat()
        if self.created_before:
            filters['created_before'] = self.created_before.isoformat()
        if self.exclude_ids:
            filters['exclude_ids'] = self.exclude_ids
        
        return filters


class VectorStore(Protocol):
    """Protocol for vector store implementations."""
    
    async def upsert(self, doc: VectorDoc) -> None:
        """
        Insert or update a document in the vector store.
        
        Args:
            doc: Document to upsert
        """
        ...
    
    async def upsert_batch(self, docs: List[VectorDoc]) -> None:
        """
        Insert or update multiple documents in batch.
        
        Args:
            docs: List of documents to upsert
        """
        ...
    
    async def search(
        self, 
        query_vector: List[float], 
        k: int = 10,
        filters: Optional[SearchFilters] = None
    ) -> List[SearchHit]:
        """
        Search for similar documents using vector similarity.
        
        Args:
            query_vector: Query vector for similarity search
            k: Number of results to return
            filters: Optional filters to apply
            
        Returns:
            List of search hits ordered by similarity score
        """
        ...
    
    async def search_hybrid(
        self,
        query_text: str,
        query_vector: List[float],
        k: int = 10,
        filters: Optional[SearchFilters] = None,
        alpha: float = 0.5
    ) -> List[SearchHit]:
        """
        Hybrid search combining text and vector similarity.
        
        Args:
            query_text: Text query for BM25/text search
            query_vector: Vector query for similarity search
            k: Number of results to return
            filters: Optional filters to apply
            alpha: Weight for combining scores (0.0 = text only, 1.0 = vector only)
            
        Returns:
            List of search hits with combined scores
        """
        ...
    
    async def get_by_id(self, doc_id: str) -> Optional[VectorDoc]:
        """
        Get document by ID.
        
        Args:
            doc_id: Document ID
            
        Returns:
            Document if found, None otherwise
        """
        ...
    
    async def delete(self, doc_id: str) -> None:
        """
        Delete document by ID.
        
        Args:
            doc_id: Document ID to delete
        """
        ...
    
    async def delete_batch(self, doc_ids: List[str]) -> None:
        """
        Delete multiple documents by ID.
        
        Args:
            doc_ids: List of document IDs to delete
        """
        ...
    
    async def count(self, filters: Optional[SearchFilters] = None) -> int:
        """
        Count documents matching filters.
        
        Args:
            filters: Optional filters to apply
            
        Returns:
            Number of matching documents
        """
        ...
    
    async def initialize(self) -> None:
        """
        Initialize the vector store (create indexes, tables, etc.).
        """
        ...
    
    async def health_check(self) -> Dict[str, Any]:
        """
        Check the health of the vector store.
        
        Returns:
            Health status information
        """
        ...


class VectorStoreFactory:
    """Factory for creating vector store instances."""
    
    @staticmethod
    def create_vector_store(config) -> VectorStore:
        """
        Create vector store instance based on configuration.
        
        Args:
            config: Configuration object
            
        Returns:
            Vector store instance
        """
        backend = config.VECTOR_BACKEND.lower()
        
        if backend == "azure":
            from .azure_search import AzureSearchClient
            return AzureSearchClient(config)
        elif backend == "pg":
            from .pgvector import PgVectorClient
            return PgVectorClient(config)
        elif backend == "qdrant":
            from .qdrant import QdrantClient
            return QdrantClient(config)
        else:
            raise ValueError(f"Unsupported vector backend: {backend}")
    
    @staticmethod
    async def initialize_storage(vector_store: VectorStore) -> None:
        """
        Initialize vector storage (create indexes, tables, etc.).
        
        Args:
            vector_store: Vector store instance to initialize
        """
        await vector_store.initialize()


# Utility functions for vector operations
def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
    """Calculate cosine similarity between two vectors."""
    import math
    
    dot_product = sum(a * b for a, b in zip(vec1, vec2))
    magnitude1 = math.sqrt(sum(a * a for a in vec1))
    magnitude2 = math.sqrt(sum(a * a for a in vec2))
    
    if magnitude1 == 0 or magnitude2 == 0:
        return 0.0
    
    return dot_product / (magnitude1 * magnitude2)


def normalize_vector(vector: List[float]) -> List[float]:
    """Normalize vector to unit length."""
    import math
    
    magnitude = math.sqrt(sum(x * x for x in vector))
    if magnitude == 0:
        return vector
    
    return [x / magnitude for x in vector]


def combine_scores(
    text_score: float, 
    vector_score: float, 
    alpha: float = 0.5
) -> float:
    """
    Combine text and vector scores for hybrid search.
    
    Args:
        text_score: Text similarity score (0-1)
        vector_score: Vector similarity score (0-1)
        alpha: Weight for combining (0.0 = text only, 1.0 = vector only)
        
    Returns:
        Combined score
    """
    return (1 - alpha) * text_score + alpha * vector_score
