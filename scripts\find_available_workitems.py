#!/usr/bin/env python3
"""
Find available work items in the Air4 Channels Testing project.
"""

import os
import sys
import json
import asyncio
import logging

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.common.utils.config import get_config
from __app__.common.adapters.ado_client import AdoClient

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def find_work_items():
    """Find available work items in the project."""
    
    print("🔍 Finding Available Work Items")
    print("=" * 50)
    
    try:
        config = get_config()
        ado_client = AdoClient(config)
        
        print(f"Organization: {config.ADO_ORGANIZATION}")
        print(f"Project: {config.ADO_PROJECT}")
        
        # Try different WIQL queries to find work items
        queries = [
            {
                "name": "All Work Items (Recent)",
                "wiql": """
                SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State], [System.AssignedTo]
                FROM WorkItems
                WHERE [System.TeamProject] = @project
                ORDER BY [System.ChangedDate] DESC
                """
            },
            {
                "name": "Bugs Only",
                "wiql": """
                SELECT [System.Id], [System.Title], [System.State], [System.AssignedTo]
                FROM WorkItems
                WHERE [System.TeamProject] = @project
                AND [System.WorkItemType] = 'Bug'
                ORDER BY [System.Id] DESC
                """
            },
            {
                "name": "All Work Item Types",
                "wiql": """
                SELECT [System.Id], [System.Title], [System.WorkItemType]
                FROM WorkItems
                WHERE [System.TeamProject] = @project
                ORDER BY [System.Id] DESC
                """
            }
        ]
        
        found_items = []
        
        for query_info in queries:
            print(f"\n📋 Query: {query_info['name']}")
            print("-" * 30)
            
            try:
                result = await ado_client.query_work_items(query_info['wiql'])
                work_items = result.get('workItems', [])
                
                print(f"Found {len(work_items)} work items")
                
                if work_items:
                    # Get details for the first few work items
                    for i, item in enumerate(work_items[:10]):
                        work_item_id = item.get('id')
                        
                        try:
                            # Get full work item details
                            work_item_details = await ado_client.get_work_item(work_item_id)
                            
                            if work_item_details:
                                fields = work_item_details.get('fields', {})
                                title = fields.get('System.Title', 'Unknown')
                                work_type = fields.get('System.WorkItemType', 'Unknown')
                                state = fields.get('System.State', 'Unknown')
                                assigned_to = fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')
                                
                                print(f"   {i+1}. ID: {work_item_id}")
                                print(f"      Title: {title}")
                                print(f"      Type: {work_type}")
                                print(f"      State: {state}")
                                print(f"      Assigned: {assigned_to}")
                                print()
                                
                                found_items.append({
                                    'id': work_item_id,
                                    'title': title,
                                    'type': work_type,
                                    'state': state,
                                    'assigned_to': assigned_to
                                })
                            
                        except Exception as e:
                            print(f"   Error getting details for {work_item_id}: {e}")
                
                if len(work_items) > 10:
                    print(f"   ... and {len(work_items) - 10} more items")
                
            except Exception as e:
                print(f"❌ Query failed: {e}")
        
        # Summary
        print("\n" + "=" * 50)
        print("📊 Summary")
        print(f"Total unique work items found: {len(found_items)}")
        
        if found_items:
            print("\n🎯 Recommended work items for testing:")
            
            # Find bugs first
            bugs = [item for item in found_items if item['type'] == 'Bug']
            if bugs:
                print("   Bugs:")
                for bug in bugs[:3]:
                    print(f"   - {bug['id']}: {bug['title'][:50]}...")
            
            # Find other types
            others = [item for item in found_items if item['type'] != 'Bug']
            if others:
                print("   Other work items:")
                for other in others[:3]:
                    print(f"   - {other['id']}: {other['title'][:50]}...")
            
            # Suggest the first available work item for testing
            first_item = found_items[0]
            print(f"\n💡 Suggestion: Use work item {first_item['id']} for testing")
            print(f"   Update your local.settings.json:")
            print(f'   "SAFETY_ONLY_WORKITEM_ID": "{first_item["id"]}",')
            
            return first_item['id']
        else:
            print("❌ No work items found in the project")
            print("   Possible issues:")
            print("   1. PAT token doesn't have 'Work Items (Read)' permission")
            print("   2. Project name is incorrect")
            print("   3. Project is empty")
            print("   4. Access permissions are restricted")
            
            return None
        
    except Exception as e:
        print(f"❌ Error: {e}")
        logger.exception("Failed to find work items")
        return None


async def test_specific_ranges():
    """Test specific ID ranges to find work items."""
    
    print("\n🔍 Testing Specific ID Ranges")
    print("=" * 40)
    
    config = get_config()
    ado_client = AdoClient(config)
    
    # Test different ID ranges
    ranges = [
        (1, 100),
        (100, 1000),
        (1000, 10000),
        (10000, 100000),
        (700000, 800000),  # Around the 748404 range
        (750000, 760000)   # More specific range
    ]
    
    found_items = []
    
    for start, end in ranges:
        print(f"\n📍 Testing range {start}-{end}...")
        
        # Test a few IDs in this range
        test_ids = [start, start + (end-start)//4, start + (end-start)//2, start + 3*(end-start)//4, end-1]
        
        for test_id in test_ids:
            try:
                work_item = await ado_client.get_work_item(test_id)
                if work_item:
                    title = work_item.get('fields', {}).get('System.Title', 'Unknown')
                    print(f"   ✅ Found: {test_id} - {title[:40]}...")
                    found_items.append(test_id)
                    break  # Found one in this range, move to next range
            except:
                pass
        
        if not any(test_id in found_items for test_id in test_ids):
            print(f"   ❌ No items found in range {start}-{end}")
    
    if found_items:
        print(f"\n✅ Found work items: {found_items}")
        return found_items[0]
    else:
        print("\n❌ No work items found in any tested range")
        return None


async def main():
    """Main function."""
    
    # First try WIQL queries
    work_item_id = await find_work_items()
    
    # If no items found with WIQL, try specific ranges
    if not work_item_id:
        work_item_id = await test_specific_ranges()
    
    if work_item_id:
        print(f"\n🎉 Success! You can test with work item {work_item_id}")
        return work_item_id
    else:
        print("\n❌ No accessible work items found")
        return None


if __name__ == "__main__":
    result = asyncio.run(main())
    sys.exit(0 if result else 1)
