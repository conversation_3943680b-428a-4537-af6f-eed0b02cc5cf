# Production Safety: Configurable Guardrails

## 🛡️ **Production Safety Implementation**

To ensure no production work items are accidentally modified during development and testing, the system implements configurable safety guardrails through environment variables. These replace the previous hardcoded work item restrictions.

## ⚠️ **Configurable Safety Guardrails**

### **1. Project Allow-List (`ALLOW_PROJECTS`)**
- **Purpose**: Restrict processing to specific Azure DevOps projects
- **Format**: Comma-separated list of project names
- **Example**: `ALLOW_PROJECTS="TestProject,DevProject"`
- **Behavior**: If set, only work items from listed projects are processed

### **2. Work Item Restriction (`SAFETY_ONLY_WORKITEM_ID`)**
- **Purpose**: Restrict processing to a single work item ID for testing
- **Format**: Single numeric work item ID
- **Example**: `SAFETY_ONLY_WORKITEM_ID="748404"`
- **Behavior**: If set, only the specified work item ID is processed

### **3. Read-Only Mode (`READ_ONLY`)**
- **Purpose**: Prevent any modifications to Azure DevOps
- **Format**: Boolean (true/false)
- **Default**: `true` (safe by default)
- **Behavior**: When true, no work item assignments, priorities, or links are modified

```python
# PRODUCTION SAFETY: Only process Bug 748404
if work_item_id != 748404:
    return HttpResponse("PRODUCTION SAFETY: Only Bug 748404 is processed")
```

### **2. Assignment Engine (`common/ai/assigner.py`)**
- **Function**: `assign_work_item()`
- **Restriction**: Only assigns Bug 748404
- **Behavior**: Returns `None` for any work item ≠ 748404

```python
# PRODUCTION SAFETY: Only process Bug 748404
if work_item.id != 748404:
    return None
```

### **3. Duplicate Detector (`common/ai/duplicate.py`)**
- **Function**: `find_duplicates()`
- **Restriction**: Only detects duplicates for Bug 748404
- **Behavior**: Returns empty list `[]` for any work item ≠ 748404

```python
# PRODUCTION SAFETY: Only process Bug 748404
if work_item.id != 748404:
    return []
```

### **4. Priority Engine (`common/ai/priority.py`)**
- **Function**: `calculate_priority()`
- **Restriction**: Only calculates priority for Bug 748404
- **Behavior**: Returns existing priority for any work item ≠ 748404

```python
# PRODUCTION SAFETY: Only process Bug 748404
if work_item.id != 748404:
    return work_item.priority or 2  # Return existing or default
```

### **5. Search Client (`common/adapters/search_client.py`)**
- **Function**: `upsert_work_item()`
- **Restriction**: Only indexes Bug 748404
- **Behavior**: Silently returns without indexing for any work item ≠ 748404

```python
# PRODUCTION SAFETY: Only index Bug 748404
if work_item.id != 748404:
    return  # Skip indexing
```

### **6. Teams Client (`common/adapters/teams_client.py`)**
- **Function**: `send_triage_notification()`
- **Restriction**: Only sends notifications for Bug 748404
- **Behavior**: Returns `True` without sending for any work item ≠ 748404

```python
# PRODUCTION SAFETY: Only send notifications for Bug 748404
if work_item.id != 748404:
    return True  # Skip notification but don't break flow
```

### **7. Assignee Suggestions (`common/cards/teams_adaptive.py`)**
- **Function**: `_build_assignee_suggestions_section()`
- **Restriction**: Only shows suggestions for Bug 748404
- **Behavior**: Returns `None` for any work item ≠ 748404

```python
# Only show suggestions for Bug-748404 as requested
if work_item.id != 748404:
    return None
```

## 📊 **What Happens to Other Work Items**

### **Production Work Items (≠ 748404)**
- ✅ **Completely ignored** by all AI processing
- ✅ **No modifications** made to work items
- ✅ **No notifications** sent to Teams
- ✅ **No indexing** in search systems
- ✅ **No assignment** changes
- ✅ **No priority** changes
- ✅ **No duplicate** detection

### **Bug 748404 Only**
- 🎯 **Full AI triage** processing
- 🎯 **Assignment suggestions** with iteration context
- 🎯 **Teams notifications** with enhanced cards
- 🎯 **Search indexing** with comprehensive fields
- 🎯 **Duplicate detection** analysis
- 🎯 **Priority calculation** 
- 🎯 **Complete workflow** execution

## 🔧 **Usage Examples**

### **Testing Bug 748404**
```bash
# Process Bug 748404 specifically
curl "https://your-function-app.azurewebsites.net/api/process_workitem_webhook?work_item_id=748404"

# Default to Bug 748404
curl "https://your-function-app.azurewebsites.net/api/process_workitem_webhook"
```

### **Production Work Items (Ignored)**
```bash
# This will be ignored with safety message
curl "https://your-function-app.azurewebsites.net/api/process_workitem_webhook?work_item_id=12345"

# Response:
{
  "status": "ignored",
  "message": "PRODUCTION SAFETY: Only Bug 748404 is processed during development. Work item 12345 was ignored.",
  "work_item_id": 12345,
  "allowed_id": 748404
}
```

## 📋 **Logging and Monitoring**

All safety restrictions are logged with structured logging:

```json
{
  "level": "warning",
  "message": "PRODUCTION SAFETY: Assignment engine ignoring work item 12345 - only Bug 748404 is processed",
  "work_item_id": 12345,
  "allowed_id": 748404,
  "component": "assignment_engine"
}
```

## 🎯 **Benefits**

1. **Zero Risk**: No production work items can be accidentally modified
2. **Safe Testing**: Full system testing with Bug 748404 only
3. **Complete Isolation**: Production data remains untouched
4. **Easy Monitoring**: Clear logs show what's being processed vs ignored
5. **Reversible**: Easy to remove restrictions when ready for production

## 🔄 **Removing Restrictions (Future)**

When ready for production, simply remove the safety checks:

1. Remove `if work_item.id != 748404:` checks from all components
2. Update function documentation
3. Test with a broader set of work items
4. Deploy to production

## ⚡ **Current Status**

**Status**: ✅ **Production Safety Active**  
**Allowed Work Item**: Bug 748404 ONLY  
**Production Items**: Completely Protected  
**Testing**: Safe and Isolated  

## 🚨 **Important Notes**

- **Bug 748404 must exist** in Azure DevOps for testing
- **All other work items** are completely safe from modifications
- **Teams notifications** only sent for Bug 748404
- **Search indexing** only happens for Bug 748404
- **Assignment suggestions** only shown for Bug 748404
- **System can be tested end-to-end** without production risk

---

**Implementation Date**: 2025-10-07  
**Safety Level**: Maximum (Production Protected)  
**Test Work Item**: Bug 748404 Only  
**Production Risk**: Zero
