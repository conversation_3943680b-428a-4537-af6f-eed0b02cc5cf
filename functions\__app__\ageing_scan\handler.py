"""
Ageing Scan Handler - Timer-based scanning for unassigned/inactive/stuck work items
Replaces timer-based work item creation processing with focused ageing checks.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, Any, List

from ..common.utils.config import get_config
from ..common.utils.logging import log_structured
from ..common.adapters.ado_client import AdoClient
from ..common.services.store import IdempotencyStore

logger = logging.getLogger(__name__)


async def ageing_scan_handler() -> Dict[str, Any]:
    """
    Scan for ageing work items and send notifications.
    
    This function runs on a timer (every 15-30 minutes) and looks for:
    - Unassigned work items older than threshold
    - Inactive work items (no updates for X time)
    - Stuck work items (same state for too long)
    
    Returns:
        Processing results summary
    """
    start_time = datetime.utcnow()
    
    try:
        config = get_config()
        
        log_structured(
            logger,
            "info",
            "Starting ageing scan",
            extra={
                "timestamp": start_time.isoformat(),
                "read_only": config.READ_ONLY
            }
        )
        
        # Initialize clients
        ado_client = AdoClient(config)
        store = IdempotencyStore(config)
        
        # Define ageing thresholds (configurable)
        thresholds = {
            "unassigned_hours": 4,      # Unassigned for more than 4 hours
            "inactive_hours": 24,       # No updates for more than 24 hours
            "stuck_hours": 72,          # Same state for more than 72 hours
            "critical_hours": 2         # Critical items unassigned for more than 2 hours
        }
        
        results = {
            "scan_timestamp": start_time.isoformat(),
            "thresholds": thresholds,
            "items_found": {
                "unassigned": [],
                "inactive": [],
                "stuck": [],
                "critical_unassigned": []
            },
            "notifications_sent": 0,
            "errors": []
        }
        
        # Scan for unassigned work items
        unassigned_items = await scan_unassigned_items(ado_client, config, thresholds)
        results["items_found"]["unassigned"] = unassigned_items
        
        # Scan for inactive work items
        inactive_items = await scan_inactive_items(ado_client, config, thresholds)
        results["items_found"]["inactive"] = inactive_items
        
        # Scan for stuck work items
        stuck_items = await scan_stuck_items(ado_client, config, thresholds)
        results["items_found"]["stuck"] = stuck_items
        
        # Scan for critical unassigned items (higher priority)
        critical_items = await scan_critical_unassigned_items(ado_client, config, thresholds)
        results["items_found"]["critical_unassigned"] = critical_items
        
        # Process notifications with deduplication
        all_items = unassigned_items + inactive_items + stuck_items + critical_items
        notifications_sent = await process_ageing_notifications(store, config, all_items)
        results["notifications_sent"] = notifications_sent
        
        duration_ms = (datetime.utcnow() - start_time).total_seconds() * 1000
        
        log_structured(
            logger,
            "info",
            "Ageing scan completed",
            extra={
                "duration_ms": duration_ms,
                "total_items": len(all_items),
                "notifications_sent": notifications_sent,
                "results": results
            }
        )
        
        return results
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error in ageing scan: {e}",
            extra={
                "error": str(e),
                "duration_ms": (datetime.utcnow() - start_time).total_seconds() * 1000
            }
        )
        raise


async def scan_unassigned_items(
    ado_client: AdoClient, 
    config, 
    thresholds: Dict[str, int]
) -> List[Dict[str, Any]]:
    """Scan for unassigned work items older than threshold."""
    
    try:
        # Calculate cutoff time
        cutoff_time = datetime.utcnow() - timedelta(hours=thresholds["unassigned_hours"])
        
        # WIQL query for unassigned items
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.State], [System.CreatedDate], [System.Priority]
        FROM WorkItems
        WHERE [System.AssignedTo] = ''
        AND [System.CreatedDate] < '{cutoff_time.isoformat()}'
        AND [System.State] NOT IN ('Closed', 'Resolved', 'Done')
        ORDER BY [System.Priority] ASC, [System.CreatedDate] ASC
        """
        
        # Execute query (with fallback if WIQL fails)
        try:
            query_result = await ado_client.query_work_items(wiql_query)
            work_items = query_result.get('workItems', [])
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"WIQL query failed for unassigned items: {e}",
                extra={"error": str(e)}
            )
            # Fallback: return empty list or use alternative method
            work_items = []
        
        # Process and enrich results
        unassigned_items = []
        for item in work_items[:50]:  # Limit to 50 items per scan
            work_item_id = item.get('id')
            if work_item_id:
                # Get full work item details
                full_item = await ado_client.get_work_item(work_item_id)
                if full_item:
                    unassigned_items.append({
                        "id": work_item_id,
                        "title": full_item.get('fields', {}).get('System.Title', ''),
                        "state": full_item.get('fields', {}).get('System.State', ''),
                        "priority": full_item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 3),
                        "created_date": full_item.get('fields', {}).get('System.CreatedDate', ''),
                        "age_hours": (datetime.utcnow() - datetime.fromisoformat(
                            full_item.get('fields', {}).get('System.CreatedDate', '').replace('Z', '+00:00')
                        )).total_seconds() / 3600,
                        "alert_type": "unassigned"
                    })
        
        log_structured(
            logger,
            "info",
            f"Found {len(unassigned_items)} unassigned items",
            extra={"count": len(unassigned_items), "threshold_hours": thresholds["unassigned_hours"]}
        )
        
        return unassigned_items
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error scanning unassigned items: {e}",
            extra={"error": str(e)}
        )
        return []


async def scan_inactive_items(
    ado_client: AdoClient, 
    config, 
    thresholds: Dict[str, int]
) -> List[Dict[str, Any]]:
    """Scan for inactive work items (no updates for X time)."""
    
    try:
        # Calculate cutoff time
        cutoff_time = datetime.utcnow() - timedelta(hours=thresholds["inactive_hours"])
        
        # WIQL query for inactive items
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.State], [System.ChangedDate], [System.AssignedTo]
        FROM WorkItems
        WHERE [System.ChangedDate] < '{cutoff_time.isoformat()}'
        AND [System.State] NOT IN ('Closed', 'Resolved', 'Done')
        AND [System.AssignedTo] <> ''
        ORDER BY [System.ChangedDate] ASC
        """
        
        # Execute query with fallback
        try:
            query_result = await ado_client.query_work_items(wiql_query)
            work_items = query_result.get('workItems', [])
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"WIQL query failed for inactive items: {e}",
                extra={"error": str(e)}
            )
            work_items = []
        
        # Process results
        inactive_items = []
        for item in work_items[:30]:  # Limit to 30 items per scan
            work_item_id = item.get('id')
            if work_item_id:
                full_item = await ado_client.get_work_item(work_item_id)
                if full_item:
                    changed_date = full_item.get('fields', {}).get('System.ChangedDate', '')
                    if changed_date:
                        inactive_items.append({
                            "id": work_item_id,
                            "title": full_item.get('fields', {}).get('System.Title', ''),
                            "state": full_item.get('fields', {}).get('System.State', ''),
                            "assigned_to": full_item.get('fields', {}).get('System.AssignedTo', {}).get('displayName', ''),
                            "changed_date": changed_date,
                            "inactive_hours": (datetime.utcnow() - datetime.fromisoformat(
                                changed_date.replace('Z', '+00:00')
                            )).total_seconds() / 3600,
                            "alert_type": "inactive"
                        })
        
        log_structured(
            logger,
            "info",
            f"Found {len(inactive_items)} inactive items",
            extra={"count": len(inactive_items), "threshold_hours": thresholds["inactive_hours"]}
        )
        
        return inactive_items
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error scanning inactive items: {e}",
            extra={"error": str(e)}
        )
        return []


async def scan_stuck_items(
    ado_client: AdoClient, 
    config, 
    thresholds: Dict[str, int]
) -> List[Dict[str, Any]]:
    """Scan for stuck work items (same state for too long)."""
    
    # TODO: Implement stuck item detection
    # This would require tracking state change history
    # For now, return empty list
    
    log_structured(
        logger,
        "info",
        "Stuck item scanning not yet implemented",
        extra={"threshold_hours": thresholds["stuck_hours"]}
    )
    
    return []


async def scan_critical_unassigned_items(
    ado_client: AdoClient, 
    config, 
    thresholds: Dict[str, int]
) -> List[Dict[str, Any]]:
    """Scan for critical priority unassigned items."""
    
    try:
        # Calculate cutoff time for critical items (shorter threshold)
        cutoff_time = datetime.utcnow() - timedelta(hours=thresholds["critical_hours"])
        
        # WIQL query for critical unassigned items
        wiql_query = f"""
        SELECT [System.Id], [System.Title], [System.State], [System.CreatedDate], [System.Priority]
        FROM WorkItems
        WHERE [System.AssignedTo] = ''
        AND [Microsoft.VSTS.Common.Priority] <= 2
        AND [System.CreatedDate] < '{cutoff_time.isoformat()}'
        AND [System.State] NOT IN ('Closed', 'Resolved', 'Done')
        ORDER BY [System.Priority] ASC, [System.CreatedDate] ASC
        """
        
        # Execute query with fallback
        try:
            query_result = await ado_client.query_work_items(wiql_query)
            work_items = query_result.get('workItems', [])
        except Exception as e:
            log_structured(
                logger,
                "warning",
                f"WIQL query failed for critical items: {e}",
                extra={"error": str(e)}
            )
            work_items = []
        
        # Process results
        critical_items = []
        for item in work_items[:20]:  # Limit to 20 critical items per scan
            work_item_id = item.get('id')
            if work_item_id:
                full_item = await ado_client.get_work_item(work_item_id)
                if full_item:
                    critical_items.append({
                        "id": work_item_id,
                        "title": full_item.get('fields', {}).get('System.Title', ''),
                        "state": full_item.get('fields', {}).get('System.State', ''),
                        "priority": full_item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 3),
                        "created_date": full_item.get('fields', {}).get('System.CreatedDate', ''),
                        "age_hours": (datetime.utcnow() - datetime.fromisoformat(
                            full_item.get('fields', {}).get('System.CreatedDate', '').replace('Z', '+00:00')
                        )).total_seconds() / 3600,
                        "alert_type": "critical_unassigned"
                    })
        
        log_structured(
            logger,
            "info",
            f"Found {len(critical_items)} critical unassigned items",
            extra={"count": len(critical_items), "threshold_hours": thresholds["critical_hours"]}
        )
        
        return critical_items
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error scanning critical items: {e}",
            extra={"error": str(e)}
        )
        return []


async def process_ageing_notifications(
    store: IdempotencyStore,
    config,
    items: List[Dict[str, Any]]
) -> int:
    """Process notifications for ageing items with deduplication."""
    
    notifications_sent = 0
    
    for item in items:
        try:
            # Create deduplication key
            dedup_key = f"ageing:{item['id']}:{item['alert_type']}"
            
            # Check if notification was sent recently (60 min window)
            if await store.is_recent_notification(dedup_key, minutes=60):
                log_structured(
                    logger,
                    "debug",
                    f"Skipping duplicate notification for {item['id']}",
                    extra={"work_item_id": item['id'], "alert_type": item['alert_type']}
                )
                continue
            
            # TODO: Send notification (Teams/email)
            # This would integrate with the notify.py module
            
            # Mark notification as sent
            await store.mark_notification_sent(dedup_key)
            notifications_sent += 1
            
            log_structured(
                logger,
                "info",
                f"Sent ageing notification for work item {item['id']}",
                extra=item
            )
            
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending notification for work item {item['id']}: {e}",
                extra={"work_item_id": item['id'], "error": str(e)}
            )
    
    return notifications_sent
