{"$schema": "https://schema.management.azure.com/providers/Microsoft.Logic/schemas/2016-06-01/workflowdefinition.json#", "contentVersion": "1.0.0.0", "parameters": {"$connections": {"defaultValue": {}, "type": "Object"}}, "triggers": {"When_a_HTTP_request_is_received": {"type": "Request", "kind": "Http", "inputs": {"schema": {"type": "object", "properties": {"Body": {"type": "string"}, "Subject": {"type": "string"}, "To": {"type": "string"}, "Attachments": {"type": "boolean"}, "attachmentName": {"type": "string"}, "ContentBytes": {"type": "string"}, "Name": {"type": "string"}}, "required": ["Body", "Subject", "To"]}}}}, "actions": {"Check_Email_Type": {"type": "If", "expression": {"and": [{"equals": ["@triggerBody()?['attachmentName']", ""]}, {"equals": ["@triggerBody()?['Attachments']", true]}]}, "actions": {"Send_Simple_Email": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@triggerBody()?['To']", "Subject": "@triggerBody()?['Subject']", "Body": "@triggerBody()?['Body']", "IsHtml": true, "From": "<EMAIL>"}}}}, "else": {"actions": {"Send_Branded_Email_With_Attachment": {"type": "ApiConnection", "inputs": {"host": {"connection": {"name": "@parameters('$connections')['office365']['connectionId']"}}, "method": "post", "path": "/v2/Mail", "body": {"To": "@triggerBody()?['To']", "Subject": "@triggerBody()?['Subject']", "Body": "@{concat('<html><body style=\"font-family: Segoe UI, Arial, sans-serif; line-height: 1.6; color: #333;\"><div style=\"max-width: 800px; margin: 0 auto; padding: 20px;\"><div style=\"background: linear-gradient(135deg, #E10600 0%, #C41E3A 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0; text-align: center;\"><img src=\"https://www.virgin-atlantic.com/content/dam/virgin-atlantic/images/logos/virgin-atlantic-logo-white.png\" alt=\"Virgin Atlantic\" style=\"height: 40px; margin-bottom: 10px;\"><h1 style=\"margin: 0; font-size: 24px;\">Auto Defect Triage System</h1><p style=\"margin: 10px 0 0 0; opacity: 0.9;\">Virgin Atlantic - Air4 Channels Testing</p></div><div style=\"background: #f8f9fa; padding: 20px; border-radius: 0 0 8px 8px; border: 1px solid #e9ecef;\">',triggerBody()?['Body'],'</div><div style=\"background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; border-radius: 0 0 8px 8px;\">This is an automated notification from the Auto Defect Triage System<br>Generated on ', formatDateTime(utcNow(), 'MMM dd, yyyy HH:mm'), ' UTC</div></div></body></html>')}", "IsHtml": true, "From": "<EMAIL>", "Attachments": [{"Name": "@triggerBody()?['Name']", "ContentBytes": "@triggerBody()?['ContentBytes']"}]}}}}}, "runAfter": {}}, "Response": {"type": "Response", "kind": "Http", "inputs": {"statusCode": 200, "body": {"status": "success", "message": "Email notification processed", "timestamp": "@utcNow()", "email_type": "@{if(and(equals(triggerBody()?['attachmentName'], ''), equals(triggerBody()?['Attachments'], true)), 'simple', 'branded_with_attachment')}"}}, "runAfter": {"Check_Email_Type": ["Succeeded", "Failed"]}}}}