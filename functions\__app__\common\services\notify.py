"""
Notification Service
Handles Teams notifications and ADO comments with support for multiple modes.
"""

import json
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime

from ..adapters.teams_client import TeamsClient
from ..adapters.ado_client import AdoClient
from ..utils.config import get_config
from ..utils.logging import log_structured

logger = logging.getLogger(__name__)


class NotificationService:
    """Service for sending notifications and managing communication."""
    
    def __init__(self, teams_client: TeamsClient, ado_client: AdoClient, config=None):
        self.teams_client = teams_client
        self.ado_client = ado_client
        self.config = config or get_config()
    
    async def send_triage_notification(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Send comprehensive triage notification.
        
        Args:
            work_item: Work item that was triaged
            triage_results: Results from triage analysis
            historical_context: Historical analysis results
            
        Returns:
            Notification results
        """
        try:
            notification_results = {
                "teams_sent": False,
                "ado_comment_added": False,
                "errors": []
            }
            
            # Send Teams notification if enabled
            if self.config.TEAMS_NOTIFICATIONS_ENABLED:
                teams_result = await self._send_teams_notification(
                    work_item, triage_results, historical_context
                )
                notification_results.update(teams_result)
            
            # Add ADO comment if not in read-only mode
            if not self.config.READ_ONLY or self.config.ALLOW_COMMENTS_IN_READ_ONLY:
                ado_result = await self._add_ado_comment(
                    work_item, triage_results, historical_context
                )
                notification_results.update(ado_result)
            
            log_structured(
                logger,
                "info",
                f"Sent triage notification for work item {work_item.get('id')}",
                extra={
                    "work_item_id": work_item.get('id'),
                    "teams_sent": notification_results["teams_sent"],
                    "ado_comment_added": notification_results["ado_comment_added"]
                }
            )
            
            return notification_results
            
        except Exception as e:
            logger.error(f"Error sending triage notification: {e}")
            return {
                "teams_sent": False,
                "ado_comment_added": False,
                "errors": [str(e)]
            }
    
    async def send_ageing_notification(
        self,
        ageing_items: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Send notification for ageing work items.
        
        Args:
            ageing_items: List of ageing work items
            
        Returns:
            Notification results
        """
        try:
            if not ageing_items:
                return {"teams_sent": False, "message": "No ageing items to notify"}
            
            # Group items by alert type
            grouped_items = {}
            for item in ageing_items:
                alert_type = item.get('alert_type', 'unknown')
                if alert_type not in grouped_items:
                    grouped_items[alert_type] = []
                grouped_items[alert_type].append(item)
            
            # Send Teams notification
            teams_result = await self._send_ageing_teams_notification(grouped_items)
            
            return teams_result
            
        except Exception as e:
            logger.error(f"Error sending ageing notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    async def _send_teams_notification(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send Teams notification for work item triage."""
        try:
            teams_mode = self.config.get_teams_mode()
            
            if teams_mode == "webhook":
                return await self._send_webhook_notification(work_item, triage_results, historical_context)
            elif teams_mode == "bot":
                return await self._send_bot_notification(work_item, triage_results, historical_context)
            elif teams_mode == "logic_app":
                return await self._send_logic_app_notification(work_item, triage_results, historical_context)
            else:
                return {"teams_sent": False, "message": "Teams notifications disabled"}
                
        except Exception as e:
            logger.error(f"Error sending Teams notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    async def _send_webhook_notification(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send simple webhook notification."""
        try:
            # Build adaptive card for webhook
            card = self._build_triage_card(work_item, triage_results, historical_context, interactive=False)
            
            # Send via webhook
            result = await self.teams_client.send_webhook_message(card)
            
            return {"teams_sent": result, "mode": "webhook"}
            
        except Exception as e:
            logger.error(f"Error sending webhook notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    async def _send_bot_notification(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send interactive bot notification."""
        try:
            if self.config.DISABLE_BOT_ACTIONS:
                # Send non-interactive card
                card = self._build_triage_card(work_item, triage_results, historical_context, interactive=False)
            else:
                # Send interactive card with actions
                card = self._build_triage_card(work_item, triage_results, historical_context, interactive=True)
            
            # Send via bot framework
            result = await self.teams_client.send_bot_message(card)
            
            return {"teams_sent": result, "mode": "bot"}
            
        except Exception as e:
            logger.error(f"Error sending bot notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    async def _send_logic_app_notification(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Send notification via Logic App."""
        try:
            # Build payload for Logic App
            payload = self._build_logic_app_payload(work_item, triage_results, historical_context)
            
            # Send via Logic App
            result = await self.teams_client.send_logic_app_message(payload)
            
            return {"teams_sent": result, "mode": "logic_app"}
            
        except Exception as e:
            logger.error(f"Error sending Logic App notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    def _build_triage_card(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any],
        interactive: bool = False
    ) -> Dict[str, Any]:
        """Build adaptive card for triage notification."""
        fields = work_item.get('fields', {})
        work_item_id = work_item.get('id')
        title = fields.get('System.Title', 'Unknown Title')
        state = fields.get('System.State', 'Unknown')
        priority = fields.get('Microsoft.VSTS.Common.Priority', 3)
        
        # Get triage suggestions
        assignee_suggestions = triage_results.get('assignee_suggestions', [])
        priority_suggestion = triage_results.get('priority_suggestion', {})
        
        # Get historical context
        similar_count = historical_context.get('summary', {}).get('similar_count', 0)
        duplicate_count = historical_context.get('summary', {}).get('duplicate_count', 0)
        
        # Build card structure
        card = {
            "type": "AdaptiveCard",
            "version": "1.4",
            "body": [
                {
                    "type": "TextBlock",
                    "text": f"🎯 Work Item Triage: #{work_item_id}",
                    "weight": "Bolder",
                    "size": "Medium",
                    "color": "Accent"
                },
                {
                    "type": "TextBlock",
                    "text": title,
                    "weight": "Bolder",
                    "wrap": True
                },
                {
                    "type": "FactSet",
                    "facts": [
                        {"title": "ID", "value": str(work_item_id)},
                        {"title": "State", "value": state},
                        {"title": "Priority", "value": self._priority_to_text(priority)},
                        {"title": "Similar Items", "value": str(similar_count)},
                        {"title": "Potential Duplicates", "value": str(duplicate_count)}
                    ]
                }
            ]
        }
        
        # Add assignee suggestions
        if assignee_suggestions:
            top_suggestion = assignee_suggestions[0]
            card["body"].append({
                "type": "TextBlock",
                "text": "📋 **Assignment Suggestions**",
                "weight": "Bolder",
                "spacing": "Medium"
            })
            
            suggestions_text = []
            for i, suggestion in enumerate(assignee_suggestions[:3], 1):
                confidence_pct = int(suggestion['confidence'] * 100)
                suggestions_text.append(
                    f"{i}. **{suggestion['assignee']}** ({confidence_pct}% confidence)\n"
                    f"   {suggestion['rationale']}"
                )
            
            card["body"].append({
                "type": "TextBlock",
                "text": "\n\n".join(suggestions_text),
                "wrap": True
            })
        
        # Add priority suggestion
        if priority_suggestion.get('confidence', 0) > 0.5:
            suggested_priority = priority_suggestion.get('priority', 3)
            confidence_pct = int(priority_suggestion.get('confidence', 0) * 100)
            rationale = priority_suggestion.get('rationale', '')
            
            card["body"].append({
                "type": "TextBlock",
                "text": f"⚡ **Priority Suggestion**: {self._priority_to_text(suggested_priority)} ({confidence_pct}% confidence)",
                "weight": "Bolder",
                "spacing": "Medium"
            })
            
            if rationale:
                card["body"].append({
                    "type": "TextBlock",
                    "text": rationale,
                    "wrap": True,
                    "isSubtle": True
                })
        
        # Add interactive actions if enabled
        if interactive and not self.config.DISABLE_BOT_ACTIONS:
            actions = self._build_interactive_actions(work_item_id, assignee_suggestions, priority_suggestion)
            if actions:
                card["actions"] = actions
        
        # Add view link
        ado_url = f"https://dev.azure.com/{self.config.ADO_ORG}/{fields.get('System.TeamProject', '')}/_workitems/edit/{work_item_id}"
        card["actions"] = card.get("actions", [])
        card["actions"].append({
            "type": "Action.OpenUrl",
            "title": "View in ADO",
            "url": ado_url
        })
        
        return card
    
    def _build_interactive_actions(
        self,
        work_item_id: int,
        assignee_suggestions: List[Dict[str, Any]],
        priority_suggestion: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Build interactive actions for bot cards."""
        actions = []
        
        # Assign actions
        if assignee_suggestions:
            for suggestion in assignee_suggestions[:2]:  # Top 2 suggestions
                actions.append({
                    "type": "Action.Execute",
                    "title": f"Assign to {suggestion['assignee']}",
                    "verb": "assign",
                    "data": {
                        "work_item_id": work_item_id,
                        "assignee": suggestion['assignee'],
                        "confidence": suggestion['confidence']
                    }
                })
        
        # Priority actions
        if priority_suggestion.get('confidence', 0) > 0.5:
            suggested_priority = priority_suggestion.get('priority', 3)
            actions.append({
                "type": "Action.Execute",
                "title": f"Set Priority: {self._priority_to_text(suggested_priority)}",
                "verb": "set_priority",
                "data": {
                    "work_item_id": work_item_id,
                    "priority": suggested_priority,
                    "confidence": priority_suggestion.get('confidence', 0)
                }
            })
        
        # Mark as duplicate action
        actions.append({
            "type": "Action.Execute",
            "title": "Mark as Duplicate",
            "verb": "mark_duplicate",
            "data": {
                "work_item_id": work_item_id
            }
        })
        
        return actions
    
    def _priority_to_text(self, priority: int) -> str:
        """Convert priority number to text."""
        priority_map = {
            1: "🔴 Critical",
            2: "🟠 High", 
            3: "🟡 Medium",
            4: "🟢 Low"
        }
        return priority_map.get(priority, f"Priority {priority}")
    
    async def _send_ageing_teams_notification(
        self,
        grouped_items: Dict[str, List[Dict[str, Any]]]
    ) -> Dict[str, Any]:
        """Send Teams notification for ageing items."""
        try:
            # Build ageing summary card
            card = self._build_ageing_card(grouped_items)
            
            # Send based on configured mode
            teams_mode = self.config.get_teams_mode()
            
            if teams_mode == "webhook":
                result = await self.teams_client.send_webhook_message(card)
            elif teams_mode == "bot":
                result = await self.teams_client.send_bot_message(card)
            elif teams_mode == "logic_app":
                payload = {"card": card, "type": "ageing_summary"}
                result = await self.teams_client.send_logic_app_message(payload)
            else:
                return {"teams_sent": False, "message": "Teams notifications disabled"}
            
            return {"teams_sent": result, "mode": teams_mode}
            
        except Exception as e:
            logger.error(f"Error sending ageing Teams notification: {e}")
            return {"teams_sent": False, "errors": [str(e)]}
    
    def _build_ageing_card(self, grouped_items: Dict[str, List[Dict[str, Any]]]) -> Dict[str, Any]:
        """Build adaptive card for ageing items."""
        total_items = sum(len(items) for items in grouped_items.values())
        
        card = {
            "type": "AdaptiveCard",
            "version": "1.4",
            "body": [
                {
                    "type": "TextBlock",
                    "text": f"⏰ Ageing Work Items Alert ({total_items} items)",
                    "weight": "Bolder",
                    "size": "Medium",
                    "color": "Warning"
                }
            ]
        }
        
        # Add sections for each alert type
        for alert_type, items in grouped_items.items():
            if not items:
                continue
            
            alert_title = {
                "unassigned": "🔍 Unassigned Items",
                "inactive": "💤 Inactive Items", 
                "stuck": "🚫 Stuck Items",
                "critical_unassigned": "🚨 Critical Unassigned Items"
            }.get(alert_type, f"📋 {alert_type.title()} Items")
            
            card["body"].append({
                "type": "TextBlock",
                "text": f"{alert_title} ({len(items)})",
                "weight": "Bolder",
                "spacing": "Medium"
            })
            
            # Show top items
            for item in items[:5]:  # Show max 5 items per type
                age_hours = item.get('age_hours', 0)
                age_text = f"{age_hours:.1f}h" if age_hours < 24 else f"{age_hours/24:.1f}d"
                
                card["body"].append({
                    "type": "TextBlock",
                    "text": f"• **#{item.get('id')}**: {item.get('title', 'Unknown')} (Age: {age_text})",
                    "wrap": True,
                    "spacing": "Small"
                })
            
            if len(items) > 5:
                card["body"].append({
                    "type": "TextBlock",
                    "text": f"... and {len(items) - 5} more items",
                    "isSubtle": True,
                    "spacing": "Small"
                })
        
        return card
    
    def _build_logic_app_payload(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Build payload for Logic App notification."""
        return {
            "work_item": {
                "id": work_item.get('id'),
                "title": work_item.get('fields', {}).get('System.Title', ''),
                "state": work_item.get('fields', {}).get('System.State', ''),
                "priority": work_item.get('fields', {}).get('Microsoft.VSTS.Common.Priority', 3)
            },
            "triage_results": triage_results,
            "historical_context": historical_context,
            "timestamp": datetime.utcnow().isoformat()
        }
    
    async def _add_ado_comment(
        self,
        work_item: Dict[str, Any],
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Add triage comment to ADO work item."""
        try:
            work_item_id = work_item.get('id')
            if not work_item_id:
                return {"ado_comment_added": False, "errors": ["No work item ID"]}
            
            # Build comment text
            comment_text = self._build_ado_comment(triage_results, historical_context)
            
            # Add comment to work item
            result = await self.ado_client.add_work_item_comment(work_item_id, comment_text)
            
            return {"ado_comment_added": result}
            
        except Exception as e:
            logger.error(f"Error adding ADO comment: {e}")
            return {"ado_comment_added": False, "errors": [str(e)]}
    
    def _build_ado_comment(
        self,
        triage_results: Dict[str, Any],
        historical_context: Dict[str, Any]
    ) -> str:
        """Build ADO comment text for triage results."""
        lines = ["🤖 **AutoDefectTriage Analysis**", ""]
        
        # Assignee suggestions
        assignee_suggestions = triage_results.get('assignee_suggestions', [])
        if assignee_suggestions:
            lines.append("**Assignment Suggestions:**")
            for i, suggestion in enumerate(assignee_suggestions[:3], 1):
                confidence_pct = int(suggestion['confidence'] * 100)
                lines.append(f"{i}. {suggestion['assignee']} ({confidence_pct}% confidence)")
                lines.append(f"   Rationale: {suggestion['rationale']}")
            lines.append("")
        
        # Priority suggestion
        priority_suggestion = triage_results.get('priority_suggestion', {})
        if priority_suggestion.get('confidence', 0) > 0.5:
            priority = priority_suggestion.get('priority', 3)
            confidence_pct = int(priority_suggestion.get('confidence', 0) * 100)
            rationale = priority_suggestion.get('rationale', '')
            
            lines.append(f"**Priority Suggestion:** {self._priority_to_text(priority)} ({confidence_pct}% confidence)")
            if rationale:
                lines.append(f"Rationale: {rationale}")
            lines.append("")
        
        # Historical context
        similar_count = historical_context.get('summary', {}).get('similar_count', 0)
        duplicate_count = historical_context.get('summary', {}).get('duplicate_count', 0)
        
        if similar_count > 0:
            lines.append(f"**Historical Analysis:** Found {similar_count} similar items")
            
            resolution_analysis = historical_context.get('resolution_analysis', {})
            recommendations = resolution_analysis.get('recommendations', [])
            if recommendations:
                lines.append("**Recommendations:**")
                for rec in recommendations:
                    lines.append(f"• {rec}")
            lines.append("")
        
        if duplicate_count > 0:
            lines.append(f"⚠️ **Potential Duplicates:** {duplicate_count} items found")
            lines.append("")
        
        lines.append(f"*Analysis completed at {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S')} UTC*")
        
        return "\n".join(lines)
    
    async def handle_bot_action(
        self,
        action_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Handle interactive bot action callbacks.
        
        Args:
            action_data: Action data from Teams bot callback
            
        Returns:
            Action result
        """
        try:
            verb = action_data.get('verb')
            work_item_id = action_data.get('data', {}).get('work_item_id')
            
            if not work_item_id:
                return {"success": False, "error": "No work item ID provided"}
            
            if self.config.READ_ONLY and verb not in ['view', 'comment']:
                return {"success": False, "error": "System is in read-only mode"}
            
            if verb == "assign":
                return await self._handle_assign_action(action_data)
            elif verb == "set_priority":
                return await self._handle_priority_action(action_data)
            elif verb == "mark_duplicate":
                return await self._handle_duplicate_action(action_data)
            else:
                return {"success": False, "error": f"Unknown action verb: {verb}"}
                
        except Exception as e:
            logger.error(f"Error handling bot action: {e}")
            return {"success": False, "error": str(e)}
    
    async def _handle_assign_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle assignment action."""
        try:
            data = action_data.get('data', {})
            work_item_id = data.get('work_item_id')
            assignee = data.get('assignee')
            
            # Update work item assignment
            result = await self.ado_client.update_work_item_assignment(work_item_id, assignee)
            
            if result:
                return {"success": True, "message": f"Assigned work item {work_item_id} to {assignee}"}
            else:
                return {"success": False, "error": "Failed to update assignment"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_priority_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle priority change action."""
        try:
            data = action_data.get('data', {})
            work_item_id = data.get('work_item_id')
            priority = data.get('priority')
            
            # Update work item priority
            result = await self.ado_client.update_work_item_priority(work_item_id, priority)
            
            if result:
                priority_text = self._priority_to_text(priority)
                return {"success": True, "message": f"Set priority of work item {work_item_id} to {priority_text}"}
            else:
                return {"success": False, "error": "Failed to update priority"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    async def _handle_duplicate_action(self, action_data: Dict[str, Any]) -> Dict[str, Any]:
        """Handle mark as duplicate action."""
        try:
            data = action_data.get('data', {})
            work_item_id = data.get('work_item_id')
            
            # Add comment indicating duplicate status
            comment = "🔄 Marked as potential duplicate by AutoDefectTriage bot action"
            result = await self.ado_client.add_work_item_comment(work_item_id, comment)
            
            if result:
                return {"success": True, "message": f"Marked work item {work_item_id} as potential duplicate"}
            else:
                return {"success": False, "error": "Failed to add duplicate comment"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
