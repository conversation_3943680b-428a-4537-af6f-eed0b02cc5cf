#!/usr/bin/env python3
"""
Test script to verify configuration loading.
"""

import os
import sys
import json

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

def test_environment_variables():
    """Test what environment variables are available."""
    
    print("🔍 Environment Variables Check")
    print("=" * 50)
    
    # Check if we're loading from local.settings.json
    local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
    
    if os.path.exists(local_settings_path):
        print(f"✅ Found local.settings.json at: {local_settings_path}")
        
        try:
            with open(local_settings_path, 'r') as f:
                settings = json.load(f)
            
            values = settings.get('Values', {})
            print(f"📊 Found {len(values)} settings in local.settings.json")
            
            # Set environment variables from local.settings.json
            for key, value in values.items():
                os.environ[key] = str(value)
                print(f"   Set {key} = {value[:20]}{'...' if len(str(value)) > 20 else ''}")
            
        except Exception as e:
            print(f"❌ Error reading local.settings.json: {e}")
    else:
        print(f"❌ local.settings.json not found at: {local_settings_path}")
    
    # Check specific ADO-related environment variables
    print("\n🔍 ADO Configuration Check")
    print("-" * 30)
    
    ado_vars = [
        'ADO_ORGANIZATION', 'ADO_PROJECT', 'ADO_PAT_TOKEN',
        'AZURE_SEARCH_SERVICE_NAME', 'AZURE_SEARCH_ADMIN_KEY',
        'SAFETY_ONLY_WORKITEM_ID', 'READ_ONLY'
    ]
    
    for var in ado_vars:
        value = os.getenv(var)
        if value:
            display_value = value[:10] + '...' if len(value) > 10 else value
            print(f"   ✅ {var} = {display_value}")
        else:
            print(f"   ❌ {var} = Not set")


def test_config_object():
    """Test the Config object loading."""
    
    print("\n🔍 Config Object Test")
    print("=" * 30)
    
    try:
        from __app__.common.utils.config import get_config
        
        config = get_config()
        
        print(f"✅ Config object created successfully")
        print(f"   ADO_ORGANIZATION: {config.ADO_ORGANIZATION}")
        print(f"   ADO_PROJECT: {config.ADO_PROJECT}")
        print(f"   ADO_PAT_TOKEN: {'***' + config.ADO_PAT_TOKEN[-4:] if config.ADO_PAT_TOKEN else 'Not set'}")
        print(f"   SAFETY_ONLY_WORKITEM_ID: {config.SAFETY_ONLY_WORKITEM_ID}")
        print(f"   READ_ONLY: {config.READ_ONLY}")
        print(f"   VECTOR_BACKEND: {config.VECTOR_BACKEND}")
        
        # Test the helper methods
        print(f"   is_work_item_allowed(748404): {config.is_work_item_allowed(748404)}")
        print(f"   get_teams_mode(): {config.get_teams_mode()}")
        
        return config
        
    except Exception as e:
        print(f"❌ Error creating config object: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_ado_connection():
    """Test ADO connection with the loaded config."""
    
    print("\n🔍 ADO Connection Test")
    print("=" * 30)
    
    try:
        config = test_config_object()
        if not config:
            return False
        
        from __app__.common.adapters.ado_client import AdoClient
        
        ado_client = AdoClient(config)
        
        # Test the connection by trying to get a work item
        print("🔗 Testing ADO connection...")
        
        # Build the URL to see what we're trying to connect to
        base_url = f"https://dev.azure.com/{config.ADO_ORGANIZATION}"
        print(f"   Base URL: {base_url}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing ADO connection: {e}")
        return False


def main():
    """Main test function."""
    
    print("🧪 Configuration Loading Test")
    print("=" * 50)
    
    # Test environment variable loading
    test_environment_variables()
    
    # Test config object creation
    config = test_config_object()
    
    # Test ADO connection setup
    test_ado_connection()
    
    print("\n" + "=" * 50)
    print("✅ Configuration test complete")


if __name__ == "__main__":
    main()
