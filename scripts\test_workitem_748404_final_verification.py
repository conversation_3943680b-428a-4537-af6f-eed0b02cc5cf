#!/usr/bin/env python3
"""
Final verification test for work item 748404 with the new REAL assignee system.
This test verifies that the complete triage pipeline works correctly.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.common.utils.config import get_config
from __app__.common.services.triage import TriageService

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_workitem_748404_final():
    """Final test for work item 748404 with real assignee suggestions."""
    
    print("🎯 FINAL VERIFICATION - Work Item 748404")
    print("=" * 60)
    print("✅ REAL ASSIGNEES ONLY - No hallucination allowed")
    print("🔍 Testing complete triage pipeline")
    
    try:
        config = get_config()
        triage_service = TriageService(config)
        
        # Work item 748404 details
        work_item_id = 748404
        title = "Test - New defect"
        description = "Testing the assignee suggestion system with real data"
        work_item_type = "Bug"
        
        print(f"\n📋 Work Item Details:")
        print(f"   ID: {work_item_id}")
        print(f"   Title: {title}")
        print(f"   Type: {work_item_type}")
        
        # Create realistic similar items based on actual ADO patterns
        similar_items = [
            {
                'id': 752662,  # Real work item ID from the system
                'score': 0.89,
                'title': 'Authentication service timeout',
                'assigned_to': 'John Doe <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Air4 Channels Testing\\AI Testing - Defect Management',
                'iteration_path': 'Air4 Channels Testing\\AI Testing - Defect Management\\Sprint 1',
                'created_date': '2025-09-15T10:30:00Z',
                'priority': 2
            },
            {
                'id': 748401,
                'score': 0.82,
                'title': 'Login system defect',
                'assigned_to': 'Jane Smith <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Resolved',
                'area_path': 'Air4 Channels Testing\\AI Testing - Defect Management',
                'iteration_path': 'Air4 Channels Testing\\AI Testing - Defect Management\\Sprint 2',
                'created_date': '2025-09-20T14:15:00Z',
                'priority': 1
            },
            {
                'id': 748402,
                'score': 0.76,
                'title': 'User authentication bug',
                'assigned_to': 'John Doe <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Air4 Channels Testing\\AI Testing - Defect Management',
                'iteration_path': 'Air4 Channels Testing\\AI Testing - Defect Management\\Sprint 1',
                'created_date': '2025-09-25T09:45:00Z',
                'priority': 2
            }
        ]
        
        print(f"\n📊 Similar Historical Items:")
        expected_assignees = set()
        for item in similar_items:
            assignee = item['assigned_to']
            name = assignee.split('<')[0].strip() if '<' in assignee else assignee
            expected_assignees.add(name)
            print(f"   • #{item['id']}: {assignee}")
            print(f"     Title: {item['title']}")
            print(f"     Score: {item['score']:.2f}")
        
        print(f"\n✅ Expected Real Assignees: {expected_assignees}")
        
        # Test the complete triage recommendations
        print(f"\n🤖 Getting Complete Triage Recommendations...")
        
        recommendations = await triage_service.get_triage_recommendations(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=similar_items
        )
        
        print(f"✅ Triage recommendations generated")
        
        # Analyze assignee suggestions
        suggested_assignees = recommendations.get('suggested_assignees', [])
        print(f"\n👥 Assignee Suggestions:")
        print(f"   Count: {len(suggested_assignees)}")
        print(f"   Data Source: {recommendations.get('data_source', 'unknown')}")
        
        suggested_names = set()
        if suggested_assignees:
            for i, assignee in enumerate(suggested_assignees, 1):
                name = assignee['name']
                display_name = name.split('<')[0].strip() if '<' in name else name
                suggested_names.add(display_name)
                
                print(f"\n   {i}. {name}")
                print(f"      Email: {assignee['email']}")
                print(f"      Score: {assignee['score']:.3f}")
                print(f"      Historical Count: {assignee['historical_count']}")
                print(f"      Current Workload: {assignee['current_workload']}")
                print(f"      Rationale: {assignee['rationale']}")
                print(f"      Data Source: {assignee['data_source']}")
        else:
            print("   ❌ No assignee suggestions found")
        
        # Priority recommendation
        priority_rec = recommendations.get('priority_recommendation', {})
        print(f"\n🎯 Priority Recommendation:")
        print(f"   Priority: {priority_rec.get('priority', 'N/A')}")
        print(f"   Confidence: {priority_rec.get('confidence', 0.0):.3f}")
        print(f"   Rationale: {priority_rec.get('rationale', 'N/A')}")
        
        # Auto-assignment
        auto_assign = recommendations.get('auto_assign_recommended', False)
        print(f"\n🔄 Auto-Assignment:")
        print(f"   Recommended: {auto_assign}")
        print(f"   Threshold: {recommendations.get('auto_assign_threshold', 'N/A')}")
        
        # CRITICAL VERIFICATION: No Hallucination Check
        print(f"\n🔍 CRITICAL VERIFICATION - No Hallucination Check:")
        
        # Check data source
        data_source_ok = recommendations.get('data_source') == 'real_ado_history'
        print(f"   Data Source: {'✅ REAL ADO HISTORY' if data_source_ok else '❌ UNKNOWN SOURCE'}")
        
        # Check for hallucinated assignees
        hallucinated = suggested_names - expected_assignees
        hallucination_ok = len(hallucinated) == 0
        
        if hallucinated:
            print(f"   ❌ HALLUCINATION DETECTED: {hallucinated}")
            print(f"   ⚠️ These names were NOT in the historical data!")
            print(f"   Expected: {expected_assignees}")
            print(f"   Suggested: {suggested_names}")
        else:
            print(f"   ✅ NO HALLUCINATION: All suggestions from real data")
            print(f"   Expected: {expected_assignees}")
            print(f"   Suggested: {suggested_names}")
        
        # Check assignee data source
        assignee_source_ok = all(
            assignee.get('data_source') == 'real_ado_history' 
            for assignee in suggested_assignees
        )
        print(f"   Assignee Data Source: {'✅ REAL' if assignee_source_ok else '❌ UNKNOWN'}")
        
        # Overall verification
        verification_passed = data_source_ok and hallucination_ok and assignee_source_ok
        
        print(f"\n📋 FINAL SUMMARY:")
        print(f"   • Work Item: {work_item_id}")
        print(f"   • Similar Items: {len(similar_items)}")
        print(f"   • Assignee Suggestions: {len(suggested_assignees)}")
        print(f"   • Data Source: {'✅ REAL' if data_source_ok else '❌ UNKNOWN'}")
        print(f"   • Hallucination Check: {'✅ PASSED' if hallucination_ok else '❌ FAILED'}")
        print(f"   • Overall Verification: {'✅ PASSED' if verification_passed else '❌ FAILED'}")
        
        if suggested_assignees and verification_passed:
            top_assignee = suggested_assignees[0]
            print(f"   • Top Recommendation: {top_assignee['name']} (Score: {top_assignee['score']:.3f})")
        
        return verification_passed
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def main():
    """Main test function."""
    
    print("🎯 FINAL VERIFICATION TEST - Work Item 748404")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🚫 NO HALLUCINATION: Only real historical assignees allowed")
    print(f"✅ REAL DATA ONLY: Assignees from iteration paths and comments")
    
    success = await test_workitem_748404_final()
    
    print(f"\n{'='*80}")
    print("🏁 FINAL VERIFICATION RESULT")
    print(f"{'='*80}")
    
    if success:
        print("🎉 ✅ VERIFICATION PASSED!")
        print("✅ Work item 748404 processed successfully")
        print("✅ Assignee suggestions use REAL data only")
        print("✅ No hallucination detected")
        print("✅ Data source confirmed as real ADO history")
        print("✅ System is SAFE FOR PRODUCTION USE")
        print("\n🚀 READY FOR DEPLOYMENT!")
    else:
        print("💥 ❌ VERIFICATION FAILED!")
        print("⚠️ CRITICAL ISSUES DETECTED:")
        print("   • Hallucinated assignees found")
        print("   • Data source not confirmed")
        print("   • System NOT safe for production")
        print("\n🚫 DO NOT DEPLOY - FIX REQUIRED!")
    
    return success


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
