#!/usr/bin/env python3
"""
Test script to verify the new triage service uses REAL assignee data from ADO.
NO HALLUCINATION - Only real historical assignees should be suggested.
"""

import os
import sys
import json
import asyncio
import logging
from datetime import datetime

# Add the functions directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

# Load environment from local.settings.json
local_settings_path = os.path.join(os.path.dirname(__file__), '..', 'functions', 'local.settings.json')
if os.path.exists(local_settings_path):
    with open(local_settings_path, 'r') as f:
        settings = json.load(f)
    for key, value in settings.get('Values', {}).items():
        os.environ[key] = str(value)

from __app__.common.utils.config import get_config
from __app__.common.services.triage import TriageService
from __app__.common.adapters.ado_client import AdoClient

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


async def test_real_assignee_suggestions():
    """Test that assignee suggestions come from REAL ADO data only."""
    
    print("🔍 Testing REAL Assignee Suggestions (NO HALLUCINATION)")
    print("=" * 70)
    
    try:
        config = get_config()
        triage_service = TriageService(config)
        
        # Test work item 748404
        work_item_id = 748404
        title = "Test - New defect"
        description = "Testing the assignee suggestion system with real data"
        work_item_type = "Bug"
        
        print(f"🎯 Testing Work Item: {work_item_id}")
        print(f"   Title: {title}")
        print(f"   Type: {work_item_type}")
        
        # Create mock similar items with REAL assignee data
        # These should represent actual work items from ADO with real assignees
        similar_items = [
            {
                'id': 748401,
                'score': 0.85,
                'title': 'Login authentication issue',
                'assigned_to': 'John Smith <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Air4 Channels Testing',
                'iteration_path': 'Sprint 1',
                'created_date': '2025-09-15T10:30:00Z'
            },
            {
                'id': 748402,
                'score': 0.78,
                'title': 'User authentication failure',
                'assigned_to': 'Jane Doe <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Closed',
                'area_path': 'Air4 Channels Testing',
                'iteration_path': 'Sprint 1',
                'created_date': '2025-09-20T14:15:00Z'
            },
            {
                'id': 748403,
                'score': 0.72,
                'title': 'Authentication service down',
                'assigned_to': 'John Smith <<EMAIL>>',
                'work_item_type': 'Bug',
                'state': 'Resolved',
                'area_path': 'Air4 Channels Testing',
                'iteration_path': 'Sprint 2',
                'created_date': '2025-09-25T09:45:00Z'
            }
        ]
        
        print(f"\n📊 Similar Items Found: {len(similar_items)}")
        for item in similar_items:
            print(f"   • #{item['id']}: {item['title'][:40]}... (Score: {item['score']:.2f})")
            print(f"     Assigned to: {item['assigned_to']}")
        
        # Test assignee suggestions
        print(f"\n🤖 Getting Assignee Suggestions...")
        suggestions = await triage_service.suggest_assignees(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=similar_items,
            k=3
        )
        
        print(f"\n✅ Assignee Suggestions (REAL DATA ONLY):")
        print(f"   Found {len(suggestions)} suggestions")
        
        if suggestions:
            for i, suggestion in enumerate(suggestions, 1):
                print(f"\n   {i}. {suggestion['name']}")
                print(f"      Email: {suggestion['email']}")
                print(f"      Score: {suggestion['score']:.3f}")
                print(f"      Historical Count: {suggestion['historical_count']}")
                print(f"      Current Workload: {suggestion['current_workload']}")
                print(f"      Rationale: {suggestion['rationale']}")
                print(f"      Data Source: {suggestion['data_source']}")
        else:
            print("   ❌ No suggestions found")
        
        # Verify no hallucination
        print(f"\n🔍 VERIFICATION - No Hallucination Check:")
        # Extract just the names from the expected assignees in similar items
        expected_assignees = set()
        for item in similar_items:
            assignee = item['assigned_to']
            # Extract name part before email
            if '<' in assignee:
                name = assignee.split('<')[0].strip()
                expected_assignees.add(name)
            else:
                expected_assignees.add(assignee)

        # Extract names from suggestions
        suggested_names = set()
        for s in suggestions:
            name = s['name']
            # Extract name part before email if present
            if '<' in name:
                name = name.split('<')[0].strip()
            suggested_names.add(name)

        print(f"   Expected assignees from historical data: {expected_assignees}")
        print(f"   Suggested assignees: {suggested_names}")

        hallucinated_names = suggested_names - expected_assignees
        if hallucinated_names:
            print(f"   ❌ HALLUCINATION DETECTED: {hallucinated_names}")
            print(f"   ⚠️ These names were NOT in the historical data!")
            return False
        else:
            print(f"   ✅ No hallucination detected")
            print(f"   ✅ All suggestions come from real historical data")
        
        # Test with empty similar items
        print(f"\n🧪 Testing with No Similar Items...")
        empty_suggestions = await triage_service.suggest_assignees(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=[],
            k=3
        )
        
        if empty_suggestions:
            print(f"   ❌ HALLUCINATION: Got {len(empty_suggestions)} suggestions with no historical data!")
            for suggestion in empty_suggestions:
                print(f"      • {suggestion['name']} (HALLUCINATED)")
            return False
        else:
            print(f"   ✅ Correctly returned no suggestions when no historical data available")
        
        # Test full triage recommendations
        print(f"\n🎯 Testing Full Triage Recommendations...")
        recommendations = await triage_service.get_triage_recommendations(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=similar_items
        )
        
        print(f"   Data Source: {recommendations.get('data_source', 'unknown')}")
        print(f"   Suggested Assignees: {len(recommendations.get('suggested_assignees', []))}")
        print(f"   Priority Recommendation: {recommendations.get('priority_recommendation', {}).get('priority', 'N/A')}")
        
        # Final verification
        if recommendations.get('data_source') == 'real_ado_history':
            print(f"   ✅ Confirmed using real ADO history")
        else:
            print(f"   ⚠️ Data source not confirmed as real ADO history")
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        logger.exception("Test failed")
        return False


async def test_comment_extraction():
    """Test extraction of assignees from work item comments."""
    
    print(f"\n📝 Testing Comment-Based Assignee Extraction")
    print("=" * 50)
    
    try:
        config = get_config()
        triage_service = TriageService(config)
        
        # Test with real work item IDs that might have comments
        test_work_item_ids = [748404, 752662]
        
        print(f"🔍 Testing comment extraction for work items: {test_work_item_ids}")
        
        comment_assignees = await triage_service._extract_assignees_from_comments(test_work_item_ids)
        
        print(f"✅ Comment extraction results:")
        if comment_assignees:
            for work_item_id, assignees in comment_assignees.items():
                print(f"   Work Item {work_item_id}: {len(assignees)} assignees found")
                for assignee in assignees:
                    print(f"      • {assignee}")
        else:
            print(f"   No assignees found in comments (this is normal if comments don't contain email addresses)")
        
        return True
        
    except Exception as e:
        print(f"❌ Comment extraction test failed: {e}")
        logger.exception("Comment extraction test failed")
        return False


async def main():
    """Main test function."""
    
    print("🧪 REAL Assignee Suggestion Test (NO HALLUCINATION)")
    print("=" * 80)
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🎯 Goal: Verify assignee suggestions come from REAL ADO data only")
    print(f"🚫 NO HALLUCINATION: No fake or made-up assignee names allowed")
    
    # Run tests
    tests = [
        ("Real Assignee Suggestions", test_real_assignee_suggestions),
        ("Comment Extraction", test_comment_extraction)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*80}")
        print(f"🧪 Running: {test_name}")
        print(f"{'='*80}")
        
        try:
            success = await test_func()
            results.append((test_name, success))
            
            if success:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")
                
        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*80}")
    print("🏁 Test Summary")
    print(f"{'='*80}")
    
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"   {status}: {test_name}")
    
    print(f"\n📊 Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Assignee suggestions use REAL data only.")
        print("\n✅ Key Verifications:")
        print("   • No hallucinated assignee names")
        print("   • Suggestions based on historical work items")
        print("   • Comment extraction working")
        print("   • Proper handling of empty data")
        print("   • Real ADO data source confirmed")
    else:
        print("⚠️ Some tests failed. Please review the output above.")
        print("🚫 CRITICAL: Ensure no hallucination is occurring!")
    
    return passed == total


if __name__ == "__main__":
    # Run the test
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
