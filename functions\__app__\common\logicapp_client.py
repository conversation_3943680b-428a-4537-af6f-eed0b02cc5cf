"""
Logic App Client for Teams Personal Chat Integration
Sends Adaptive Cards via Logic App and handles responses back to ADO.
"""

import json
import logging
import time
from typing import Dict, Any, Optional
import httpx
from datetime import datetime, timezone

from .utils.config import get_config
from .utils.logging import log_structured

logger = logging.getLogger(__name__)


class LogicAppError(Exception):
    """Exception raised for Logic App communication errors."""
    pass


class LogicAppClient:
    """Client for communicating with the Teams Logic App."""
    
    def __init__(self, config=None):
        """Initialize the Logic App client."""
        self.config = config or get_config()
        self.logicapp_url = self.config.LOGICAPP_URL
        
        if not self.logicapp_url:
            raise ValueError("LOGICAPP_URL not configured")
    
    async def send_personal_card(
        self,
        to_email: str,
        subject: str,
        body: str,
        work_item_id: int,
        adaptive_card: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Send a Teams personal chat message via Logic App.
        
        Args:
            to_email: Recipient email address
            subject: Message subject line
            body: Message body text
            work_item_id: ADO work item ID
            adaptive_card: Optional Adaptive Card JSON payload
            
        Returns:
            Dict with status and trackingId (202) or final result (200)
            
        Raises:
            LogicAppError: If the request fails
        """
        start_time = time.time()
        
        # Prepare the payload
        payload = {
            "To": to_email,
            "Subject": subject,
            "Body": body,
            "work_item_id": work_item_id
        }
        
        if adaptive_card:
            payload["adaptive_card"] = adaptive_card
        
        # Log the request (redact PII)
        log_structured(
            logger,
            "info",
            "Sending Logic App request",
            extra={
                "work_item_id": work_item_id,
                "to_email_domain": to_email.split('@')[-1] if '@' in to_email else "unknown",
                "has_adaptive_card": adaptive_card is not None,
                "payload_size": len(json.dumps(payload))
            }
        )
        
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    self.logicapp_url,
                    json=payload,
                    headers={
                        "Content-Type": "application/json",
                        "User-Agent": "AutoDefectTriage/1.0"
                    }
                )
                
                duration = time.time() - start_time
                
                # Handle different response codes
                if response.status_code == 202:
                    # Async processing - Logic App accepted the request
                    result = {
                        "status": "accepted",
                        "trackingId": response.headers.get("x-ms-workflow-run-id", "unknown")
                    }
                    
                    # Log success
                    log_structured(
                        logger,
                        "info",
                        "Logic App request accepted",
                        extra={
                            "work_item_id": work_item_id,
                            "tracking_id": result["trackingId"],
                            "duration_ms": round(duration * 1000, 2),
                            "status_code": response.status_code
                        }
                    )
                    
                    # Custom App Insights event
                    self._log_app_insights_event(
                        "logicapp_request_sent",
                        {
                            "wi_id": work_item_id,
                            "to": to_email.split('@')[-1],  # Domain only for privacy
                            "trackingId": result["trackingId"],
                            "duration_ms": round(duration * 1000, 2)
                        }
                    )
                    
                    return result
                    
                elif response.status_code == 200:
                    # Sync processing - Logic App completed immediately
                    try:
                        result = response.json()
                        result["status"] = "completed"
                        
                        log_structured(
                            logger,
                            "info",
                            "Logic App request completed synchronously",
                            extra={
                                "work_item_id": work_item_id,
                                "duration_ms": round(duration * 1000, 2),
                                "status_code": response.status_code
                            }
                        )
                        
                        return result
                        
                    except json.JSONDecodeError:
                        # Fallback for non-JSON 200 response
                        return {
                            "status": "completed",
                            "message": "Request completed successfully",
                            "response_text": response.text[:500]  # Truncate for safety
                        }
                
                else:
                    # Error response
                    error_msg = f"Logic App request failed with status {response.status_code}"
                    
                    try:
                        error_detail = response.json()
                        error_msg += f": {error_detail.get('message', 'Unknown error')}"
                    except json.JSONDecodeError:
                        error_msg += f": {response.text[:200]}"
                    
                    # Log error
                    log_structured(
                        logger,
                        "error",
                        error_msg,
                        extra={
                            "work_item_id": work_item_id,
                            "status_code": response.status_code,
                            "duration_ms": round(duration * 1000, 2)
                        }
                    )
                    
                    # Custom App Insights event
                    self._log_app_insights_event(
                        "logicapp_request_failed",
                        {
                            "status": response.status_code,
                            "error": error_msg,
                            "wi_id": work_item_id,
                            "duration_ms": round(duration * 1000, 2)
                        }
                    )
                    
                    raise LogicAppError(error_msg)
                    
        except httpx.TimeoutException:
            duration = time.time() - start_time
            error_msg = "Logic App request timed out"
            
            log_structured(
                logger,
                "error",
                error_msg,
                extra={
                    "work_item_id": work_item_id,
                    "duration_ms": round(duration * 1000, 2)
                }
            )
            
            self._log_app_insights_event(
                "logicapp_request_failed",
                {
                    "status": "timeout",
                    "error": error_msg,
                    "wi_id": work_item_id,
                    "duration_ms": round(duration * 1000, 2)
                }
            )
            
            raise LogicAppError(error_msg)
            
        except httpx.RequestError as e:
            duration = time.time() - start_time
            error_msg = f"Logic App request failed: {str(e)}"
            
            log_structured(
                logger,
                "error",
                error_msg,
                extra={
                    "work_item_id": work_item_id,
                    "duration_ms": round(duration * 1000, 2),
                    "error_type": type(e).__name__
                }
            )
            
            self._log_app_insights_event(
                "logicapp_request_failed",
                {
                    "status": "network_error",
                    "error": error_msg,
                    "wi_id": work_item_id,
                    "duration_ms": round(duration * 1000, 2)
                }
            )
            
            raise LogicAppError(error_msg)
    
    def _log_app_insights_event(self, event_name: str, properties: Dict[str, Any]):
        """Log custom event to App Insights."""
        try:
            # Add timestamp and common properties
            properties.update({
                "timestamp": datetime.now(timezone.utc).isoformat(),
                "component": "logicapp_client",
                "version": "1.0"
            })
            
            # Log as structured event (App Insights integration would be added here)
            log_structured(
                logger,
                "info",
                f"App Insights Event: {event_name}",
                extra={
                    "event_name": event_name,
                    "properties": properties
                }
            )
            
        except Exception as e:
            logger.warning(f"Failed to log App Insights event: {e}")


# Convenience function for direct usage
async def send_personal_card(
    to_email: str,
    subject: str,
    body: str,
    work_item_id: int,
    adaptive_card: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Send a Teams personal chat message via Logic App.
    
    This is a convenience function that creates a LogicAppClient instance
    and calls send_personal_card().
    
    Args:
        to_email: Recipient email address
        subject: Message subject line
        body: Message body text
        work_item_id: ADO work item ID
        adaptive_card: Optional Adaptive Card JSON payload
        
    Returns:
        Dict with status and trackingId (202) or final result (200)
        
    Raises:
        LogicAppError: If the request fails
    """
    client = LogicAppClient()
    return await client.send_personal_card(
        to_email=to_email,
        subject=subject,
        body=body,
        work_item_id=work_item_id,
        adaptive_card=adaptive_card
    )
