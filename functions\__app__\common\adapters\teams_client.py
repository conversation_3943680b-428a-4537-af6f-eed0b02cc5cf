"""
Microsoft Teams Client
Handles Teams notifications and Adaptive Cards for triage results.
"""

import json
import logging
from typing import Dict, List, Any, Optional
import httpx
from datetime import datetime

from ..models.schemas import WorkItem, TriageResult
from ..utils.config import Config
from ..utils.logging import log_structured
from ..cards.teams_adaptive import build_triage_card, build_duplicate_alert_card, build_enhanced_feedback_card

logger = logging.getLogger(__name__)


class TeamsClient:
    """Client for Microsoft Teams webhook and Graph API operations."""
    
    def __init__(self, config: Config):
        self.config = config
        self.webhook_url = config.get('TEAMS_WEBHOOK_URL')
        self.graph_token = config.get('TEAMS_GRAPH_TOKEN')
        self.teams_logic_app_url = config.get('TEAMS_LOGIC_APP_URL')
        self.email_logic_app_url = config.get('Email_LOGIC_APP_URL')

        # Setup HTTP client
        self.client = httpx.AsyncClient(
            headers={
                "Content-Type": "application/json",
                "User-Agent": "QA-AI-Triage/1.0"
            },
            timeout=30.0
        )

        # Initialize card builder lazily to avoid circular imports
        self._card_builder = None

    @property
    def card_builder(self):
        """Get the card builder, initializing it lazily to avoid circular imports."""
        if self._card_builder is None:
            from ..notifications.teams_cards import TeamsCardBuilder
            self._card_builder = TeamsCardBuilder()
        return self._card_builder

    def _clean_text_for_json(self, text: str) -> str:
        """Clean text to prevent JSON parsing issues."""
        if not text:
            return ""

        # Replace problematic characters
        cleaned = text.replace("\\", "/")  # Replace backslashes
        cleaned = cleaned.replace('"', "'")  # Replace double quotes with single quotes
        cleaned = cleaned.replace("\n", " ")  # Replace newlines with spaces
        cleaned = cleaned.replace("\r", " ")  # Replace carriage returns with spaces
        cleaned = cleaned.replace("\t", " ")  # Replace tabs with spaces

        # Remove control characters
        cleaned = ''.join(char for char in cleaned if ord(char) >= 32 or char in ['\n', '\r', '\t'])

        # Limit length to prevent overly long strings
        if len(cleaned) > 500:
            cleaned = cleaned[:500] + "..."

        return cleaned.strip()

    def _create_simple_teams_message(self, work_item: WorkItem, triage_result: TriageResult) -> str:
        """Create a simple text message for Teams to avoid JSON parsing issues."""

        # Clean text values
        title = self._clean_text_for_json(work_item.title or "")
        description = self._clean_text_for_json(work_item.description or "No description provided")
        if len(description) > 200:
            description = description[:200] + "..."

        # Create confidence indicator
        confidence_text = f"{int(triage_result.confidence_score * 100)}%" if triage_result.confidence_score else "N/A"

        # Build simple text message
        message = f"""🚨 **Work Item Assignment Notification**

**{work_item.work_item_type} {work_item.id}: {title}**

📋 **Details:**
• Type: {work_item.work_item_type or 'Bug'}
• Priority: P{work_item.priority or 2}
• State: {work_item.state or 'New'}
• Assigned To: {triage_result.assigned_to or 'Unassigned'}
• Area Path: {work_item.area_path or 'N/A'}
• AI Confidence: {confidence_text}

🤖 **AI Triage Results:**
• Reasoning: {self._clean_text_for_json(triage_result.reasoning or 'Assigned based on historical patterns')}
• Duplicates Found: {len(triage_result.duplicates) if triage_result.duplicates else 0}
• Processing Time: {triage_result.processing_time_ms or 0}ms

📝 **Description:**
{description}

🔗 **Actions:**
• View in Azure DevOps: https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item.id}
• Accept Assignment
• Request More Information

---
*Automated notification from AutoDefectTriage System*"""

        return message

    async def send_triage_notification(
        self,
        work_item: WorkItem,
        triage_result: TriageResult
    ) -> bool:
        """
        Send a triage notification to Teams channel or email if Teams is not available.

        ⚠️ PRODUCTION SAFETY: Only sends notifications for Bug 748404 during development.

        Args:
            work_item: The work item that was triaged
            triage_result: The triage results

        Returns:
            True if notification was sent successfully
        """
        # PRODUCTION SAFETY: Only send notifications for Bug 748404
        if work_item.id != 748404:
            log_structured(
                logger,
                "warning",
                f"PRODUCTION SAFETY: Teams client ignoring work item {work_item.id} - only Bug 748404 notifications are sent",
                extra={"work_item_id": work_item.id, "allowed_id": 748404}
            )
            return True  # Return True to avoid breaking the flow

        try:
            # Try Teams Logic App first if configured
            if self.teams_logic_app_url:
                return await self._send_teams_logic_app_notification(work_item, triage_result)

            # Try Teams webhook if Logic App not available
            elif self.webhook_url:
                # Build enhanced adaptive card for the notification with feedback collection
                card = build_enhanced_feedback_card(work_item, triage_result)

                # Send to Teams webhook
                response = await self.client.post(
                    self.webhook_url,
                    json=card
                )
                response.raise_for_status()

                log_structured(
                    logger,
                    "info",
                    "Sent Teams triage notification",
                    extra={
                        "work_item_id": work_item.id,
                        "assigned_to": triage_result.assigned_to,
                        "webhook_response_status": response.status_code
                    }
                )

                return True

            # Fallback to email notification if Teams not configured
            elif self.email_logic_app_url:
                return await self._send_email_notification(work_item, triage_result)

            else:
                logger.warning("No notification channels configured (Teams Logic App, Teams webhook, or Email Logic App), skipping notification")
                return False

        except httpx.HTTPStatusError as e:
            logger.error(f"HTTP error sending Teams notification: {e}")
            # Try email fallback if Teams fails
            if self.email_logic_app_url:
                logger.info("Attempting email fallback after Teams failure")
                return await self._send_email_notification(work_item, triage_result)
            return False
        except Exception as e:
            logger.error(f"Error sending Teams notification: {e}")
            # Try email fallback if Teams fails
            if self.email_logic_app_url:
                logger.info("Attempting email fallback after Teams failure")
                return await self._send_email_notification(work_item, triage_result)
            return False

    async def _send_teams_logic_app_notification(
        self,
        work_item: WorkItem,
        triage_result: TriageResult
    ) -> bool:
        """
        Send Teams notification via Logic App with adaptive card for response collection.

        Args:
            work_item: The work item that was triaged
            triage_result: The triage results

        Returns:
            True if Teams notification was sent successfully
        """
        try:
            # Generate unique notification ID
            import uuid
            notification_id = f"teams-{work_item.id}-{uuid.uuid4().hex[:8]}"

            # Build enhanced adaptive card for the notification with feedback collection
            from ..cards.teams_adaptive import build_enhanced_feedback_card
            adaptive_card = build_enhanced_feedback_card(work_item, triage_result, notification_id)

            # Prepare Teams Logic App payload with adaptive card
            teams_payload = {
                "To": "<EMAIL>",  # Send to specific user
                "Subject": f"🚨 Work Item {work_item.id} - {work_item.title}",
                "Body": f"AI Triage notification for work item {work_item.id}",
                "work_item_id": work_item.id,
                "notification_id": notification_id,
                "adaptive_card": adaptive_card,
                "Attachments": "",
                "attachmentName": ""
            }

            # Send to Teams Logic App
            response = await self.client.post(
                self.teams_logic_app_url,
                json=teams_payload
            )
            response.raise_for_status()

            log_structured(
                logger,
                "info",
                "Sent Teams adaptive card notification via Logic App",
                extra={
                    "work_item_id": work_item.id,
                    "assigned_to": triage_result.assigned_to,
                    "logic_app_response_status": response.status_code,
                    "has_adaptive_card": True
                }
            )

            return True

        except httpx.HTTPStatusError as e:
            log_structured(
                logger,
                "error",
                f"HTTP error sending Teams Logic App notification: {e}",
                extra={
                    "work_item_id": work_item.id,
                    "status_code": e.response.status_code if e.response else None
                }
            )
            # Try email fallback if Teams Logic App fails
            if self.email_logic_app_url:
                logger.info("Attempting email fallback after Teams Logic App failure")
                return await self._send_email_notification(work_item, triage_result)
            return False
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending Teams Logic App notification: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            # Try email fallback if Teams Logic App fails
            if self.email_logic_app_url:
                logger.info("Attempting email fallback after Teams Logic App failure")
                return await self._send_email_notification(work_item, triage_result)
            return False

    async def _send_email_notification(
        self,
        work_item: WorkItem,
        triage_result: TriageResult
    ) -> bool:
        """
        Send email notification via Logic App.

        Args:
            work_item: The work item that was triaged
            triage_result: The triage results

        Returns:
            True if email was sent successfully
        """
        try:
            # Determine recipient email
            recipient_email = self._get_recipient_email(triage_result.assigned_to)

            # Create email subject
            priority_text = f"P{work_item.priority}" if work_item.priority else "P2"
            subject = f"🔔 {work_item.work_item_type} {work_item.id} Assigned - {priority_text} - {work_item.title[:50]}"

            # Create email body
            body = self._create_email_body(work_item, triage_result)

            # Create defect information attachment
            attachment_content = self._create_defect_attachment(work_item, triage_result)
            attachment_name = f"DefectAnalysis_{work_item.id}_{work_item.work_item_type}.txt"

            # Prepare Email Logic App payload with attachment
            # Based on Email Logic App conditional logic: if attachmentName is empty AND Attachments is true, sends simple email
            # Otherwise sends email with Virgin Atlantic branded template and attachment support
            email_payload = {
                "Body": body,
                "Subject": subject,
                "To": recipient_email,
                "Attachments": True,  # Enable attachments
                "attachmentName": attachment_name,  # Provide attachment name for branded template
                "ContentBytes": attachment_content,  # Base64 encoded content
                "Name": attachment_name  # Add Name field as expected by Logic App
            }

            # Send to Logic App
            response = await self.client.post(
                self.email_logic_app_url,
                json=email_payload
            )
            response.raise_for_status()

            log_structured(
                logger,
                "info",
                "Sent email notification via Logic App",
                extra={
                    "work_item_id": work_item.id,
                    "assigned_to": triage_result.assigned_to,
                    "recipient_email": recipient_email,
                    "logic_app_response_status": response.status_code
                }
            )

            return True

        except httpx.HTTPStatusError as e:
            log_structured(
                logger,
                "error",
                f"HTTP error sending email notification with attachment: {e}",
                extra={
                    "work_item_id": work_item.id,
                    "status_code": e.response.status_code if e.response else None
                }
            )
            # Try fallback to simple email
            logger.info("Attempting simple email fallback after attachment email failure")
            return await self._send_simple_email_notification(work_item, triage_result)
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending email notification with attachment: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            # Try fallback to simple email
            logger.info("Attempting simple email fallback after attachment email failure")
            return await self._send_simple_email_notification(work_item, triage_result)

    async def _send_simple_email_notification(self, work_item: WorkItem, triage_result: TriageResult) -> bool:
        """
        Send simple email notification without attachments.
        Uses the Email Logic App conditional logic: if attachmentName is empty AND Attachments is true, sends simple email.

        Args:
            work_item: The work item
            triage_result: The triage results

        Returns:
            True if email was sent successfully
        """
        try:
            # Determine recipient email
            recipient_email = self._get_recipient_email(triage_result.assigned_to)

            # Create email subject
            priority_text = f"P{work_item.priority}" if work_item.priority else "P2"
            subject = f"🔔 {work_item.work_item_type} {work_item.id} Assigned - {priority_text} - {work_item.title[:50]}"

            # Create simple email body (plain text)
            body = f"""Work Item {work_item.id} has been assigned to {triage_result.assigned_to}.

Title: {work_item.title}
Priority: {priority_text}
State: {work_item.state}
Assigned To: {triage_result.assigned_to}

Please review this work item in Azure DevOps.

View in ADO: https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item.id}
"""

            # Prepare Email Logic App payload for simple email
            # Based on conditional logic: if attachmentName is empty AND Attachments is true, sends simple email
            email_payload = {
                "Body": body,
                "Subject": subject,
                "To": recipient_email,
                "Attachments": True,  # Enable attachments flag
                "attachmentName": "",  # Empty attachment name triggers simple email flow
                "ContentBytes": ""  # Empty content bytes
            }

            # Send to Logic App
            response = await self.client.post(
                self.email_logic_app_url,
                json=email_payload
            )
            response.raise_for_status()

            log_structured(
                logger,
                "info",
                "Sent simple email notification via Logic App",
                extra={
                    "work_item_id": work_item.id,
                    "assigned_to": triage_result.assigned_to,
                    "recipient_email": recipient_email,
                    "logic_app_response_status": response.status_code,
                    "email_type": "simple"
                }
            )

            return True

        except httpx.HTTPStatusError as e:
            log_structured(
                logger,
                "error",
                f"HTTP error sending simple email notification: {e}",
                extra={
                    "work_item_id": work_item.id,
                    "status_code": e.response.status_code if e.response else None
                }
            )
            return False
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending simple email notification: {e}",
                extra={"work_item_id": work_item.id},
                exc_info=True
            )
            return False

    def _get_recipient_email(self, assigned_to: str) -> str:
        """
        Get recipient email address from assigned_to field.

        Args:
            assigned_to: Assigned person name or email

        Returns:
            Email address
        """
        # If assigned_to looks like an email, use it directly
        if assigned_to and "@" in assigned_to:
            return assigned_to

        # Map common names to emails (you can extend this mapping)
        name_to_email = {
            "qa-team": "<EMAIL>",
            "dev-team": "<EMAIL>",
            "security-team": "<EMAIL>",
            "unassigned": "<EMAIL>"
        }

        # Try to find email mapping
        if assigned_to and assigned_to.lower() in name_to_email:
            return name_to_email[assigned_to.lower()]

        # Default fallback email
        return "<EMAIL>"

    def _create_email_body(self, work_item: WorkItem, triage_result: TriageResult) -> str:
        """
        Create professional email body for work item notification with assignee suggestions.

        Args:
            work_item: The work item
            triage_result: The triage results

        Returns:
            HTML email body
        """
        # Get Azure DevOps URL
        ado_url = f"https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item.id}"

        # Determine priority color
        priority_color = "#dc3545" if work_item.priority and work_item.priority <= 2 else "#ffc107"

        # Create confidence indicator
        confidence_text = f"{int(triage_result.confidence_score * 100)}%" if triage_result.confidence_score else "N/A"

        # Generate assignee suggestions section
        assignee_suggestions_html = self._create_assignee_suggestions_html(work_item, triage_result)

        # Build HTML email body
        body = f"""
        <html>
        <body style="font-family: 'Segoe UI', Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">

                <!-- Header -->
                <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 20px; border-radius: 8px 8px 0 0;">
                    <h2 style="margin: 0; font-size: 24px;">🔔 Work Item Assignment</h2>
                    <p style="margin: 10px 0 0 0; opacity: 0.9;">Auto Defect Triage System</p>
                </div>

                <!-- Work Item Details -->
                <div style="background: #f8f9fa; padding: 20px; border-left: 1px solid #e9ecef; border-right: 1px solid #e9ecef;">
                    <div style="background: white; padding: 20px; border-radius: 6px; border-left: 4px solid {priority_color};">

                        <h3 style="margin-top: 0; color: #495057;">
                            <a href="{ado_url}" style="color: #007bff; text-decoration: none;" target="_blank">
                                {work_item.work_item_type} {work_item.id}: {work_item.title}
                            </a>
                        </h3>

                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 15px 0; font-size: 14px;">
                            <div><strong>Type:</strong> {work_item.work_item_type}</div>
                            <div><strong>Priority:</strong> P{work_item.priority or 2}</div>
                            <div><strong>State:</strong> {work_item.state}</div>
                            <div><strong>Assigned To:</strong> {triage_result.assigned_to}</div>
                            <div><strong>Area Path:</strong> {work_item.area_path or 'N/A'}</div>
                            <div><strong>Confidence:</strong> {confidence_text}</div>
                        </div>

                        {f'<div style="margin: 15px 0;"><strong>Description:</strong><br/>{work_item.description[:200]}{"..." if len(work_item.description or "") > 200 else ""}</div>' if work_item.description else ''}

                        <!-- AI Insights -->
                        <div style="background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 15px 0;">
                            <h4 style="margin-top: 0; color: #1976d2;">🤖 AI Triage Results</h4>
                            <ul style="margin: 10px 0; padding-left: 20px;">
                                <li><strong>Assignment Reasoning:</strong> {triage_result.reasoning or 'Assigned based on historical patterns and area path'}</li>
                                {f'<li><strong>Duplicates Found:</strong> {len(triage_result.duplicates)} potential duplicates detected</li>' if triage_result.duplicates else '<li><strong>Duplicates:</strong> No duplicates detected</li>'}
                                <li><strong>Processing Time:</strong> {triage_result.processing_time_ms or 0}ms</li>
                            </ul>
                        </div>

                        {assignee_suggestions_html}

                        <!-- Action Buttons -->
                        <div style="margin: 20px 0; text-align: center;">
                            <a href="{ado_url}" style="background: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 4px; display: inline-block; margin: 5px;">
                                📝 View in Azure DevOps
                            </a>
                        </div>

                    </div>
                </div>

                <!-- Footer -->
                <div style="background: #e9ecef; padding: 15px; text-align: center; font-size: 12px; color: #6c757d; border-radius: 0 0 8px 8px;">
                    <p style="margin: 0;">This notification was sent by the Auto Defect Triage System</p>
                    <p style="margin: 5px 0 0 0;">Generated on {datetime.utcnow().strftime('%B %d, %Y at %H:%M UTC')}</p>
                </div>

            </div>
        </body>
        </html>
        """

        return body.strip()

    def _create_assignee_suggestions_html(self, work_item: WorkItem, triage_result: TriageResult) -> str:
        """
        Create HTML section for assignee suggestions with real data for Bug-748404.

        Args:
            work_item: The work item
            triage_result: The triage results

        Returns:
            HTML string for assignee suggestions section
        """
        # Only show suggestions for Bug-748404
        if work_item.id != 748404:
            return ""

        # Extract iteration name if available
        iteration_name = ""
        if work_item.iteration_path:
            iteration_name = work_item.iteration_path.split('\\')[-1] if '\\' in work_item.iteration_path else work_item.iteration_path

        # Get real suggestions based on current assignment and work item context
        primary_assignee = triage_result.assigned_to or "Unassigned"
        primary_confidence = triage_result.confidence_score or 0.0
        primary_reasoning = triage_result.reasoning or "AI assignment based on historical patterns"

        suggestions = [
            {
                "name": primary_assignee,
                "confidence": primary_confidence,
                "reasoning": f"{primary_reasoning}; Iteration: {iteration_name}" if iteration_name else primary_reasoning
            }
        ]

        # Add alternative suggestions based on area path and work item type
        if work_item.area_path and "Air4 Channels Testing" in work_item.area_path:
            # Add team members who typically work on this area
            if primary_assignee != "qa-team":
                suggestions.append({
                    "name": "qa-team",
                    "confidence": 0.75,
                    "reasoning": f"Area path expertise: {work_item.area_path}; Bug testing specialist"
                })

            if primary_assignee != "dev-team":
                suggestions.append({
                    "name": "dev-team",
                    "confidence": 0.68,
                    "reasoning": f"Development team for {work_item.area_path}; Code ownership match"
                })

        # Limit to top 3 suggestions
        suggestions = suggestions[:3]

        if not suggestions:
            return ""

        # Build HTML for suggestions
        suggestions_html = """
                        <!-- Assignment Suggestions -->
                        <div style="background: #f3e5f5; padding: 15px; border-radius: 4px; margin: 15px 0;">
                            <h4 style="margin-top: 0; color: #7b1fa2;">👤 Assignment Suggestions</h4>
        """

        for i, suggestion in enumerate(suggestions[:3], 1):
            # Parse reasoning to highlight iteration context
            reasoning_parts = suggestion["reasoning"].split(';')
            iteration_parts = []
            other_parts = []

            for part in reasoning_parts:
                part = part.strip()
                if "Iteration history:" in part or "Bug experience:" in part or "Task experience:" in part:
                    iteration_parts.append(part)
                else:
                    other_parts.append(part)

            suggestions_html += f"""
                            <div style="margin: 10px 0; padding: 10px; background: white; border-radius: 4px; border-left: 3px solid #7b1fa2;">
                                <div style="font-weight: bold; color: #333;">
                                    {i}. {suggestion['name']} ({suggestion['confidence']:.1%} confidence)
                                </div>
            """

            # Add iteration context if available
            if iteration_parts:
                suggestions_html += f"""
                                <div style="margin: 5px 0; color: #7b1fa2; font-size: 14px;">
                                    🔄 <strong>Iteration Experience:</strong> {'; '.join(iteration_parts)}
                                </div>
                """

            # Add other reasoning
            if other_parts:
                for part in other_parts[:2]:  # Limit to 2 additional reasons
                    suggestions_html += f"""
                                <div style="margin: 3px 0; color: #666; font-size: 13px;">
                                    • {part}
                                </div>
                    """

            suggestions_html += "                            </div>"

        suggestions_html += """
                        </div>
        """

        return suggestions_html

    def _create_teams_adaptive_card(self, work_item: WorkItem, triage_result: TriageResult) -> dict:
        """
        Create Teams Adaptive Card for work item notification.

        Args:
            work_item: The work item
            triage_result: The triage results

        Returns:
            Adaptive Card JSON structure
        """
        # Get Azure DevOps URL
        ado_url = f"https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item.id}"

        # Determine priority color
        priority_color = "attention" if work_item.priority and work_item.priority <= 2 else "warning"

        # Create confidence indicator
        confidence_text = f"{int(triage_result.confidence_score * 100)}%" if triage_result.confidence_score else "N/A"

        # Clean description to avoid JSON escape issues
        description = work_item.description or "No description provided"
        if len(description) > 200:
            description = description[:200] + "..."
        # Remove problematic characters that could cause JSON escape issues
        description = self._clean_text_for_json(description)

        # Clean title and other text fields
        title = self._clean_text_for_json(work_item.title or "")
        reasoning = self._clean_text_for_json(triage_result.reasoning or "Assigned based on historical patterns and area path")

        # Create safe text values
        work_item_header = f"{work_item.work_item_type} {work_item.id}: {title}"
        work_item_header = self._clean_text_for_json(work_item_header)

        # Create safe values for all dynamic content
        priority_value = f"P{work_item.priority or 2}"
        reasoning_text = f"**Reasoning:** {reasoning}"
        duplicates_text = f"**Duplicates Found:** {len(triage_result.duplicates) if triage_result.duplicates else 0}"
        processing_time_text = f"**Processing Time:** {triage_result.processing_time_ms or 0}ms"
        description_text = f"**Description:** {description}"

        # Build Adaptive Card
        adaptive_card = {
            "type": "AdaptiveCard",
            "version": "1.4",
            "body": [
                {
                    "type": "Container",
                    "style": "emphasis",
                    "items": [
                        {
                            "type": "TextBlock",
                            "text": "Work Item Assignment Notification",
                            "weight": "bolder",
                            "size": "medium",
                            "color": "accent"
                        }
                    ]
                },
                {
                    "type": "Container",
                    "items": [
                        {
                            "type": "TextBlock",
                            "text": work_item_header,
                            "weight": "bolder",
                            "size": "large",
                            "color": priority_color,
                            "wrap": True
                        }
                    ]
                },
                {
                    "type": "FactSet",
                    "facts": [
                        {"title": "Type", "value": work_item.work_item_type or "Bug"},
                        {"title": "Priority", "value": priority_value},
                        {"title": "State", "value": work_item.state or "New"},
                        {"title": "Assigned To", "value": triage_result.assigned_to or "Unassigned"},
                        {"title": "Area Path", "value": work_item.area_path or "N/A"},
                        {"title": "AI Confidence", "value": confidence_text}
                    ]
                },
                {
                    "type": "Container",
                    "style": "emphasis",
                    "items": [
                        {
                            "type": "TextBlock",
                            "text": "AI Triage Results",
                            "weight": "bolder",
                            "color": "accent"
                        },
                        {
                            "type": "TextBlock",
                            "text": reasoning_text,
                            "wrap": True
                        },
                        {
                            "type": "TextBlock",
                            "text": duplicates_text,
                            "wrap": True
                        },
                        {
                            "type": "TextBlock",
                            "text": processing_time_text,
                            "wrap": True
                        }
                    ]
                },
                {
                    "type": "Container",
                    "items": [
                        {
                            "type": "TextBlock",
                            "text": description_text,
                            "wrap": True
                        }
                    ]
                }
            ],
            "actions": [
                {
                    "type": "Action.OpenUrl",
                    "title": "View in Azure DevOps",
                    "url": ado_url
                }
            ]
        }

        return adaptive_card
    
    async def send_duplicate_alert(
        self, 
        work_item: WorkItem, 
        duplicate_items: List[Dict[str, Any]]
    ) -> bool:
        """
        Send a duplicate detection alert to Teams.
        
        Args:
            work_item: The new work item
            duplicate_items: List of potential duplicate work items
        
        Returns:
            True if alert was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping duplicate alert")
                return False
            
            # Build adaptive card for duplicate alert
            card = build_duplicate_alert_card(work_item, duplicate_items)
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams duplicate alert",
                extra={
                    "work_item_id": work_item.id,
                    "duplicate_count": len(duplicate_items),
                    "webhook_response_status": response.status_code
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams duplicate alert: {e}")
            return False
    
    async def send_custom_message(
        self, 
        title: str, 
        message: str, 
        color: str = "good"
    ) -> bool:
        """
        Send a custom message to Teams channel.
        
        Args:
            title: Message title
            message: Message content
            color: Message color (good, warning, attention)
        
        Returns:
            True if message was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping custom message")
                return False
            
            # Build simple message card
            card = {
                "type": "message",
                "attachments": [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content": {
                            "type": "AdaptiveCard",
                            "version": "1.4",
                            "body": [
                                {
                                    "type": "TextBlock",
                                    "text": title,
                                    "weight": "Bolder",
                                    "size": "Medium",
                                    "color": self._get_color_for_theme(color)
                                },
                                {
                                    "type": "TextBlock",
                                    "text": message,
                                    "wrap": True
                                }
                            ]
                        }
                    }
                ]
            }
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams custom message",
                extra={
                    "title": title,
                    "message_length": len(message),
                    "color": color
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams custom message: {e}")
            return False
    
    async def send_daily_summary(self, summary_data: Dict[str, Any]) -> bool:
        """
        Send a daily summary of triage activities to Teams.
        
        Args:
            summary_data: Dictionary containing summary statistics
        
        Returns:
            True if summary was sent successfully
        """
        try:
            if not self.webhook_url:
                logger.warning("Teams webhook URL not configured, skipping daily summary")
                return False
            
            # Build summary card
            card = {
                "type": "message",
                "attachments": [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content": {
                            "type": "AdaptiveCard",
                            "version": "1.4",
                            "body": [
                                {
                                    "type": "TextBlock",
                                    "text": "🤖 Daily AI Triage Summary",
                                    "weight": "Bolder",
                                    "size": "Large",
                                    "color": "Accent"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": f"**Date:** {datetime.now().strftime('%Y-%m-%d')}",
                                    "wrap": True
                                },
                                {
                                    "type": "FactSet",
                                    "facts": [
                                        {
                                            "title": "Work Items Processed",
                                            "value": str(summary_data.get('processed_count', 0))
                                        },
                                        {
                                            "title": "Auto-Assigned",
                                            "value": str(summary_data.get('assigned_count', 0))
                                        },
                                        {
                                            "title": "Duplicates Found",
                                            "value": str(summary_data.get('duplicate_count', 0))
                                        },
                                        {
                                            "title": "High Priority Items",
                                            "value": str(summary_data.get('high_priority_count', 0))
                                        },
                                        {
                                            "title": "Average Confidence",
                                            "value": f"{summary_data.get('avg_confidence', 0):.1%}"
                                        }
                                    ]
                                }
                            ]
                        }
                    }
                ]
            }
            
            # Send to Teams webhook
            response = await self.client.post(
                self.webhook_url,
                json=card
            )
            response.raise_for_status()
            
            log_structured(
                logger,
                "info",
                "Sent Teams daily summary",
                extra={
                    "processed_count": summary_data.get('processed_count', 0),
                    "assigned_count": summary_data.get('assigned_count', 0)
                }
            )
            
            return True
            
        except Exception as e:
            logger.error(f"Error sending Teams daily summary: {e}")
            return False
    
    def _get_color_for_theme(self, color: str) -> str:
        """
        Map color names to Teams theme colors.
        
        Args:
            color: Color name (good, warning, attention)
        
        Returns:
            Teams color value
        """
        color_map = {
            "good": "Good",
            "warning": "Warning", 
            "attention": "Attention",
            "accent": "Accent",
            "default": "Default"
        }
        return color_map.get(color.lower(), "Default")
    
    async def send_notification_card(
        self,
        context: "NotificationContext",
        route: "StakeholderRoute",
        webhook_url: Optional[str] = None
    ) -> bool:
        """
        Send a rich notification card to Teams.

        Args:
            context: Complete notification context
            route: Stakeholder route information
            webhook_url: Override webhook URL (optional)

        Returns:
            True if notification was sent successfully
        """
        # Lazy import to avoid circular dependencies
        from ..models import NotificationContext, StakeholderRoute, DeliveryMethod

        try:
            url = webhook_url or self.webhook_url
            if not url:
                logger.warning("Teams webhook URL not configured, skipping notification")
                return False

            # Build adaptive card based on notification context
            card = self.card_builder.build_notification_card(context)

            # Send to Teams webhook
            response = await self.client.post(url, json=card)
            response.raise_for_status()

            log_structured(
                logger,
                "info",
                "Sent Teams notification card",
                extra={
                    "work_item_id": context.work_item.id,
                    "trigger_type": context.trigger.trigger_type,
                    "recipient_id": route.recipient_id,
                    "webhook_response_status": response.status_code
                }
            )

            return True

        except httpx.HTTPStatusError as e:
            log_structured(
                logger,
                "error",
                f"HTTP error sending Teams notification card: {e}",
                extra={
                    "work_item_id": context.work_item.id,
                    "status_code": e.response.status_code,
                    "response_text": e.response.text
                }
            )
            return False
        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error sending Teams notification card: {e}",
                extra={"work_item_id": context.work_item.id},
                exc_info=True
            )
            return False

    async def handle_card_action(self, action_data: Dict[str, Any]) -> bool:
        """
        Handle action submitted from Teams adaptive card.

        Args:
            action_data: Action data from Teams card submission

        Returns:
            True if action was handled successfully
        """
        try:
            action = action_data.get("action")
            work_item_id = action_data.get("work_item_id")

            if not action or not work_item_id:
                logger.warning("Invalid action data received from Teams card")
                return False

            log_structured(
                logger,
                "info",
                f"Handling Teams card action: {action}",
                extra={
                    "action": action,
                    "work_item_id": work_item_id,
                    "action_data": action_data
                }
            )

            # Handle different action types
            if action == "assign_to_me":
                return await self._handle_assign_to_me(work_item_id, action_data)
            elif action == "acknowledge":
                return await self._handle_acknowledge(work_item_id, action_data)
            elif action == "mark_duplicate":
                return await self._handle_mark_duplicate(work_item_id, action_data)
            elif action == "escalate_security":
                return await self._handle_escalate_security(work_item_id, action_data)
            elif action == "submit_feedback":
                return await self._handle_submit_feedback(work_item_id, action_data)
            elif action == "quick_accept":
                return await self._handle_quick_accept(work_item_id, action_data)
            elif action == "request_reassignment":
                return await self._handle_request_reassignment(work_item_id, action_data)
            else:
                logger.warning(f"Unknown action type: {action}")
                return False

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Error handling Teams card action: {e}",
                extra={"action_data": action_data},
                exc_info=True
            )
            return False

    async def _handle_assign_to_me(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle assign to me action."""
        try:
            # Get user information from action data
            user_email = action_data.get("user_email") or action_data.get("user_id")
            user_name = action_data.get("user_name", "Unknown User")

            if not user_email:
                log_structured(
                    logger,
                    "warning",
                    f"No user email provided for assign to me action on work item {work_item_id}",
                    extra={"work_item_id": work_item_id, "action_data": action_data}
                )
                return False

            # Import ADO client here to avoid circular imports
            from ..adapters.ado_client import AdoClient
            from ..utils.config import get_config

            # Initialize ADO client
            config = get_config()
            ado_client = AdoClient(config)

            # Update work item assignment
            updates = {
                "System.AssignedTo": user_email,
                "System.History": f"🙋‍♂️ Work item assigned to {user_name} via Teams notification action"
            }

            await ado_client.update_work_item(work_item_id, updates)

            # Send confirmation back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "assign_to_me",
                f"✅ Work item successfully assigned to {user_name}",
                action_data
            )

            log_structured(
                logger,
                "info",
                f"Successfully assigned work item {work_item_id} to {user_name}",
                extra={
                    "work_item_id": work_item_id,
                    "assignee": user_email,
                    "user_name": user_name
                }
            )
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to assign work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )

            # Send error notification back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "assign_to_me",
                f"❌ Failed to assign work item: {str(e)}",
                action_data
            )
            return False

    def _create_defect_attachment(self, work_item: WorkItem, triage_result: TriageResult) -> str:
        """
        Create a detailed defect analysis attachment.

        Args:
            work_item: The work item that was triaged
            triage_result: The triage results

        Returns:
            Base64 encoded attachment content
        """
        import base64
        from datetime import datetime

        # Create comprehensive defect analysis report
        report_content = f"""DEFECT ANALYSIS REPORT
{'=' * 80}

WORK ITEM INFORMATION
{'=' * 40}
ID: {work_item.id}
Title: {work_item.title or 'No title'}
Type: {work_item.work_item_type or 'Unknown'}
State: {work_item.state or 'Unknown'}
Priority: P{work_item.priority or 'Unknown'}
Created: {work_item.created_date or 'Unknown'}
Project: {work_item.project or 'Unknown'}

LOCATION & OWNERSHIP
{'=' * 40}
Area Path: {work_item.area_path or 'Not specified'}
Iteration: {work_item.iteration_path or 'Not specified'}
Tags: {work_item.tags or 'None'}

DESCRIPTION
{'=' * 40}
{work_item.description or 'No description provided'}

AI TRIAGE ANALYSIS
{'=' * 40}
Assigned To: {triage_result.assigned_to or 'Unassigned'}
Recommended Priority: P{triage_result.priority or 'Unknown'}
Confidence Score: {int(triage_result.confidence_score * 100) if triage_result.confidence_score else 'N/A'}%
Processing Time: {triage_result.processing_time_ms or 0}ms

ASSIGNMENT REASONING
{'=' * 40}
{triage_result.reasoning or 'No reasoning provided'}

DUPLICATE ANALYSIS
{'=' * 40}
Potential Duplicates Found: {len(triage_result.duplicates) if triage_result.duplicates else 0}
"""

        # Add duplicate details if any found
        if triage_result.duplicates:
            report_content += "\nDUPLICATE DETAILS:\n"
            for i, duplicate in enumerate(triage_result.duplicates[:5], 1):
                report_content += f"\n{i}. Work Item {duplicate.work_item_id}\n"
                report_content += f"   Title: {duplicate.title}\n"
                report_content += f"   Similarity: {duplicate.similarity_score:.2%}\n"
                report_content += f"   State: {duplicate.state}\n"

        # Add recommendations
        report_content += f"""

RECOMMENDATIONS
{'=' * 40}
1. Review the assigned team and priority recommendations
2. Check for potential duplicates listed above
3. Verify the area path assignment is correct
4. Consider the AI confidence score when making decisions
5. Update work item status and assignment as needed

NEXT STEPS
{'=' * 40}
• Accept the AI assignment recommendation
• Review and update priority if needed
• Check for any missing information in the description
• Coordinate with the assigned team for resolution
• Monitor progress and update stakeholders

AZURE DEVOPS LINKS
{'=' * 40}
Work Item URL: https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item.id}
Project Dashboard: https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}

REPORT METADATA
{'=' * 40}
Generated: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}
System: AutoDefectTriage v1.0
AI Model: Sentence Transformers (all-MiniLM-L6-v2)
Analysis Type: Automated Triage with Machine Learning

{'=' * 80}
END OF REPORT
"""

        # Encode content as base64
        content_bytes = report_content.encode('utf-8')
        base64_content = base64.b64encode(content_bytes).decode('utf-8')

        return base64_content

    async def _handle_acknowledge(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle acknowledge action."""
        try:
            # Get user information from action data
            user_name = action_data.get("user_name", "Unknown User")
            user_email = action_data.get("user_email") or action_data.get("user_id")
            acknowledgment_note = action_data.get("note", "")

            # Import ADO client here to avoid circular imports
            from ..adapters.ado_client import AdoClient
            from ..utils.config import get_config

            # Initialize ADO client
            config = get_config()
            ado_client = AdoClient(config)

            # Create acknowledgment comment
            comment = f"✅ Work item acknowledged by {user_name}"
            if acknowledgment_note:
                comment += f"\n📝 Note: {acknowledgment_note}"
            comment += f"\n⏰ Acknowledged at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"

            # Update work item with acknowledgment
            updates = {
                "System.History": comment
            }

            # If work item is in New state, move it to Active
            work_item = await ado_client.get_work_item(work_item_id)
            current_state = work_item.get("fields", {}).get("System.State", "")

            if current_state.lower() in ["new", "to do"]:
                updates["System.State"] = "Active"
                comment += "\n🔄 State changed from 'New' to 'Active'"

            await ado_client.update_work_item(work_item_id, updates)

            # Send confirmation back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "acknowledge",
                f"✅ Work item acknowledged by {user_name}",
                action_data
            )

            log_structured(
                logger,
                "info",
                f"Work item {work_item_id} acknowledged by {user_name}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name,
                    "user_email": user_email,
                    "note": acknowledgment_note
                }
            )
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to acknowledge work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )

            # Send error notification back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "acknowledge",
                f"❌ Failed to acknowledge work item: {str(e)}",
                action_data
            )
            return False

    async def _handle_mark_duplicate(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle mark as duplicate action."""
        try:
            # Get user information and duplicate details from action data
            user_name = action_data.get("user_name", "Unknown User")
            user_email = action_data.get("user_email") or action_data.get("user_id")
            duplicate_of_id = action_data.get("duplicate_of_id")
            duplicate_reason = action_data.get("reason", "")

            # Import ADO client here to avoid circular imports
            from ..adapters.ado_client import AdoClient
            from ..utils.config import get_config

            # Initialize ADO client
            config = get_config()
            ado_client = AdoClient(config)

            # Create duplicate marking comment
            comment = f"🔄 Work item marked as duplicate by {user_name}"
            if duplicate_of_id:
                comment += f"\n🔗 Duplicate of work item #{duplicate_of_id}"
            if duplicate_reason:
                comment += f"\n📝 Reason: {duplicate_reason}"
            comment += f"\n⏰ Marked at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"

            # Update work item to mark as duplicate
            updates = {
                "System.State": "Closed",
                "System.Reason": "Duplicate",
                "System.History": comment
            }

            # Add duplicate relationship if duplicate_of_id is provided
            if duplicate_of_id:
                # Note: Adding work item relations requires a separate API call
                try:
                    await self._add_duplicate_relation(work_item_id, duplicate_of_id, ado_client)
                except Exception as e:
                    log_structured(
                        logger,
                        "warning",
                        f"Failed to add duplicate relation: {e}",
                        extra={"work_item_id": work_item_id, "duplicate_of_id": duplicate_of_id}
                    )

            await ado_client.update_work_item(work_item_id, updates)

            # Send confirmation back to Teams
            confirmation_msg = f"✅ Work item marked as duplicate by {user_name}"
            if duplicate_of_id:
                confirmation_msg += f" (duplicate of #{duplicate_of_id})"

            await self._send_action_confirmation(
                work_item_id,
                "mark_duplicate",
                confirmation_msg,
                action_data
            )

            log_structured(
                logger,
                "info",
                f"Work item {work_item_id} marked as duplicate by {user_name}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name,
                    "duplicate_of_id": duplicate_of_id,
                    "reason": duplicate_reason
                }
            )
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to mark work item {work_item_id} as duplicate: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )

            # Send error notification back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "mark_duplicate",
                f"❌ Failed to mark as duplicate: {str(e)}",
                action_data
            )
            return False

    async def _handle_escalate_security(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle escalate to security action."""
        try:
            # Get user information from action data
            user_name = action_data.get("user_name", "Unknown User")
            user_email = action_data.get("user_email") or action_data.get("user_id")
            escalation_reason = action_data.get("reason", "")
            security_team = action_data.get("security_team", "Security Team")

            # Import ADO client here to avoid circular imports
            from ..adapters.ado_client import AdoClient
            from ..utils.config import get_config

            # Initialize ADO client
            config = get_config()
            ado_client = AdoClient(config)

            # Create escalation comment
            comment = f"🚨 Work item escalated to security by {user_name}"
            if escalation_reason:
                comment += f"\n📝 Escalation reason: {escalation_reason}"
            comment += f"\n👥 Escalated to: {security_team}"
            comment += f"\n⏰ Escalated at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}"
            comment += f"\n🔒 This item requires security review and approval"

            # Update work item with security escalation
            updates = {
                "System.History": comment,
                "System.Tags": "Security; Escalated; High-Priority"  # Add security tags
            }

            # Set high priority if not already set
            work_item = await ado_client.get_work_item(work_item_id)
            current_priority = work_item.get("fields", {}).get("Microsoft.VSTS.Common.Priority", 4)

            if current_priority > 2:  # If priority is not already high (1 or 2)
                updates["Microsoft.VSTS.Common.Priority"] = 1  # Set to highest priority
                comment += "\n📈 Priority elevated to P1 due to security escalation"

            await ado_client.update_work_item(work_item_id, updates)

            # Send security team notification
            await self._send_security_escalation_notification(
                work_item_id,
                user_name,
                escalation_reason,
                security_team
            )

            # Send confirmation back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "escalate_security",
                f"🚨 Work item escalated to security team by {user_name}",
                action_data
            )

            log_structured(
                logger,
                "info",
                f"Work item {work_item_id} escalated to security by {user_name}",
                extra={
                    "work_item_id": work_item_id,
                    "user_name": user_name,
                    "escalation_reason": escalation_reason,
                    "security_team": security_team
                }
            )
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to escalate work item {work_item_id} to security: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )

            # Send error notification back to Teams
            await self._send_action_confirmation(
                work_item_id,
                "escalate_security",
                f"❌ Failed to escalate to security: {str(e)}",
                action_data
            )
            return False

    async def _send_action_confirmation(
        self,
        work_item_id: int,
        action: str,
        message: str,
        action_data: Dict[str, Any]
    ) -> bool:
        """
        Send confirmation message back to Teams after processing an action.

        Args:
            work_item_id: The work item ID
            action: The action that was performed
            message: Confirmation message to send
            action_data: Original action data containing user info

        Returns:
            True if confirmation was sent successfully
        """
        try:
            # Create simple confirmation message
            confirmation_payload = {
                "To": action_data.get("user_email", "<EMAIL>"),
                "Subject": f"Action Confirmation - Work Item {work_item_id}",
                "Body": f"""
**Action Confirmation**

{message}

**Details:**
- Work Item: #{work_item_id}
- Action: {action.replace('_', ' ').title()}
- Processed: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}

You can view the work item in Azure DevOps for the latest updates.
                """.strip(),
                "Attachments": "",
                "attachmentName": ""
            }

            # Send confirmation via Teams Logic App if available
            if self.teams_logic_app_url:
                response = await self.client.post(
                    self.teams_logic_app_url,
                    json=confirmation_payload
                )
                response.raise_for_status()

                log_structured(
                    logger,
                    "info",
                    f"Sent action confirmation for work item {work_item_id}",
                    extra={
                        "work_item_id": work_item_id,
                        "action": action,
                        "user_email": action_data.get("user_email")
                    }
                )
                return True

            return False

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send action confirmation: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "action": action,
                    "message": message
                },
                exc_info=True
            )
            return False

    async def _add_duplicate_relation(
        self,
        work_item_id: int,
        duplicate_of_id: int,
        ado_client
    ) -> bool:
        """
        Add a duplicate relation between two work items.

        Args:
            work_item_id: The work item being marked as duplicate
            duplicate_of_id: The work item it's a duplicate of
            ado_client: ADO client instance

        Returns:
            True if relation was added successfully
        """
        try:
            # Use ADO REST API to add work item relation
            url = f"{ado_client.base_url}/{ado_client.project}/_apis/wit/workitems/{work_item_id}"
            params = {"api-version": "7.0"}

            # Create relation patch operation
            patch_operations = [{
                "op": "add",
                "path": "/relations/-",
                "value": {
                    "rel": "System.LinkTypes.Duplicate-Forward",
                    "url": f"{ado_client.base_url}/{ado_client.project}/_apis/wit/workitems/{duplicate_of_id}",
                    "attributes": {
                        "comment": f"Marked as duplicate via Teams notification action"
                    }
                }
            }]

            response = await ado_client.client.patch(
                url,
                params=params,
                json=patch_operations,
                headers={"Content-Type": "application/json-patch+json"}
            )
            response.raise_for_status()

            log_structured(
                logger,
                "info",
                f"Added duplicate relation: {work_item_id} -> {duplicate_of_id}",
                extra={
                    "work_item_id": work_item_id,
                    "duplicate_of_id": duplicate_of_id
                }
            )
            return True

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to add duplicate relation: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "duplicate_of_id": duplicate_of_id
                },
                exc_info=True
            )
            return False

    async def _send_security_escalation_notification(
        self,
        work_item_id: int,
        escalated_by: str,
        reason: str,
        security_team: str
    ) -> bool:
        """
        Send notification to security team about escalated work item.

        Args:
            work_item_id: The work item ID
            escalated_by: User who escalated the item
            reason: Reason for escalation
            security_team: Security team to notify

        Returns:
            True if notification was sent successfully
        """
        try:
            # Create security escalation notification
            security_payload = {
                "To": "<EMAIL>",  # Configure this in settings
                "Subject": f"🚨 SECURITY ESCALATION - Work Item {work_item_id}",
                "Body": f"""
**SECURITY ESCALATION ALERT**

A work item has been escalated to the security team and requires immediate attention.

**Work Item Details:**
- ID: #{work_item_id}
- Escalated by: {escalated_by}
- Escalation reason: {reason or 'No specific reason provided'}
- Security team: {security_team}
- Escalated at: {datetime.utcnow().strftime('%Y-%m-%d %H:%M:%S UTC')}

**Required Actions:**
1. Review the work item for security implications
2. Assess the severity and impact
3. Coordinate with the development team
4. Provide security guidance and approval

**Azure DevOps Link:**
https://dev.azure.com/{self.config.get('ADO_ORGANIZATION', 'virginatlantic')}/{self.config.get('ADO_PROJECT', 'Air4 Channels Testing')}/_workitems/edit/{work_item_id}

This is an automated notification from the AutoDefectTriage system.
                """.strip(),
                "Attachments": "",
                "attachmentName": ""
            }

            # Send notification via Teams Logic App if available
            if self.teams_logic_app_url:
                response = await self.client.post(
                    self.teams_logic_app_url,
                    json=security_payload
                )
                response.raise_for_status()

                log_structured(
                    logger,
                    "info",
                    f"Sent security escalation notification for work item {work_item_id}",
                    extra={
                        "work_item_id": work_item_id,
                        "escalated_by": escalated_by,
                        "security_team": security_team
                    }
                )
                return True

            return False

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send security escalation notification: {e}",
                extra={
                    "work_item_id": work_item_id,
                    "escalated_by": escalated_by,
                    "reason": reason
                },
                exc_info=True
            )
            return False

    async def _send_simple_message(
        self,
        recipient: str,
        subject: str,
        message: str
    ) -> bool:
        """
        Send a simple text message via Teams Logic App.

        Args:
            recipient: Recipient name or email
            subject: Message subject
            message: Message content

        Returns:
            True if message was sent successfully
        """
        try:
            # Create simple message payload
            message_payload = {
                "To": "<EMAIL>",  # Default recipient
                "Subject": subject,
                "Body": message,
                "Attachments": "",
                "attachmentName": ""
            }

            # Send via Teams Logic App if available
            if self.teams_logic_app_url:
                response = await self.client.post(
                    self.teams_logic_app_url,
                    json=message_payload
                )
                response.raise_for_status()

                log_structured(
                    logger,
                    "info",
                    f"Sent simple message to {recipient}",
                    extra={
                        "recipient": recipient,
                        "subject": subject
                    }
                )
                return True

            return False

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to send simple message: {e}",
                extra={
                    "recipient": recipient,
                    "subject": subject
                },
                exc_info=True
            )
            return False

    async def _handle_submit_feedback(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle feedback submission from enhanced adaptive card."""
        try:
            # Extract feedback data
            reply_text = action_data.get("replyText", "")
            priority = action_data.get("priority")
            user_email = action_data.get("user_email") or action_data.get("user_id")
            user_name = action_data.get("user_name", "Unknown User")

            # Create response data for feedback service
            response_data = {
                "work_item_id": work_item_id,
                "replyText": reply_text,
                "priority": priority,
                "user_email": user_email,
                "user_name": user_name,
                "source": "teams_adaptive_card"
            }

            # Import and use feedback service
            from ..services.defect_feedback_service import DefectFeedbackService
            from ..utils.config import get_config

            config = get_config()
            feedback_service = DefectFeedbackService(config)

            # Process the feedback
            success = await feedback_service.process_teams_adaptive_card_response(
                work_item_id,
                response_data
            )

            if success:
                # Send confirmation
                await self._send_action_confirmation(
                    work_item_id,
                    "submit_feedback",
                    f"✅ Feedback submitted successfully by {user_name}",
                    action_data
                )

            return success

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to handle feedback submission for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )
            return False

    async def _handle_quick_accept(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle quick accept action."""
        try:
            user_name = action_data.get("user_name", "Unknown User")

            # Process as feedback submission
            response_data = {
                "work_item_id": work_item_id,
                "replyText": "I accept the AI triage recommendations.",
                "priority": action_data.get("priority"),
                "user_email": action_data.get("user_email"),
                "user_name": user_name,
                "source": "teams_quick_action"
            }

            # Import and use feedback service
            from ..services.defect_feedback_service import DefectFeedbackService
            from ..utils.config import get_config

            config = get_config()
            feedback_service = DefectFeedbackService(config)

            success = await feedback_service.process_teams_adaptive_card_response(
                work_item_id,
                response_data
            )

            if success:
                await self._send_action_confirmation(
                    work_item_id,
                    "quick_accept",
                    f"✅ AI triage accepted by {user_name}",
                    action_data
                )

            return success

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to handle quick accept for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )
            return False

    async def _handle_request_reassignment(self, work_item_id: int, action_data: Dict[str, Any]) -> bool:
        """Handle reassignment request action."""
        try:
            user_name = action_data.get("user_name", "Unknown User")

            # Process as feedback submission
            response_data = {
                "work_item_id": work_item_id,
                "replyText": "Please reassign this work item to a different team.",
                "priority": action_data.get("priority"),
                "user_email": action_data.get("user_email"),
                "user_name": user_name,
                "source": "teams_quick_action"
            }

            # Import and use feedback service
            from ..services.defect_feedback_service import DefectFeedbackService
            from ..utils.config import get_config

            config = get_config()
            feedback_service = DefectFeedbackService(config)

            success = await feedback_service.process_teams_adaptive_card_response(
                work_item_id,
                response_data
            )

            if success:
                await self._send_action_confirmation(
                    work_item_id,
                    "request_reassignment",
                    f"🔄 Reassignment requested by {user_name}",
                    action_data
                )

            return success

        except Exception as e:
            log_structured(
                logger,
                "error",
                f"Failed to handle reassignment request for work item {work_item_id}: {e}",
                extra={"work_item_id": work_item_id, "action_data": action_data},
                exc_info=True
            )
            return False

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
