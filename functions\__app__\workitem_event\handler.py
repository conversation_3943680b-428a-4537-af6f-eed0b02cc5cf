"""
Work Item Event Handler - Event-driven processing for ADO Service Hooks
Replaces hardcoded work item processing with configurable guardrails.
"""

import json
import logging
from datetime import datetime
from typing import Dict, Any, Optional
import azure.functions as func

from ..common.utils.config import get_config
from ..common.utils.logging import log_structured
from ..common.adapters.ado_client import AdoClient
from ..common.services.store import IdempotencyStore
from ..common.services.triage import TriageService
from ..common.services.history import HistoryService
from ..common.logicapp_client import send_personal_card, LogicAppError

logger = logging.getLogger(__name__)


async def workitem_event_handler(event_data: Dict[str, Any]) -> func.HttpResponse:
    """
    Handle ADO Service Hook events for work item creation/updates.
    
    Args:
        event_data: ADO Service Hook payload
        
    Returns:
        HTTP response with processing results
    """
    start_time = datetime.utcnow()
    
    try:
        # Load configuration
        config = get_config()
        
        # Validate event type
        event_type = event_data.get('eventType', '')
        if event_type not in ['workitem.created', 'workitem.updated']:
            log_structured(
                logger,
                "info",
                f"Ignoring unsupported event type: {event_type}",
                extra={"event_type": event_type}
            )
            return func.HttpResponse(
                json.dumps({
                    "status": "ignored",
                    "message": f"Event type '{event_type}' not supported",
                    "supported_types": ["workitem.created", "workitem.updated"]
                }),
                status_code=200,
                mimetype="application/json"
            )
        
        # Extract work item information
        resource = event_data.get('resource', {})
        work_item_id = resource.get('id') or resource.get('workItemId')  # Support both formats
        revision = resource.get('rev', 1)
        
        if not work_item_id:
            return func.HttpResponse(
                json.dumps({
                    "status": "error",
                    "message": "work item ID not found in event payload (checked 'id' and 'workItemId' fields)"
                }),
                status_code=400,
                mimetype="application/json"
            )
        
        # Extract project information
        project_info = event_data.get('resourceContainers', {}).get('project', {})
        project_id = project_info.get('id', '')
        project_name = project_info.get('name', '')
        
        log_structured(
            logger,
            "info",
            f"Processing {event_type} for work item {work_item_id}",
            extra={
                "work_item_id": work_item_id,
                "event_type": event_type,
                "project_id": project_id,
                "project_name": project_name,
                "revision": revision
            }
        )
        
        # Apply production safety guardrails
        safety_check = await apply_safety_guardrails(config, work_item_id, project_name)
        if safety_check:
            return safety_check
        
        # Check idempotency
        idempotency_key = f"{work_item_id}:{revision}"
        store = IdempotencyStore(config)
        
        if await store.is_processed(idempotency_key):
            log_structured(
                logger,
                "info",
                f"Event already processed: {idempotency_key}",
                extra={"work_item_id": work_item_id, "revision": revision}
            )
            return func.HttpResponse(
                json.dumps({
                    "status": "ignored",
                    "message": f"Event {idempotency_key} already processed",
                    "work_item_id": work_item_id
                }),
                status_code=200,
                mimetype="application/json"
            )
        
        # Mark as being processed
        await store.mark_processing(idempotency_key)
        
        try:
            # Process the work item
            result = await process_work_item(config, work_item_id, event_type)
            
            # Mark as completed
            await store.mark_completed(idempotency_key, result)
            
            log_structured(
                logger,
                "info",
                f"Successfully processed work item {work_item_id}",
                extra={
                    "work_item_id": work_item_id,
                    "duration_ms": (datetime.utcnow() - start_time).total_seconds() * 1000,
                    "result": result
                }
            )
            
            return func.HttpResponse(
                json.dumps({
                    "status": "success",
                    "work_item_id": work_item_id,
                    "event_type": event_type,
                    "result": result,
                    "timestamp": datetime.utcnow().isoformat()
                }),
                status_code=200,
                mimetype="application/json"
            )
            
        except Exception as e:
            # Mark as failed
            await store.mark_failed(idempotency_key, str(e))
            raise
        
    except Exception as e:
        log_structured(
            logger,
            "error",
            f"Error processing work item event: {e}",
            extra={
                "error": str(e),
                "event_data": event_data,
                "duration_ms": (datetime.utcnow() - start_time).total_seconds() * 1000
            }
        )
        
        # Return 200 to prevent ADO retry storms
        return func.HttpResponse(
            json.dumps({
                "status": "error",
                "message": "Internal processing error",
                "timestamp": datetime.utcnow().isoformat()
            }),
            status_code=200,
            mimetype="application/json"
        )


async def apply_safety_guardrails(
    config, 
    work_item_id: int, 
    project_name: str
) -> Optional[func.HttpResponse]:
    """
    Apply production safety guardrails.
    
    Returns:
        HttpResponse if request should be blocked, None if allowed
    """
    
    # Check project allow-list
    if not config.is_project_allowed(project_name):
        log_structured(
            logger,
            "warning",
            f"Project '{project_name}' not in allow-list",
            extra={
                "project_name": project_name,
                "allowed_projects": config.get_allowed_projects(),
                "work_item_id": work_item_id
            }
        )
        return func.HttpResponse(
            json.dumps({
                "status": "ignored",
                "message": f"Project '{project_name}' not in allow-list",
                "work_item_id": work_item_id,
                "allowed_projects": config.get_allowed_projects()
            }),
            status_code=200,
            mimetype="application/json"
        )
    
    # Check work item ID restriction
    if not config.is_work_item_allowed(work_item_id):
        log_structured(
            logger,
            "warning",
            f"Work item {work_item_id} not allowed by safety settings",
            extra={
                "work_item_id": work_item_id,
                "safety_setting": config.SAFETY_ONLY_WORKITEM_ID
            }
        )
        return func.HttpResponse(
            json.dumps({
                "status": "ignored",
                "message": f"Work item {work_item_id} not allowed by safety settings",
                "work_item_id": work_item_id,
                "safety_setting": config.SAFETY_ONLY_WORKITEM_ID
            }),
            status_code=200,
            mimetype="application/json"
        )
    
    return None


async def process_work_item(config, work_item_id: int, event_type: str) -> Dict[str, Any]:
    """
    Process a work item through the triage pipeline.
    
    Args:
        config: Configuration object
        work_item_id: Work item ID to process
        event_type: Type of event (created/updated)
        
    Returns:
        Processing results
    """
    
    # Initialize ADO client
    ado_client = AdoClient(config)
    
    # Fetch work item details
    work_item_data = await ado_client.get_work_item(work_item_id)
    if not work_item_data:
        raise ValueError(f"Work item {work_item_id} not found")
    
    # Initialize result tracking
    result = {
        "work_item_id": work_item_id,
        "event_type": event_type,
        "title": work_item_data.get('fields', {}).get('System.Title', ''),
        "state": work_item_data.get('fields', {}).get('System.State', ''),
        "read_only_mode": config.READ_ONLY,
        "processing_timestamp": datetime.utcnow().isoformat(),
        "pipeline_steps": {
            "fetch_work_item": "completed",
            "normalize_text": "pending",
            "find_similar": "pending",
            "calculate_assignment": "pending",
            "determine_priority": "pending",
            "send_notification": "pending",
            "update_ado": "pending" if not config.READ_ONLY else "skipped"
        },
        "recommendations": {},
        "logic_app_response": None
    }

    try:
        # Initialize services
        history_service = HistoryService(config)
        triage_service = TriageService(config)

        # Extract work item fields
        fields = work_item_data.get('fields', {})
        title = fields.get('System.Title', '')
        description = fields.get('System.Description', '')
        work_item_type = fields.get('System.WorkItemType', '')
        current_assignee = fields.get('System.AssignedTo', {}).get('displayName', '')
        current_assignee_email = fields.get('System.AssignedTo', {}).get('uniqueName', '')

        # 1. Find similar historical items
        result["pipeline_steps"]["find_similar"] = "running"

        # Create work item dict for history service
        work_item_dict = {
            'id': work_item_id,
            'fields': {
                'System.Title': title,
                'System.Description': description,
                'System.WorkItemType': work_item_type
            }
        }

        # TODO: Generate embedding vector for the work item
        # For now, we'll use a placeholder - this should be implemented with proper embedding service
        query_vector = [0.0] * 1536  # Placeholder vector

        similar_items = await history_service.find_similar_items(
            work_item=work_item_dict,
            query_vector=query_vector,
            k=10,
            include_resolved=True
        )
        result["pipeline_steps"]["find_similar"] = "completed"
        result["similar_items_count"] = len(similar_items)

        # Convert SearchHit objects to dict format for triage service
        similar_items_dict = []
        for item in similar_items:
            similar_items_dict.append({
                'id': item.id,
                'score': item.score,
                'title': item.metadata.get('title', ''),
                'assigned_to': item.metadata.get('assigned_to', ''),
                'work_item_type': item.metadata.get('work_item_type', ''),
                'state': item.metadata.get('state', ''),
                'area_path': item.metadata.get('area_path', ''),
                'iteration_path': item.metadata.get('iteration_path', ''),
                'created_date': item.metadata.get('created_date', '')
            })

        # 2. Get triage recommendations using REAL historical data
        result["pipeline_steps"]["calculate_assignment"] = "running"
        recommendations = await triage_service.get_triage_recommendations(
            work_item_id=work_item_id,
            title=title,
            description=description,
            work_item_type=work_item_type,
            similar_items=similar_items_dict
        )
        result["pipeline_steps"]["calculate_assignment"] = "completed"
        result["recommendations"] = recommendations

        # 3. Determine target assignee for notification
        target_assignee_email = None
        if recommendations.get('suggested_assignees'):
            # Use top suggested assignee
            top_assignee = recommendations['suggested_assignees'][0]
            target_assignee_email = top_assignee.get('email', '')
            result["notification_target"] = "suggested_assignee"
        elif current_assignee_email:
            # Fallback to current assignee
            target_assignee_email = current_assignee_email
            result["notification_target"] = "current_assignee"

        # 4. Send Teams personal chat notification via Logic App
        if target_assignee_email and config.LOGICAPP_URL:
            result["pipeline_steps"]["send_notification"] = "running"

            try:
                # Prepare notification content
                subject = f"[#{work_item_id}] {title}"

                # Build body with summary and recommendations
                body_parts = [
                    f"Work Item: {work_item_id}",
                    f"Title: {title}",
                    f"Type: {work_item_type}",
                    f"State: {fields.get('System.State', 'Unknown')}"
                ]

                if recommendations.get('priority_recommendation'):
                    priority_rec = recommendations['priority_recommendation']
                    body_parts.append(f"Recommended Priority: {priority_rec.get('priority', 'Unknown')} (Confidence: {priority_rec.get('confidence', 0):.2f})")
                    body_parts.append(f"Rationale: {priority_rec.get('rationale', 'No rationale provided')}")

                if similar_items:
                    body_parts.append(f"\nSimilar Items Found: {len(similar_items)}")
                    for i, item in enumerate(similar_items[:3]):  # Top 3
                        body_parts.append(f"  {i+1}. #{item.get('id', 'Unknown')} - {item.get('title', 'No title')[:50]}...")

                body = "\n".join(body_parts)

                # TODO: Build Adaptive Card (optional enhancement)
                adaptive_card = None

                # Send via Logic App
                if not config.BYPASS_LOGICAPP_AND_UPDATE_ADO:
                    logic_app_response = await send_personal_card(
                        to_email=target_assignee_email,
                        subject=subject,
                        body=body,
                        work_item_id=work_item_id,
                        adaptive_card=adaptive_card
                    )

                    result["logic_app_response"] = logic_app_response
                    result["pipeline_steps"]["send_notification"] = "completed"

                    # Log the tracking ID
                    tracking_id = logic_app_response.get('trackingId', 'unknown')
                    log_structured(
                        logger,
                        "info",
                        f"Logic App notification sent for work item {work_item_id}",
                        extra={
                            "work_item_id": work_item_id,
                            "tracking_id": tracking_id,
                            "target_email_domain": target_assignee_email.split('@')[-1] if '@' in target_assignee_email else "unknown"
                        }
                    )
                else:
                    # Bypass mode for testing
                    result["pipeline_steps"]["send_notification"] = "bypassed"
                    log_structured(
                        logger,
                        "info",
                        f"Logic App bypassed for work item {work_item_id} (BYPASS_LOGICAPP_AND_UPDATE_ADO=true)",
                        extra={"work_item_id": work_item_id}
                    )

            except LogicAppError as e:
                result["pipeline_steps"]["send_notification"] = "failed"
                result["notification_error"] = str(e)
                log_structured(
                    logger,
                    "error",
                    f"Logic App notification failed for work item {work_item_id}: {e}",
                    extra={"work_item_id": work_item_id, "error": str(e)}
                )
        else:
            result["pipeline_steps"]["send_notification"] = "skipped"
            result["notification_skip_reason"] = "no_target_email" if not target_assignee_email else "no_logicapp_url"

        # 5. Update ADO (if not in read-only mode and bypass is enabled)
        if config.BYPASS_LOGICAPP_AND_UPDATE_ADO and not config.READ_ONLY:
            result["pipeline_steps"]["update_ado"] = "running"

            # Add comment with recommendations
            if config.ALLOW_COMMENTS_IN_READ_ONLY or not config.READ_ONLY:
                comment_parts = ["🤖 AutoDefectTriage Recommendations:"]

                if recommendations.get('suggested_assignees'):
                    comment_parts.append("\n📋 Suggested Assignees:")
                    for i, assignee in enumerate(recommendations['suggested_assignees'][:3]):
                        comment_parts.append(f"  {i+1}. {assignee.get('name', 'Unknown')} (Score: {assignee.get('score', 0):.2f})")

                if recommendations.get('priority_recommendation'):
                    priority_rec = recommendations['priority_recommendation']
                    comment_parts.append(f"\n⚡ Priority: {priority_rec.get('priority', 'Unknown')} (Confidence: {priority_rec.get('confidence', 0):.2f})")
                    comment_parts.append(f"Rationale: {priority_rec.get('rationale', 'No rationale')}")

                if similar_items:
                    comment_parts.append(f"\n🔍 Similar Items ({len(similar_items)} found):")
                    for i, item in enumerate(similar_items[:3]):
                        comment_parts.append(f"  {i+1}. #{item.get('id', 'Unknown')} - {item.get('title', 'No title')[:50]}...")

                comment_text = "\n".join(comment_parts)

                try:
                    await ado_client.add_comment(work_item_id, comment_text)
                    result["ado_comment_added"] = True
                except Exception as e:
                    result["ado_comment_error"] = str(e)
                    log_structured(
                        logger,
                        "error",
                        f"Failed to add ADO comment for work item {work_item_id}: {e}",
                        extra={"work_item_id": work_item_id, "error": str(e)}
                    )

            result["pipeline_steps"]["update_ado"] = "completed"

    except Exception as e:
        result["pipeline_error"] = str(e)
        log_structured(
            logger,
            "error",
            f"Pipeline processing failed for work item {work_item_id}: {e}",
            extra={"work_item_id": work_item_id, "error": str(e)}
        )
    
    log_structured(
        logger,
        "info",
        f"Work item processing pipeline initialized for {work_item_id}",
        extra=result
    )
    
    return result
