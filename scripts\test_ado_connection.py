#!/usr/bin/env python3
"""
Test Azure DevOps connection and fetch defects using the running Azure Functions.
"""

import asyncio
import httpx
import json
from datetime import datetime


async def test_function_endpoint():
    """Test the Azure Functions endpoint to get work items."""
    try:
        # The Azure Functions should be running on localhost:7071
        base_url = "http://localhost:7071"
        
        print("🔗 Testing Azure Functions endpoint...")
        print(f"   Base URL: {base_url}")
        
        # Test the work_item_check_manual endpoint which should return recent work items
        endpoint = f"{base_url}/api/work_item_check"
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            print(f"🔍 Calling endpoint: {endpoint}")
            
            # Make a GET request to trigger the work item check
            response = await client.get(endpoint)
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Successfully retrieved data")
                    print(f"📋 Response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")
                    
                    # Pretty print the response
                    print(f"\n📄 Response Data:")
                    print(json.dumps(data, indent=2)[:2000])  # First 2000 chars
                    
                    return data
                except json.JSONDecodeError as e:
                    print(f"❌ Failed to parse JSON response: {e}")
                    print(f"📄 Raw response: {response.text[:500]}")
                    return None
            else:
                print(f"❌ Request failed with status {response.status_code}")
                print(f"📄 Response: {response.text[:500]}")
                return None
                
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return None


async def call_backfill_job():
    """Call the backfill job to see what work items are available."""
    try:
        base_url = "http://localhost:7071"
        endpoint = f"{base_url}/api/backfill_job"
        
        print(f"\n🔍 Testing backfill job endpoint: {endpoint}")
        
        async with httpx.AsyncClient(timeout=120.0) as client:
            # Make a POST request to trigger the backfill job
            response = await client.post(endpoint, json={})
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Backfill job completed successfully")
                    print(f"📋 Response: {json.dumps(data, indent=2)[:1000]}")
                    return data
                except json.JSONDecodeError:
                    print(f"✅ Backfill job completed (non-JSON response)")
                    print(f"📄 Response: {response.text[:500]}")
                    return {"status": "success", "message": response.text}
            else:
                print(f"❌ Backfill job failed with status {response.status_code}")
                print(f"📄 Response: {response.text[:500]}")
                return None
                
    except Exception as e:
        print(f"❌ Error calling backfill job: {e}")
        return None


async def call_step_by_step_workflow():
    """Call the step-by-step workflow to analyze work items."""
    try:
        base_url = "http://localhost:7071"
        endpoint = f"{base_url}/api/step_by_step_workflow"
        
        print(f"\n🔍 Testing step-by-step workflow: {endpoint}")
        
        # Sample request to get recent work items
        request_data = {
            "action": "get_recent_work_items",
            "hours_back": 24
        }
        
        async with httpx.AsyncClient(timeout=60.0) as client:
            response = await client.post(endpoint, json=request_data)
            
            print(f"📊 Response Status: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    print(f"✅ Step-by-step workflow completed")
                    
                    # Check if we got work items
                    if isinstance(data, dict) and 'work_items' in data:
                        work_items = data['work_items']
                        print(f"📋 Found {len(work_items)} work items")
                        
                        # Show details of first few work items
                        for i, item in enumerate(work_items[:5]):
                            print(f"\n📄 Work Item {i+1}:")
                            print(f"   ID: {item.get('id', 'N/A')}")
                            print(f"   Title: {item.get('title', 'N/A')[:60]}")
                            print(f"   Type: {item.get('work_item_type', 'N/A')}")
                            print(f"   State: {item.get('state', 'N/A')}")
                            print(f"   Area: {item.get('area_path', 'N/A')}")
                    else:
                        print(f"📋 Response: {json.dumps(data, indent=2)[:1000]}")
                    
                    return data
                except json.JSONDecodeError:
                    print(f"✅ Workflow completed (non-JSON response)")
                    print(f"📄 Response: {response.text[:500]}")
                    return {"status": "success", "message": response.text}
            else:
                print(f"❌ Workflow failed with status {response.status_code}")
                print(f"📄 Response: {response.text[:500]}")
                return None
                
    except Exception as e:
        print(f"❌ Error calling workflow: {e}")
        return None


async def main():
    """Main function to test Azure Functions endpoints."""
    print("🚀 Testing Azure DevOps Connection via Azure Functions")
    print("=" * 60)
    
    print("ℹ️  Make sure Azure Functions are running with: func start")
    print("ℹ️  Expected URL: http://localhost:7071")
    print()
    
    # Test different endpoints
    endpoints_to_test = [
        ("Work Item Check", test_function_endpoint),
        ("Step-by-Step Workflow", call_step_by_step_workflow),
        ("Backfill Job", call_backfill_job)
    ]
    
    results = {}
    
    for name, func in endpoints_to_test:
        print(f"\n{'='*60}")
        print(f"🧪 Testing: {name}")
        print(f"{'='*60}")
        
        try:
            result = await func()
            results[name] = result
            
            if result:
                print(f"✅ {name} test completed successfully")
            else:
                print(f"❌ {name} test failed")
                
        except Exception as e:
            print(f"❌ {name} test failed with error: {e}")
            results[name] = None
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 SUMMARY")
    print(f"{'='*60}")
    
    for name, result in results.items():
        status = "✅ SUCCESS" if result else "❌ FAILED"
        print(f"{name}: {status}")
    
    # If any test succeeded, we have a working connection
    if any(results.values()):
        print(f"\n🎉 Azure DevOps connection is working!")
        print(f"💡 You can now use the Azure Functions to query work items")
    else:
        print(f"\n❌ All tests failed. Check:")
        print(f"   1. Azure Functions are running (func start)")
        print(f"   2. Configuration in local.settings.json is correct")
        print(f"   3. Azure DevOps PAT token has proper permissions")


if __name__ == "__main__":
    asyncio.run(main())
