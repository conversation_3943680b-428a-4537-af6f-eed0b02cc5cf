"""
Azure AI Search Vector Store Implementation
Provides vector search capabilities using Azure Cognitive Search.
"""

import json
import logging
from typing import List, Dict, Any, Optional
from datetime import datetime

from .base import VectorStore, VectorDoc, SearchHit, SearchFilters

logger = logging.getLogger(__name__)


class AzureSearchClient:
    """Azure AI Search implementation of VectorStore protocol."""
    
    def __init__(self, config):
        self.config = config
        self.endpoint = config.AZURE_SEARCH_ENDPOINT or f"https://{config.AZURE_SEARCH_SERVICE_NAME}.search.windows.net"
        self.key = config.AZURE_SEARCH_KEY or config.AZURE_SEARCH_ADMIN_KEY
        self.index_name = config.SEARCH_INDEX_NAME
        self._client = None
        self._search_client = None
    
    def _get_client(self):
        """Get Azure Search client (lazy initialization)."""
        if self._client is None:
            try:
                from azure.search.documents import SearchClient
                from azure.core.credentials import AzureKeyCredential
                
                credential = AzureKeyCredential(self.key)
                self._search_client = SearchClient(
                    endpoint=self.endpoint,
                    index_name=self.index_name,
                    credential=credential
                )
                
                # Also get admin client for index management
                from azure.search.documents.indexes import SearchIndexClient
                self._client = SearchIndexClient(
                    endpoint=self.endpoint,
                    credential=credential
                )
                
            except ImportError:
                raise ImportError("azure-search-documents package required for Azure Search")
        
        return self._search_client
    
    def _get_admin_client(self):
        """Get Azure Search admin client."""
        if self._client is None:
            self._get_client()  # Initialize both clients
        return self._client
    
    async def upsert(self, doc: VectorDoc) -> None:
        """Insert or update a document."""
        client = self._get_client()
        
        # Convert to Azure Search document format
        search_doc = {
            "id": doc.id,
            "text": doc.text,
            "vector": doc.vector,
            **doc.metadata
        }
        
        try:
            result = await client.upload_documents([search_doc])
            if not result[0].succeeded:
                raise Exception(f"Failed to upsert document: {result[0].error_message}")
                
            logger.debug(f"Upserted document {doc.id} to Azure Search")
            
        except Exception as e:
            logger.error(f"Error upserting document {doc.id}: {e}")
            raise
    
    async def upsert_batch(self, docs: List[VectorDoc]) -> None:
        """Insert or update multiple documents in batch."""
        if not docs:
            return
        
        client = self._get_client()
        
        # Convert to Azure Search document format
        search_docs = []
        for doc in docs:
            search_doc = {
                "id": doc.id,
                "text": doc.text,
                "vector": doc.vector,
                **doc.metadata
            }
            search_docs.append(search_doc)
        
        try:
            results = await client.upload_documents(search_docs)
            
            # Check for failures
            failed_docs = [r for r in results if not r.succeeded]
            if failed_docs:
                error_messages = [f"{r.key}: {r.error_message}" for r in failed_docs]
                logger.warning(f"Failed to upsert {len(failed_docs)} documents: {error_messages}")
            
            logger.info(f"Upserted {len(docs) - len(failed_docs)}/{len(docs)} documents to Azure Search")
            
        except Exception as e:
            logger.error(f"Error upserting batch of {len(docs)} documents: {e}")
            raise
    
    async def search(
        self, 
        query_vector: List[float], 
        k: int = 10,
        filters: Optional[SearchFilters] = None
    ) -> List[SearchHit]:
        """Search using vector similarity."""
        client = self._get_client()
        
        try:
            # Build search parameters
            search_params = {
                "search_text": "*",  # Match all for pure vector search
                "vector_queries": [{
                    "vector": query_vector,
                    "k_nearest_neighbors": k,
                    "fields": "vector"
                }],
                "select": ["id", "text", "work_item_id", "title", "assigned_to", "state", "priority", "created_date"],
                "top": k
            }
            
            # Add filters if provided
            if filters:
                filter_expr = self._build_filter_expression(filters)
                if filter_expr:
                    search_params["filter"] = filter_expr
            
            # Execute search
            results = await client.search(**search_params)
            
            # Convert to SearchHit objects
            hits = []
            async for result in results:
                hit = SearchHit(
                    id=result["id"],
                    text=result.get("text", ""),
                    score=result.get("@search.score", 0.0),
                    metadata={
                        "work_item_id": result.get("work_item_id"),
                        "title": result.get("title", ""),
                        "assigned_to": result.get("assigned_to", ""),
                        "state": result.get("state", ""),
                        "priority": result.get("priority", 3),
                        "created_date": result.get("created_date")
                    }
                )
                hits.append(hit)
            
            logger.debug(f"Vector search returned {len(hits)} results")
            return hits
            
        except Exception as e:
            logger.error(f"Error in vector search: {e}")
            raise
    
    async def search_hybrid(
        self,
        query_text: str,
        query_vector: List[float],
        k: int = 10,
        filters: Optional[SearchFilters] = None,
        alpha: float = 0.5
    ) -> List[SearchHit]:
        """Hybrid search combining text and vector similarity."""
        client = self._get_client()
        
        try:
            # Build hybrid search parameters
            search_params = {
                "search_text": query_text,
                "vector_queries": [{
                    "vector": query_vector,
                    "k_nearest_neighbors": k,
                    "fields": "vector"
                }],
                "select": ["id", "text", "work_item_id", "title", "assigned_to", "state", "priority", "created_date"],
                "top": k,
                "query_type": "semantic" if hasattr(client, 'semantic_search') else "simple"
            }
            
            # Add filters if provided
            if filters:
                filter_expr = self._build_filter_expression(filters)
                if filter_expr:
                    search_params["filter"] = filter_expr
            
            # Execute hybrid search
            results = await client.search(**search_params)
            
            # Convert to SearchHit objects
            hits = []
            async for result in results:
                # Azure Search automatically combines text and vector scores
                hit = SearchHit(
                    id=result["id"],
                    text=result.get("text", ""),
                    score=result.get("@search.score", 0.0),
                    metadata={
                        "work_item_id": result.get("work_item_id"),
                        "title": result.get("title", ""),
                        "assigned_to": result.get("assigned_to", ""),
                        "state": result.get("state", ""),
                        "priority": result.get("priority", 3),
                        "created_date": result.get("created_date")
                    }
                )
                hits.append(hit)
            
            logger.debug(f"Hybrid search returned {len(hits)} results")
            return hits
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            raise
    
    def _build_filter_expression(self, filters: SearchFilters) -> str:
        """Build OData filter expression from SearchFilters."""
        filter_parts = []
        
        if filters.project:
            filter_parts.append(f"project eq '{filters.project}'")
        
        if filters.work_item_type:
            filter_parts.append(f"work_item_type eq '{filters.work_item_type}'")
        
        if filters.state:
            state_filters = " or ".join([f"state eq '{state}'" for state in filters.state])
            filter_parts.append(f"({state_filters})")
        
        if filters.assigned_to:
            filter_parts.append(f"assigned_to eq '{filters.assigned_to}'")
        
        if filters.priority:
            priority_filters = " or ".join([f"priority eq {p}" for p in filters.priority])
            filter_parts.append(f"({priority_filters})")
        
        if filters.created_after:
            filter_parts.append(f"created_date ge {filters.created_after.isoformat()}")
        
        if filters.created_before:
            filter_parts.append(f"created_date le {filters.created_before.isoformat()}")
        
        if filters.exclude_ids:
            exclude_filters = " and ".join([f"id ne '{doc_id}'" for doc_id in filters.exclude_ids])
            filter_parts.append(f"({exclude_filters})")
        
        return " and ".join(filter_parts)
    
    async def get_by_id(self, doc_id: str) -> Optional[VectorDoc]:
        """Get document by ID."""
        client = self._get_client()
        
        try:
            result = await client.get_document(key=doc_id)
            
            if result:
                # Extract metadata (all fields except id, text, vector)
                metadata = {k: v for k, v in result.items() if k not in ["id", "text", "vector"]}
                
                return VectorDoc(
                    id=result["id"],
                    text=result.get("text", ""),
                    vector=result.get("vector", []),
                    metadata=metadata
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting document {doc_id}: {e}")
            return None
    
    async def delete(self, doc_id: str) -> None:
        """Delete document by ID."""
        client = self._get_client()
        
        try:
            result = await client.delete_documents([{"id": doc_id}])
            if not result[0].succeeded:
                raise Exception(f"Failed to delete document: {result[0].error_message}")
                
            logger.debug(f"Deleted document {doc_id} from Azure Search")
            
        except Exception as e:
            logger.error(f"Error deleting document {doc_id}: {e}")
            raise
    
    async def delete_batch(self, doc_ids: List[str]) -> None:
        """Delete multiple documents by ID."""
        if not doc_ids:
            return
        
        client = self._get_client()
        
        try:
            docs_to_delete = [{"id": doc_id} for doc_id in doc_ids]
            results = await client.delete_documents(docs_to_delete)
            
            # Check for failures
            failed_deletes = [r for r in results if not r.succeeded]
            if failed_deletes:
                error_messages = [f"{r.key}: {r.error_message}" for r in failed_deletes]
                logger.warning(f"Failed to delete {len(failed_deletes)} documents: {error_messages}")
            
            logger.info(f"Deleted {len(doc_ids) - len(failed_deletes)}/{len(doc_ids)} documents from Azure Search")
            
        except Exception as e:
            logger.error(f"Error deleting batch of {len(doc_ids)} documents: {e}")
            raise
    
    async def count(self, filters: Optional[SearchFilters] = None) -> int:
        """Count documents matching filters."""
        client = self._get_client()
        
        try:
            search_params = {
                "search_text": "*",
                "include_total_count": True,
                "top": 0  # Don't return documents, just count
            }
            
            # Add filters if provided
            if filters:
                filter_expr = self._build_filter_expression(filters)
                if filter_expr:
                    search_params["filter"] = filter_expr
            
            results = await client.search(**search_params)
            return results.get_count()
            
        except Exception as e:
            logger.error(f"Error counting documents: {e}")
            return 0
    
    async def initialize(self) -> None:
        """Initialize the Azure Search index."""
        admin_client = self._get_admin_client()
        
        try:
            # Check if index exists
            try:
                await admin_client.get_index(self.index_name)
                logger.info(f"Azure Search index '{self.index_name}' already exists")
                return
            except Exception:
                # Index doesn't exist, create it
                pass
            
            # Define index schema
            from azure.search.documents.indexes.models import (
                SearchIndex, SimpleField, SearchableField, VectorSearch,
                VectorSearchProfile, HnswAlgorithmConfiguration
            )
            
            # Create index with vector search configuration
            fields = [
                SimpleField(name="id", type="Edm.String", key=True),
                SearchableField(name="text", type="Edm.String"),
                SimpleField(name="vector", type="Collection(Edm.Single)", searchable=True, vector_search_dimensions=1536),
                SimpleField(name="work_item_id", type="Edm.Int32", filterable=True),
                SearchableField(name="title", type="Edm.String"),
                SimpleField(name="assigned_to", type="Edm.String", filterable=True),
                SimpleField(name="state", type="Edm.String", filterable=True),
                SimpleField(name="priority", type="Edm.Int32", filterable=True),
                SimpleField(name="created_date", type="Edm.DateTimeOffset", filterable=True),
                SimpleField(name="project", type="Edm.String", filterable=True),
                SimpleField(name="work_item_type", type="Edm.String", filterable=True)
            ]
            
            # Vector search configuration
            vector_search = VectorSearch(
                profiles=[
                    VectorSearchProfile(
                        name="default-vector-profile",
                        algorithm_configuration_name="default-hnsw-config"
                    )
                ],
                algorithms=[
                    HnswAlgorithmConfiguration(
                        name="default-hnsw-config"
                    )
                ]
            )
            
            index = SearchIndex(
                name=self.index_name,
                fields=fields,
                vector_search=vector_search
            )
            
            # Create the index
            await admin_client.create_index(index)
            logger.info(f"Created Azure Search index '{self.index_name}'")
            
        except Exception as e:
            logger.error(f"Error initializing Azure Search index: {e}")
            raise
    
    async def health_check(self) -> Dict[str, Any]:
        """Check the health of Azure Search."""
        try:
            admin_client = self._get_admin_client()
            
            # Check if index exists and get stats
            index = await admin_client.get_index(self.index_name)
            
            # Get document count
            doc_count = await self.count()
            
            return {
                "status": "healthy",
                "backend": "azure_search",
                "index_name": self.index_name,
                "endpoint": self.endpoint,
                "document_count": doc_count,
                "index_exists": True
            }
            
        except Exception as e:
            return {
                "status": "unhealthy",
                "backend": "azure_search",
                "error": str(e),
                "index_name": self.index_name,
                "endpoint": self.endpoint
            }
