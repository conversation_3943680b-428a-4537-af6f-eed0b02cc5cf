#!/usr/bin/env python3
"""
Simple Azure DevOps test script that loads configuration properly.
"""

import asyncio
import sys
import os
import json
from datetime import datetime
from typing import List, Dict, Any

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def test_ado_connection():
    """Test basic ADO connection and query."""
    try:
        print("\n🔗 Testing Azure DevOps Connection...")

        # Get configuration
        config = get_config()
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        print(f"   PAT Token: {'SET' if config.ADO_PAT_TOKEN else 'NOT SET'}")

        if not config.ADO_ORGANIZATION or not config.ADO_PROJECT or not config.ADO_PAT_TOKEN:
            print("❌ Missing required ADO configuration")
            return False

        # Create ADO client
        ado_client = AdoClient(config)

        # First, try to get a specific work item to test basic connectivity
        print("\n🔍 Testing basic connectivity by trying to get work item 1...")
        try:
            test_item = await ado_client.get_work_item(1)
            if test_item:
                print("✅ Successfully retrieved work item 1 - connection is working!")
            else:
                print("⚠️ Work item 1 not found, but connection seems to work")
        except Exception as e:
            print(f"❌ Failed to get work item 1: {e}")
            # Continue with WIQL tests anyway

        # Test different project name formats
        project_variations = [
            config.ADO_PROJECT,
            config.ADO_PROJECT.replace(' ', '%20'),
            config.ADO_PROJECT.replace(' ', '+'),
            'Air4%20Channels%20Testing'
        ]

        print(f"\n🔍 Testing different project name formats...")
        for project_name in project_variations:
            print(f"   Trying project: '{project_name}'")

            # Create a new client with the modified project name
            test_config = get_config()
            test_config.ADO_PROJECT = project_name
            test_client = AdoClient(test_config)

            try:
                test_item = await test_client.get_work_item(1)
                print(f"   ✅ Project format '{project_name}' works!")
                ado_client = test_client  # Use this client for further tests
                break
            except Exception as e:
                print(f"   ❌ Project format '{project_name}' failed: {e}")
                continue
        
        # Try to get work items using direct REST API instead of WIQL
        print("\n🔍 Trying direct REST API approach...")

        try:
            # Use the client's HTTP client to make direct API calls
            url = f"{ado_client.base_url}/{ado_client.project}/_apis/wit/workitems"
            params = {
                "api-version": "7.0",
                "$top": 50,
                "fields": "System.Id,System.Title,System.WorkItemType,System.State,System.AreaPath"
            }

            print(f"   Calling: {url}")
            response = await ado_client.client.get(url, params=params)

            if response.status_code == 200:
                data = response.json()
                work_items_data = data.get('value', [])
                print(f"   ✅ Direct API call succeeded! Found {len(work_items_data)} work items")

                # Convert to the format expected by our code
                work_items = []
                for item_data in work_items_data:
                    work_items.append({
                        'id': item_data.get('id'),
                        'fields': item_data.get('fields', {})
                    })

            else:
                print(f"   ❌ Direct API call failed: {response.status_code}")
                work_items = None

        except Exception as e:
            print(f"   ❌ Direct API call error: {e}")
            work_items = None

        # If direct API didn't work, try a very simple WIQL query
        if not work_items:
            print("\n🔍 Trying simplified WIQL query...")
            try:
                simple_query = "SELECT [System.Id] FROM WorkItems"
                work_items = await ado_client.query_work_items(simple_query)
                if work_items:
                    print(f"   ✅ Simple WIQL query worked! Found {len(work_items)} items")
                else:
                    print(f"   ⚠️ Simple WIQL query returned no results")
            except Exception as e:
                print(f"   ❌ Simple WIQL query failed: {e}")
                work_items = None
        
        work_items = await ado_client.query_work_items(wiql_query)
        
        if work_items:
            print(f"✅ Successfully retrieved {len(work_items)} work items")
            
            # Show first few work items
            print(f"\n📋 First 10 work items:")
            print(f"{'ID':<8} {'Type':<12} {'State':<12} {'Area Path':<30} {'Title'}")
            print("-" * 100)
            
            for item in work_items[:10]:
                fields = item.get('fields', {})
                work_item_id = item.get('id', 'N/A')
                title = fields.get('System.Title', 'No Title')[:30]
                state = fields.get('System.State', 'N/A')
                work_type = fields.get('System.WorkItemType', 'N/A')
                area_path = fields.get('System.AreaPath', 'N/A')[:30]
                
                print(f"{work_item_id:<8} {work_type:<12} {state:<12} {area_path:<30} {title}")
            
            # Get unique area paths
            area_paths = set()
            work_types = set()
            for item in work_items:
                fields = item.get('fields', {})
                area_path = fields.get('System.AreaPath', '')
                work_type = fields.get('System.WorkItemType', '')
                if area_path:
                    area_paths.add(area_path)
                if work_type:
                    work_types.add(work_type)
            
            print(f"\n📍 Found {len(area_paths)} unique area paths:")
            for path in sorted(area_paths):
                print(f"   - {path}")
            
            print(f"\n🏷️  Found {len(work_types)} work item types:")
            for wtype in sorted(work_types):
                print(f"   - {wtype}")
            
            return work_items
        else:
            print("❌ No work items found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing ADO connection: {e}")
        print(f"   Error type: {type(e).__name__}")
        return False


async def search_for_defects(work_items):
    """Search for defects in the work items."""
    if not work_items:
        return []
    
    print(f"\n🔍 Searching for Bug/Defect work items...")
    
    defects = []
    ai_testing_items = []
    
    for item in work_items:
        fields = item.get('fields', {})
        work_type = fields.get('System.WorkItemType', '').lower()
        area_path = fields.get('System.AreaPath', '').lower()
        
        # Look for bugs/defects
        if work_type in ['bug', 'defect']:
            defects.append(item)
        
        # Look for AI Testing related items
        if 'ai testing' in area_path or 'defect management' in area_path:
            ai_testing_items.append(item)
    
    print(f"🐛 Found {len(defects)} Bug/Defect work items")
    print(f"🤖 Found {len(ai_testing_items)} AI Testing related items")
    
    if defects:
        print(f"\n📋 Bug/Defect work items:")
        print(f"{'ID':<8} {'State':<12} {'Area Path':<40} {'Title'}")
        print("-" * 120)
        
        for item in defects[:20]:  # Show first 20
            fields = item.get('fields', {})
            work_item_id = item.get('id', 'N/A')
            title = fields.get('System.Title', 'No Title')[:40]
            state = fields.get('System.State', 'N/A')
            area_path = fields.get('System.AreaPath', 'N/A')[:40]
            
            print(f"{work_item_id:<8} {state:<12} {area_path:<40} {title}")
    
    if ai_testing_items:
        print(f"\n📋 AI Testing related work items:")
        print(f"{'ID':<8} {'Type':<12} {'State':<12} {'Title'}")
        print("-" * 80)
        
        for item in ai_testing_items[:20]:  # Show first 20
            fields = item.get('fields', {})
            work_item_id = item.get('id', 'N/A')
            title = fields.get('System.Title', 'No Title')[:40]
            state = fields.get('System.State', 'N/A')
            work_type = fields.get('System.WorkItemType', 'N/A')
            
            print(f"{work_item_id:<8} {work_type:<12} {state:<12} {title}")
    
    return defects


async def main():
    """Main function."""
    print("🚀 Simple Azure DevOps Connection Test")
    print("=" * 60)
    
    # Test connection and get work items
    work_items = await test_ado_connection()
    
    if work_items:
        # Search for defects
        defects = await search_for_defects(work_items)
        
        print(f"\n📊 SUMMARY")
        print("=" * 60)
        print(f"Total work items: {len(work_items)}")
        print(f"Bug/Defect items: {len(defects)}")
        
        if defects:
            print(f"\n🎯 You can use these defect IDs for testing:")
            for item in defects[:5]:
                work_item_id = item.get('id')
                title = item.get('fields', {}).get('System.Title', '')[:50]
                print(f"   ID {work_item_id}: {title}")
        
        print(f"\n✅ Azure DevOps connection is working!")
    else:
        print(f"\n❌ Failed to connect to Azure DevOps")


if __name__ == "__main__":
    asyncio.run(main())
