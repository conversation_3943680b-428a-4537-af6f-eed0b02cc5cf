"""
Azure Function for Team Notifications
Scheduled function that analyzes latest work items and sends notifications to the team.
"""

import azure.functions as func
import logging
import json
import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional

from .common.adapters.ado_client import AdoClient
from .common.adapters.vector_storage_factory import VectorStorageFactory
from .common.ai.embeddings import EmbeddingService
from .common.utils.config import get_config
from .common.utils.logging import log_structured

# Global clients (initialized once)
_ado_client = None
_vector_storage = None
_embedding_service = None


async def get_clients():
    """Get or initialize global clients."""
    global _ado_client, _vector_storage, _embedding_service
    
    if not _ado_client:
        config = get_config()
        
        # Initialize ADO client
        _ado_client = AdoClient(
            organization=config['ADO_ORGANIZATION'],
            project=config['ADO_PROJECT'],
            pat_token=config['ADO_PAT_TOKEN']
        )
        
        # Initialize vector storage
        _vector_storage = VectorStorageFactory.create_storage(config)
        
        # Initialize embedding service
        _embedding_service = EmbeddingService(config)
    
    return {
        'ado': _ado_client,
        'vector_storage': _vector_storage,
        'embedding': _embedding_service
    }


class TeamNotificationService:
    """Service for generating and sending team notifications."""
    
    def __init__(self, ado_client: AdoClient, vector_storage, embedding_service: EmbeddingService):
        self.ado_client = ado_client
        self.vector_storage = vector_storage
        self.embedding_service = embedding_service
        self.config = get_config()

    async def get_safety_work_item(self) -> Dict[str, Any]:
        """Get safety work item if configured for testing."""
        if not self.config.SAFETY_ONLY_WORKITEM_ID:
            return None

        try:
            work_item_id = int(self.config.SAFETY_ONLY_WORKITEM_ID)
            work_item_data = await self.ado_client.get_work_item(work_item_id)

            if not work_item_data:
                log_structured("warning", f"Safety work item {work_item_id} not found")
                return None

            fields = work_item_data.get('fields', {})

            # Convert to the format expected by team notifications
            work_item = {
                'id': work_item_data.get('id'),
                'title': fields.get('System.Title', ''),
                'work_item_type': fields.get('System.WorkItemType', ''),
                'state': fields.get('System.State', ''),
                'priority': fields.get('Microsoft.VSTS.Common.Priority', 2),
                'severity': fields.get('Microsoft.VSTS.Common.Severity', ''),
                'assigned_to': fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned'),
                'assigned_to_email': fields.get('System.AssignedTo', {}).get('uniqueName', ''),
                'created_date': fields.get('System.CreatedDate', ''),
                'changed_date': fields.get('System.ChangedDate', ''),
                'created_by': fields.get('System.CreatedBy', {}).get('displayName', ''),
                'area_path': fields.get('System.AreaPath', ''),
                'iteration_path': fields.get('System.IterationPath', ''),
                'description': fields.get('System.Description', ''),
                'tags': fields.get('System.Tags', ''),
                'url': f"https://dev.azure.com/{self.config.ADO_ORGANIZATION}/{self.config.ADO_PROJECT}/_workitems/edit/{work_item_id}"
            }

            log_structured("info", f"Retrieved safety work item {work_item_id}: {work_item['title']}")
            return work_item

        except Exception as e:
            log_structured("error", f"Failed to get safety work item: {e}")
            return None

    async def get_latest_work_items(self, days_back: int = 1) -> List[Dict[str, Any]]:
        """Get work items created/updated in the last N days."""
        try:
            # Calculate date range
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days_back)
            
            # Query for recent work items
            wiql = f"""
            SELECT [System.Id], [System.Title], [System.WorkItemType], [System.State], 
                   [System.AssignedTo], [System.CreatedDate], [System.ChangedDate],
                   [Microsoft.VSTS.Common.Priority], [Microsoft.VSTS.Common.Severity],
                   [System.Description], [Microsoft.VSTS.TCM.ReproSteps]
            FROM WorkItems 
            WHERE [System.TeamProject] = @project
            AND [System.CreatedDate] >= '{start_date.isoformat()}'
            AND [System.WorkItemType] IN ('Bug', 'Task', 'User Story', 'Feature')
            ORDER BY [System.CreatedDate] DESC
            """
            
            work_items = await self.ado_client.query_work_items(wiql)
            log_structured("info", f"Found {len(work_items)} work items in last {days_back} days")
            
            return work_items
            
        except Exception as e:
            log_structured("error", f"Failed to get latest work items: {e}")
            return []
    
    async def find_similar_items(self, work_item: Dict[str, Any], limit: int = 5) -> List[Dict[str, Any]]:
        """Find similar work items using vector search."""
        try:
            # Create search text from title and description
            title = work_item.get('title', '')
            description = work_item.get('description', '')
            search_text = f"{title} {description}".strip()
            
            if not search_text:
                return []
            
            # Generate embedding for search
            embedding = await self.embedding_service.get_embedding(search_text)
            
            # Search for similar items
            similar_items = await self.vector_storage.search_similar(
                embedding=embedding,
                limit=limit,
                threshold=0.7
            )
            
            return similar_items
            
        except Exception as e:
            log_structured("warning", f"Failed to find similar items for work item {work_item.get('id')}: {e}")
            return []
    
    def suggest_assignee(self, work_item: Dict[str, Any], similar_items: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Suggest assignee based on similar items."""
        try:
            if not similar_items:
                return {'suggested_assignee': None, 'confidence': 0.0, 'reason': 'No similar items found'}
            
            # Count assignees from similar items
            assignee_counts = {}
            for item in similar_items:
                assignee = item.get('assigned_to')
                if assignee and assignee != 'Unassigned':
                    assignee_counts[assignee] = assignee_counts.get(assignee, 0) + 1
            
            if not assignee_counts:
                return {'suggested_assignee': None, 'confidence': 0.0, 'reason': 'No assigned similar items'}
            
            # Get most common assignee
            suggested_assignee = max(assignee_counts, key=assignee_counts.get)
            confidence = assignee_counts[suggested_assignee] / len(similar_items)
            
            return {
                'suggested_assignee': suggested_assignee,
                'confidence': confidence,
                'reason': f'Assigned to {assignee_counts[suggested_assignee]} of {len(similar_items)} similar items'
            }
            
        except Exception as e:
            log_structured("warning", f"Failed to suggest assignee: {e}")
            return {'suggested_assignee': None, 'confidence': 0.0, 'reason': f'Error: {e}'}
    
    def extract_rca_info(self, work_item: Dict[str, Any]) -> Dict[str, Any]:
        """Extract RCA information from work item."""
        try:
            description = work_item.get('description', '').lower()
            title = work_item.get('title', '').lower()
            
            # Look for RCA keywords
            rca_keywords = ['root cause', 'rca', 'cause analysis', 'investigation', 'resolution']
            has_rca = any(keyword in description or keyword in title for keyword in rca_keywords)
            
            # Look for resolution patterns
            resolution_keywords = ['fixed', 'resolved', 'solution', 'workaround', 'patch']
            has_resolution = any(keyword in description for keyword in resolution_keywords)
            
            return {
                'has_rca': has_rca,
                'has_resolution': has_resolution,
                'rca_keywords_found': [kw for kw in rca_keywords if kw in description or kw in title],
                'resolution_keywords_found': [kw for kw in resolution_keywords if kw in description]
            }
            
        except Exception as e:
            log_structured("warning", f"Failed to extract RCA info: {e}")
            return {'has_rca': False, 'has_resolution': False}
    
    async def generate_teams_message(self, work_items: List[Dict[str, Any]], analysis_results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Generate Teams adaptive card message."""
        if not work_items:
            return {
                "type": "message",
                "text": "📭 No new work items found in the last 24 hours."
            }

        # Check if this is a single work item (safety mode or specific item)
        is_single_item = len(work_items) == 1

        if is_single_item:
            # Special handling for single work item
            item = work_items[0]
            analysis = analysis_results[0] if analysis_results else {}

            card = {
                "type": "message",
                "attachments": [
                    {
                        "contentType": "application/vnd.microsoft.card.adaptive",
                        "content": {
                            "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                            "type": "AdaptiveCard",
                            "version": "1.3",
                            "body": [
                                {
                                    "type": "TextBlock",
                                    "text": f"🚨 Work Item Alert - {item.get('id', 'Unknown')}",
                                    "weight": "Bolder",
                                    "size": "Large",
                                    "color": "Attention"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": f"**{item.get('title', 'No Title')}**",
                                    "wrap": True,
                                    "size": "Medium",
                                    "weight": "Bolder"
                                },
                                {
                                    "type": "FactSet",
                                    "facts": [
                                        {"title": "Work Item ID", "value": str(item.get('id', 'N/A'))},
                                        {"title": "Type", "value": item.get('work_item_type', 'N/A')},
                                        {"title": "State", "value": item.get('state', 'N/A')},
                                        {"title": "Priority", "value": str(item.get('priority', 'N/A'))},
                                        {"title": "Severity", "value": item.get('severity', 'N/A')},
                                        {"title": "Assigned To", "value": item.get('assigned_to', 'Unassigned')},
                                        {"title": "Iteration", "value": item.get('iteration_path', 'N/A')},
                                        {"title": "Area", "value": item.get('area_path', 'N/A')}
                                    ]
                                },
                                {
                                    "type": "TextBlock",
                                    "text": "**Assignment Recommendation:**",
                                    "weight": "Bolder",
                                    "spacing": "Medium"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": analysis.get('assignment_suggestion', {}).get('recommendation', 'No specific recommendation available'),
                                    "wrap": True,
                                    "color": "Good"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": "**Similar Historical Items:**",
                                    "weight": "Bolder",
                                    "spacing": "Medium"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": f"Found {len(analysis.get('similar_items', []))} similar items in history",
                                    "wrap": True
                                },
                                {
                                    "type": "TextBlock",
                                    "text": "**RCA & Resolution Patterns:**",
                                    "weight": "Bolder",
                                    "spacing": "Medium"
                                },
                                {
                                    "type": "TextBlock",
                                    "text": analysis.get('rca_info', {}).get('summary', 'No RCA patterns identified'),
                                    "wrap": True
                                }
                            ],
                            "actions": [
                                {
                                    "type": "Action.OpenUrl",
                                    "title": "🔗 View Work Item",
                                    "url": item.get('url', f"https://dev.azure.com/{self.config.ADO_ORGANIZATION}/{self.config.ADO_PROJECT}/_workitems/edit/{item.get('id', '')}")
                                },
                                {
                                    "type": "Action.OpenUrl",
                                    "title": "📊 View Project Board",
                                    "url": f"https://dev.azure.com/{self.config.ADO_ORGANIZATION}/{self.config.ADO_PROJECT}/_boards/board"
                                }
                            ]
                        }
                    }
                ]
            }

            return card

        # Original logic for multiple work items
        # Create summary
        priority_counts = {}
        for item in work_items:
            priority = item.get('priority', 2)
            priority_counts[priority] = priority_counts.get(priority, 0) + 1

        # Count unassigned items
        unassigned_count = sum(1 for item in work_items if item.get('assigned_to') in [None, '', 'Unassigned'])

        # Create adaptive card
        card = {
            "type": "message",
            "attachments": [
                {
                    "contentType": "application/vnd.microsoft.card.adaptive",
                    "content": {
                        "$schema": "http://adaptivecards.io/schemas/adaptive-card.json",
                        "type": "AdaptiveCard",
                        "version": "1.3",
                        "body": [
                            {
                                "type": "TextBlock",
                                "text": "🔍 Daily Work Item Analysis",
                                "weight": "Bolder",
                                "size": "Large"
                            },
                            {
                                "type": "TextBlock",
                                "text": f"Found {len(work_items)} new/updated work items in the last 24 hours",
                                "wrap": True
                            },
                            {
                                "type": "FactSet",
                                "facts": [
                                    {"title": "Total Items", "value": str(len(work_items))},
                                    {"title": "Priority 1", "value": str(priority_counts.get(1, 0))},
                                    {"title": "Priority 2", "value": str(priority_counts.get(2, 0))},
                                    {"title": "Unassigned", "value": str(unassigned_count)}
                                ]
                            }
                        ]
                    }
                }
            ]
        }
        
        # Add work item details
        for i, result in enumerate(analysis_results[:5]):  # Limit to 5 items
            item = result['work_item']
            similar_items = result['similar_items']
            assignment = result['assignment_suggestion']
            
            item_section = {
                "type": "Container",
                "separator": True,
                "items": [
                    {
                        "type": "TextBlock",
                        "text": f"**{item.get('title', 'No Title')}**",
                        "weight": "Bolder",
                        "wrap": True
                    },
                    {
                        "type": "FactSet",
                        "facts": [
                            {"title": "ID", "value": str(item.get('id', 'N/A'))},
                            {"title": "Type", "value": item.get('work_item_type', 'N/A')},
                            {"title": "Priority", "value": str(item.get('priority', 'N/A'))},
                            {"title": "Assigned To", "value": item.get('assigned_to', 'Unassigned')},
                            {"title": "Similar Items", "value": str(len(similar_items))},
                            {"title": "Suggested Assignee", "value": assignment.get('suggested_assignee', 'None')}
                        ]
                    }
                ]
            }
            
            card["attachments"][0]["content"]["body"].append(item_section)
        
        return card

    async def send_teams_notification(self, message: Dict[str, Any]) -> bool:
        """Send notification to Microsoft Teams."""
        # Try Teams Logic App first, then webhook
        teams_logic_app_url = getattr(self.config, 'TEAMS_LOGIC_APP_URL', None)
        teams_webhook_url = getattr(self.config, 'TEAMS_WEBHOOK_URL', None)

        if not teams_logic_app_url and not teams_webhook_url:
            log_structured("warning", "Neither Teams Logic App URL nor webhook URL configured")
            return False

        try:
            import aiohttp

            # Try Logic App first if available
            if teams_logic_app_url:
                # Convert adaptive card to Logic App format
                # Extract work item info from message if available
                work_item_id = "Unknown"
                subject_text = "Work Item Alert"
                if hasattr(message, 'get') and 'attachments' in message:
                    # Try to extract work item ID from the adaptive card
                    try:
                        card_content = message['attachments'][0]['content']
                        for body_item in card_content.get('body', []):
                            if body_item.get('type') == 'FactSet':
                                for fact in body_item.get('facts', []):
                                    if fact.get('title') == 'Work Item ID':
                                        work_item_id = fact.get('value', 'Unknown')
                                        subject_text = f"Work Item Alert - {work_item_id}"
                                        break
                    except (KeyError, IndexError, TypeError):
                        pass

                logic_app_payload = {
                    "To": "<EMAIL>",
                    "Subject": f"🚨 {subject_text}",
                    "Body": f"AI Triage notification for work item {work_item_id}",
                    "work_item_id": work_item_id,
                    "adaptive_card": message,
                    "Attachments": "",
                    "attachmentName": ""
                }

                async with aiohttp.ClientSession() as session:
                    async with session.post(teams_logic_app_url, json=logic_app_payload) as response:
                        if response.status == 200:
                            log_structured("info", "Teams notification sent successfully via Logic App")
                            return True
                        else:
                            log_structured("warning", f"Teams Logic App failed: {response.status}, trying webhook")

            # Fallback to webhook if Logic App failed or not available
            if teams_webhook_url:
                async with aiohttp.ClientSession() as session:
                    async with session.post(teams_webhook_url, json=message) as response:
                        if response.status == 200:
                            log_structured("info", "Teams notification sent successfully via webhook")
                            return True
                        else:
                            log_structured("error", f"Teams webhook failed: {response.status}")
                            return False

            log_structured("error", "All Teams notification methods failed")
            return False

        except Exception as e:
            log_structured("error", f"Failed to send Teams notification: {e}")
            return False

    async def process_team_notifications(self, days_back: int = 1) -> Dict[str, Any]:
        """Main process for team notifications."""
        try:
            # Check if safety work item is configured for testing
            safety_work_item = await self.get_safety_work_item()

            if safety_work_item:
                work_items = [safety_work_item]
                log_structured("info", f"Using safety work item {safety_work_item['id']} for team notification")
            else:
                # Normal behavior: get recent work items
                work_items = await self.get_latest_work_items(days_back)
                if not work_items:
                    log_structured("info", "No new work items found")
                    return {"status": "success", "message": "No new work items", "items_processed": 0}

            log_structured("info", f"Processing {len(work_items)} work items")

            # Analyze each work item
            analysis_results = []
            for item in work_items:
                try:
                    # Find similar items
                    similar_items = await self.find_similar_items(item)

                    # Get assignment suggestion
                    assignment_suggestion = self.suggest_assignee(item, similar_items)

                    # Extract RCA info
                    rca_info = self.extract_rca_info(item)

                    analysis_results.append({
                        'work_item': item,
                        'similar_items': similar_items,
                        'assignment_suggestion': assignment_suggestion,
                        'rca_info': rca_info
                    })

                except Exception as e:
                    log_structured("warning", f"Failed to analyze work item {item.get('id')}: {e}")
                    analysis_results.append({
                        'work_item': item,
                        'similar_items': [],
                        'assignment_suggestion': {'suggested_assignee': None, 'confidence': 0.0},
                        'rca_info': {'has_rca': False}
                    })

            # Generate and send Teams notification
            teams_message = await self.generate_teams_message(work_items, analysis_results)
            teams_sent = await self.send_teams_notification(teams_message)

            # Log summary
            priority_1_count = sum(1 for item in work_items if item.get('priority') == 1)
            unassigned_count = sum(1 for item in work_items if item.get('assigned_to') in [None, '', 'Unassigned'])

            log_structured("info", "Team notification completed",
                         items_processed=len(work_items),
                         priority_1_items=priority_1_count,
                         unassigned_items=unassigned_count,
                         teams_notification_sent=teams_sent)

            return {
                "status": "success",
                "items_processed": len(work_items),
                "priority_1_items": priority_1_count,
                "unassigned_items": unassigned_count,
                "teams_notification_sent": teams_sent,
                "analysis_results": analysis_results
            }

        except Exception as e:
            log_structured("error", f"Team notification process failed: {e}")
            return {"status": "error", "message": str(e)}


# Azure Function entry point
async def main(mytimer: func.TimerRequest) -> None:
    """
    Azure Function triggered by timer (daily at 9 AM UTC).
    Timer schedule: "0 0 9 * * *" (daily at 9 AM UTC)
    """
    utc_timestamp = datetime.utcnow().replace(tzinfo=None).isoformat()

    if mytimer.past_due:
        log_structured("warning", "Timer function is running late")

    log_structured("info", f"Team notification function started at {utc_timestamp}")

    try:
        # Get clients
        clients = await get_clients()

        # Initialize notification service
        notification_service = TeamNotificationService(
            clients['ado'],
            clients['vector_storage'],
            clients['embedding']
        )

        # Process notifications
        result = await notification_service.process_team_notifications(days_back=1)

        log_structured("info", "Team notification function completed", result=result)

    except Exception as e:
        log_structured("error", f"Team notification function failed: {e}")
        raise
