#!/usr/bin/env python3
"""
Get all defects from Azure DevOps using a working approach.
"""

import asyncio
import sys
import os
import json
import urllib.parse
from datetime import datetime
from typing import List, Dict, Any

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def get_work_items_by_ids(ado_client, start_id=1, count=100):
    """Get work items by trying sequential IDs."""
    print(f"\n🔍 Trying to get work items by sequential IDs (starting from {start_id})...")
    
    work_items = []
    found_count = 0
    
    for work_item_id in range(start_id, start_id + count):
        try:
            item = await ado_client.get_work_item(work_item_id)
            if item:
                work_items.append(item)
                found_count += 1
                if found_count % 10 == 0:
                    print(f"   Found {found_count} work items so far...")
        except Exception:
            # Work item doesn't exist, continue
            continue
    
    print(f"✅ Found {len(work_items)} work items by ID scanning")
    return work_items


async def try_different_wiql_approaches(ado_client):
    """Try different WIQL query approaches."""
    print(f"\n🔍 Trying different WIQL approaches...")
    
    # Get the config for project name
    config = get_config()
    
    queries_to_try = [
        # Query 1: Minimal query
        {
            "name": "Minimal query",
            "query": "SELECT [System.Id] FROM WorkItems"
        },
        # Query 2: With TOP clause
        {
            "name": "With TOP clause", 
            "query": "SELECT TOP 50 [System.Id], [System.Title] FROM WorkItems"
        },
        # Query 3: With WHERE clause for work item type
        {
            "name": "Filter by Bug type",
            "query": "SELECT [System.Id], [System.Title] FROM WorkItems WHERE [System.WorkItemType] = 'Bug'"
        },
        # Query 4: Using ASOF for current date
        {
            "name": "With ASOF clause",
            "query": "SELECT [System.Id], [System.Title] FROM WorkItems ASOF '2025-10-15'"
        }
    ]
    
    for query_info in queries_to_try:
        try:
            print(f"   Trying: {query_info['name']}")
            work_items = await ado_client.query_work_items(query_info['query'])
            if work_items:
                print(f"   ✅ {query_info['name']} succeeded! Found {len(work_items)} items")
                return work_items
            else:
                print(f"   ⚠️ {query_info['name']} returned no results")
        except Exception as e:
            print(f"   ❌ {query_info['name']} failed: {str(e)[:100]}")
            continue
    
    return None


async def main():
    """Main function to get all defects."""
    print("🚀 Getting All Defects from Azure DevOps")
    print("=" * 60)
    
    try:
        # Get configuration and create client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        ado_client = AdoClient(config)
        
        # Test basic connectivity
        print(f"\n🔍 Testing basic connectivity...")
        try:
            test_item = await ado_client.get_work_item(1)
            print("✅ Basic connectivity works")
        except Exception as e:
            print(f"⚠️ Basic connectivity test: {e}")
        
        # Try different approaches to get work items
        work_items = None
        
        # Approach 1: Try WIQL queries
        work_items = await try_different_wiql_approaches(ado_client)
        
        # Approach 2: If WIQL fails, try getting items by ID
        if not work_items:
            work_items = await get_work_items_by_ids(ado_client, start_id=1, count=200)
        
        if work_items:
            print(f"\n📊 ANALYSIS OF {len(work_items)} WORK ITEMS")
            print("=" * 60)
            
            # Analyze the work items
            work_types = {}
            states = {}
            area_paths = {}
            defects = []
            
            for item in work_items:
                fields = item.get('fields', {})
                
                # Count work types
                work_type = fields.get('System.WorkItemType', 'Unknown')
                work_types[work_type] = work_types.get(work_type, 0) + 1
                
                # Count states
                state = fields.get('System.State', 'Unknown')
                states[state] = states.get(state, 0) + 1
                
                # Count area paths
                area_path = fields.get('System.AreaPath', 'Unknown')
                area_paths[area_path] = area_paths.get(area_path, 0) + 1
                
                # Collect defects/bugs
                if work_type.lower() in ['bug', 'defect']:
                    defects.append(item)
            
            print(f"📋 Work Item Types:")
            for wtype, count in sorted(work_types.items(), key=lambda x: x[1], reverse=True):
                print(f"   {wtype}: {count}")
            
            print(f"\n📊 States:")
            for state, count in sorted(states.items(), key=lambda x: x[1], reverse=True):
                print(f"   {state}: {count}")
            
            print(f"\n📍 Area Paths:")
            for area, count in sorted(area_paths.items(), key=lambda x: x[1], reverse=True):
                print(f"   {area}: {count}")
            
            print(f"\n🐛 DEFECTS/BUGS FOUND: {len(defects)}")
            print("=" * 60)
            
            if defects:
                print(f"{'ID':<8} {'State':<12} {'Area Path':<40} {'Title'}")
                print("-" * 120)
                
                for item in defects:
                    fields = item.get('fields', {})
                    work_item_id = item.get('id', 'N/A')
                    title = fields.get('System.Title', 'No Title')[:40]
                    state = fields.get('System.State', 'N/A')
                    area_path = fields.get('System.AreaPath', 'N/A')[:40]
                    
                    print(f"{work_item_id:<8} {state:<12} {area_path:<40} {title}")
                
                # Look for AI Testing related defects
                ai_testing_defects = []
                for item in defects:
                    fields = item.get('fields', {})
                    area_path = fields.get('System.AreaPath', '').lower()
                    title = fields.get('System.Title', '').lower()
                    
                    if 'ai testing' in area_path or 'ai testing' in title or 'defect management' in area_path:
                        ai_testing_defects.append(item)
                
                if ai_testing_defects:
                    print(f"\n🤖 AI TESTING RELATED DEFECTS: {len(ai_testing_defects)}")
                    print("=" * 60)
                    
                    for item in ai_testing_defects:
                        fields = item.get('fields', {})
                        work_item_id = item.get('id', 'N/A')
                        title = fields.get('System.Title', 'No Title')
                        state = fields.get('System.State', 'N/A')
                        area_path = fields.get('System.AreaPath', 'N/A')
                        
                        print(f"\n📄 Work Item {work_item_id}:")
                        print(f"   Title: {title}")
                        print(f"   State: {state}")
                        print(f"   Area: {area_path}")
                        print(f"   URL: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{work_item_id}")
                else:
                    print(f"\n⚠️ No AI Testing specific defects found")
                    print(f"💡 All defects are listed above - you may need to check them manually")
            
            # Export results
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"all_defects_{timestamp}.json"
            
            export_data = {
                "timestamp": timestamp,
                "total_work_items": len(work_items),
                "total_defects": len(defects),
                "work_types": work_types,
                "states": states,
                "area_paths": area_paths,
                "defects": []
            }
            
            for item in defects:
                fields = item.get('fields', {})
                export_data["defects"].append({
                    "id": item.get('id'),
                    "title": fields.get('System.Title'),
                    "type": fields.get('System.WorkItemType'),
                    "state": fields.get('System.State'),
                    "area_path": fields.get('System.AreaPath'),
                    "assigned_to": fields.get('System.AssignedTo', {}).get('displayName'),
                    "created_date": fields.get('System.CreatedDate'),
                    "url": f"https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{item.get('id')}"
                })
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Exported results to: {filename}")
            print(f"✅ Successfully retrieved and analyzed {len(work_items)} work items!")
            
        else:
            print(f"\n❌ Could not retrieve any work items")
            print(f"💡 This might be due to:")
            print(f"   - WIQL queries not supported in this Azure DevOps version")
            print(f"   - Insufficient permissions")
            print(f"   - No work items in the project")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
