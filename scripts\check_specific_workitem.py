#!/usr/bin/env python3
"""
Check specific work item ID 752662
"""

import asyncio
import sys
import os
import json
from datetime import datetime

# Load environment variables from local.settings.json
def load_environment():
    """Load environment variables from local.settings.json"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    settings_path = os.path.join(script_dir, '..', 'functions', 'local.settings.json')
    
    if os.path.exists(settings_path):
        with open(settings_path, 'r') as f:
            settings = json.load(f)
            values = settings.get('Values', {})
            for key, value in values.items():
                os.environ[key] = str(value)
        print(f"✅ Loaded {len(values)} settings from local.settings.json")
        return True
    else:
        print(f"❌ Settings file not found: {settings_path}")
        return False

# Load environment first
if not load_environment():
    print("❌ Failed to load environment settings")
    sys.exit(1)

# Now add the functions directory to path and import modules
functions_dir = os.path.join(os.path.dirname(__file__), '..', 'functions')
sys.path.insert(0, functions_dir)

try:
    from __app__.common.utils.config import get_config
    from __app__.common.adapters.ado_client import AdoClient
    print("✅ Successfully imported ADO modules")
except ImportError as e:
    print(f"❌ Failed to import modules: {e}")
    sys.exit(1)


async def check_work_item(ado_client, work_item_id):
    """Check a specific work item."""
    print(f"\n🔍 Checking Work Item {work_item_id}...")
    
    try:
        item = await ado_client.get_work_item(work_item_id)
        if item:
            print(f"✅ Work Item {work_item_id} found!")
            
            fields = item.get('fields', {})
            
            print(f"\n📋 WORK ITEM DETAILS:")
            print("=" * 80)
            print(f"ID: {item.get('id')}")
            print(f"Title: {fields.get('System.Title', 'N/A')}")
            print(f"Work Item Type: {fields.get('System.WorkItemType', 'N/A')}")
            print(f"State: {fields.get('System.State', 'N/A')}")
            print(f"Area Path: {fields.get('System.AreaPath', 'N/A')}")
            print(f"Iteration Path: {fields.get('System.IterationPath', 'N/A')}")
            print(f"Assigned To: {fields.get('System.AssignedTo', {}).get('displayName', 'Unassigned')}")
            print(f"Created Date: {fields.get('System.CreatedDate', 'N/A')}")
            print(f"Changed Date: {fields.get('System.ChangedDate', 'N/A')}")
            print(f"Priority: {fields.get('Microsoft.VSTS.Common.Priority', 'N/A')}")
            print(f"Severity: {fields.get('Microsoft.VSTS.Common.Severity', 'N/A')}")
            print(f"Description: {fields.get('System.Description', 'N/A')[:200]}...")
            
            print(f"\n🔗 Links:")
            print(f"Azure DevOps URL: https://dev.azure.com/virginatlantic/Air4%20Channels%20Testing/_workitems/edit/{work_item_id}")
            
            # Check if this is related to AI Testing
            iteration_path = fields.get('System.IterationPath', '').lower()
            area_path = fields.get('System.AreaPath', '').lower()
            title = fields.get('System.Title', '').lower()
            
            ai_testing_related = (
                'ai testing' in iteration_path or 
                'ai testing' in area_path or 
                'ai testing' in title or
                'defect management' in iteration_path or
                'defect management' in area_path
            )
            
            if ai_testing_related:
                print(f"\n🤖 ✅ This work item IS related to AI Testing!")
            else:
                print(f"\n🤖 ❌ This work item is NOT related to AI Testing")
            
            # Export to JSON
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"work_item_{work_item_id}_{timestamp}.json"
            
            export_data = {
                "timestamp": timestamp,
                "work_item": {
                    "id": item.get('id'),
                    "url": item.get('url'),
                    "fields": fields,
                    "ai_testing_related": ai_testing_related
                }
            }
            
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            print(f"\n💾 Work item details exported to: {filename}")
            
            return item
        else:
            print(f"❌ Work Item {work_item_id} not found")
            return None
            
    except Exception as e:
        print(f"❌ Error checking work item {work_item_id}: {e}")
        return None


async def scan_nearby_items(ado_client, center_id, range_size=50):
    """Scan work items around the center ID to find others."""
    print(f"\n🔍 Scanning for work items around ID {center_id} (±{range_size})...")
    
    start_id = max(1, center_id - range_size)
    end_id = center_id + range_size
    
    found_items = []
    
    for work_item_id in range(start_id, end_id + 1):
        try:
            item = await ado_client.get_work_item(work_item_id)
            if item:
                found_items.append(item)
                fields = item.get('fields', {})
                work_type = fields.get('System.WorkItemType', 'Unknown')
                title = fields.get('System.Title', 'No Title')[:40]
                print(f"   ✅ Found {work_item_id}: {work_type} - {title}")
        except Exception:
            continue
    
    print(f"\n📊 Found {len(found_items)} work items in range {start_id}-{end_id}")
    
    if found_items:
        # Analyze the found items
        work_types = {}
        iterations = set()
        areas = set()
        defects = []
        
        for item in found_items:
            fields = item.get('fields', {})
            work_type = fields.get('System.WorkItemType', 'Unknown')
            work_types[work_type] = work_types.get(work_type, 0) + 1
            
            iteration = fields.get('System.IterationPath', '')
            if iteration:
                iterations.add(iteration)
                
            area = fields.get('System.AreaPath', '')
            if area:
                areas.add(area)
            
            if work_type.lower() in ['bug', 'defect']:
                defects.append(item)
        
        print(f"\n📋 Work Item Types Found:")
        for wtype, count in sorted(work_types.items(), key=lambda x: x[1], reverse=True):
            print(f"   {wtype}: {count}")
        
        print(f"\n📍 Iteration Paths Found:")
        for iteration in sorted(iterations):
            print(f"   - {iteration}")
            
        print(f"\n📍 Area Paths Found:")
        for area in sorted(areas):
            print(f"   - {area}")
        
        if defects:
            print(f"\n🐛 Defects/Bugs Found: {len(defects)}")
            for item in defects:
                fields = item.get('fields', {})
                work_item_id = item.get('id')
                title = fields.get('System.Title', 'No Title')[:40]
                state = fields.get('System.State', 'N/A')
                print(f"   Bug {work_item_id}: {state} - {title}")
    
    return found_items


async def main():
    """Main function."""
    print("🚀 Checking Specific Work Item: 752662")
    print("=" * 60)
    
    try:
        # Get configuration and create client
        config = get_config()
        print(f"🔗 Connecting to Azure DevOps...")
        print(f"   Organization: {config.ADO_ORGANIZATION}")
        print(f"   Project: {config.ADO_PROJECT}")
        
        ado_client = AdoClient(config)
        
        # Check the specific work item
        work_item = await check_work_item(ado_client, 752662)
        
        if work_item:
            # If found, scan nearby items to understand the ID range
            nearby_items = await scan_nearby_items(ado_client, 752662, range_size=100)
            
            print(f"\n🎯 SUMMARY:")
            print("=" * 60)
            print(f"✅ Work Item 752662 exists and was analyzed")
            print(f"✅ Found {len(nearby_items)} total work items in range 752562-752762")
            
            # Look for AI Testing related items in the nearby range
            ai_testing_items = []
            for item in nearby_items:
                fields = item.get('fields', {})
                iteration_path = fields.get('System.IterationPath', '').lower()
                area_path = fields.get('System.AreaPath', '').lower()
                title = fields.get('System.Title', '').lower()
                
                if ('ai testing' in iteration_path or 'ai testing' in area_path or 
                    'ai testing' in title or 'defect management' in iteration_path or 
                    'defect management' in area_path):
                    ai_testing_items.append(item)
            
            if ai_testing_items:
                print(f"🤖 Found {len(ai_testing_items)} AI Testing related items in the nearby range!")
                for item in ai_testing_items:
                    fields = item.get('fields', {})
                    work_item_id = item.get('id')
                    title = fields.get('System.Title', 'No Title')[:50]
                    work_type = fields.get('System.WorkItemType', 'Unknown')
                    print(f"   {work_item_id}: {work_type} - {title}")
            else:
                print(f"🤖 No AI Testing related items found in the nearby range")
        else:
            print(f"\n❌ Work Item 752662 not found")
            print(f"💡 This might indicate:")
            print(f"   - The work item doesn't exist")
            print(f"   - Insufficient permissions")
            print(f"   - Wrong project or organization")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
