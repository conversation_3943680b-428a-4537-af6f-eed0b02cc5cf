"""
Integration tests for workitem event handler with Logic App integration.
"""

import pytest
import json
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch
from datetime import datetime

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'functions'))

from __app__.workitem_event.handler import workitem_event_handler, process_work_item
from __app__.common.utils.config import Config


@pytest.fixture
def mock_config():
    """Create a mock configuration."""
    config = Mock(spec=Config)
    config.ADO_ORGANIZATION = "test-org"
    config.ADO_PROJECT = "test-project"
    config.LOGICAPP_URL = "https://test-logic-app.azure.com/workflows/test/triggers/manual"
    config.BYPASS_LOGICAPP_AND_UPDATE_ADO = False
    config.READ_ONLY = True
    config.ALLOW_COMMENTS_IN_READ_ONLY = True
    config.SAFETY_ONLY_WORKITEM_ID = ""
    config.ALLOW_PROJECTS = ""
    config.get_allowed_projects.return_value = []
    config.is_project_allowed.return_value = True
    config.is_work_item_allowed.return_value = True
    return config


@pytest.fixture
def sample_work_item_data():
    """Sample work item data."""
    return {
        "id": 12345,
        "fields": {
            "System.Id": 12345,
            "System.Title": "Test Bug - Login not working",
            "System.Description": "Users cannot log in to the application",
            "System.WorkItemType": "Bug",
            "System.State": "New",
            "System.AssignedTo": {
                "displayName": "John Doe",
                "uniqueName": "<EMAIL>"
            }
        }
    }


@pytest.fixture
def sample_ado_event(sample_work_item_data):
    """Sample ADO Service Hook event."""
    return {
        "eventType": "workitem.updated",
        "publisherId": "tfs",
        "scope": "all",
        "message": {
            "text": "Work item updated",
            "html": "Work item updated",
            "markdown": "Work item updated"
        },
        "detailedMessage": {
            "text": "Work item 12345 updated by Test User",
            "html": "Work item 12345 updated by Test User",
            "markdown": "Work item 12345 updated by Test User"
        },
        "resource": {
            "id": 12345,
            "rev": 42,
            "fields": sample_work_item_data["fields"],
            "url": "https://dev.azure.com/test-org/test-project/_apis/wit/workItems/12345"
        },
        "resourceVersion": "1.0",
        "resourceContainers": {
            "collection": {
                "id": "test-collection-id",
                "baseUrl": "https://dev.azure.com/test-org/"
            },
            "project": {
                "id": "test-project-id",
                "baseUrl": "https://dev.azure.com/test-org/"
            }
        },
        "createdDate": datetime.utcnow().isoformat() + "Z"
    }


class TestWorkItemEventHandler:
    """Test cases for the work item event handler."""
    
    @pytest.mark.asyncio
    async def test_handler_calls_logic_app_with_recommendations(self, mock_config, sample_ado_event, sample_work_item_data):
        """Test that handler calls Logic App when recommendations exist."""
        
        # Mock dependencies
        with patch('__app__.workitem_event.handler.get_config', return_value=mock_config), \
             patch('__app__.workitem_event.handler.IdempotencyStore') as mock_store_class, \
             patch('__app__.workitem_event.handler.process_work_item') as mock_process:
            
            # Setup mocks
            mock_store = AsyncMock()
            mock_store_class.return_value = mock_store
            mock_store.is_duplicate.return_value = False
            mock_store.mark_processed.return_value = None
            
            # Mock process_work_item to return recommendations
            mock_process.return_value = {
                "work_item_id": 12345,
                "event_type": "workitem.updated",
                "title": "Test Bug - Login not working",
                "recommendations": {
                    "suggested_assignees": [
                        {"name": "Jane Smith", "email": "<EMAIL>", "score": 0.85}
                    ],
                    "priority_recommendation": {
                        "priority": 2,
                        "confidence": 0.75,
                        "rationale": "Similar bugs were resolved with priority 2"
                    }
                },
                "logic_app_response": {
                    "status": "accepted",
                    "trackingId": "test-tracking-123"
                },
                "pipeline_steps": {
                    "send_notification": "completed"
                }
            }
            
            # Call the handler
            response = await workitem_event_handler(sample_ado_event)
            
            # Verify response
            assert response.status_code == 200
            response_data = json.loads(response.get_body().decode())
            assert response_data["status"] == "success"
            
            # Verify process_work_item was called
            mock_process.assert_called_once_with(mock_config, 12345, "workitem.updated")
    
    @pytest.mark.asyncio
    async def test_process_work_item_with_logic_app_integration(self, mock_config, sample_work_item_data):
        """Test process_work_item with Logic App integration."""
        
        # Mock dependencies
        with patch('__app__.workitem_event.handler.AdoClient') as mock_ado_class, \
             patch('__app__.workitem_event.handler.HistoryService') as mock_history_class, \
             patch('__app__.workitem_event.handler.TriageService') as mock_triage_class, \
             patch('__app__.workitem_event.handler.send_personal_card') as mock_send_card:
            
            # Setup ADO client mock
            mock_ado = AsyncMock()
            mock_ado_class.return_value = mock_ado
            mock_ado.get_work_item.return_value = sample_work_item_data
            
            # Setup history service mock
            mock_history = AsyncMock()
            mock_history_class.return_value = mock_history
            mock_history.find_similar_items.return_value = [
                {"id": 11111, "title": "Similar login issue", "score": 0.9},
                {"id": 22222, "title": "Another login bug", "score": 0.8}
            ]
            
            # Setup triage service mock
            mock_triage = AsyncMock()
            mock_triage_class.return_value = mock_triage
            mock_triage.get_triage_recommendations.return_value = {
                "suggested_assignees": [
                    {"name": "Jane Smith", "email": "<EMAIL>", "score": 0.85},
                    {"name": "Bob Wilson", "email": "<EMAIL>", "score": 0.75}
                ],
                "priority_recommendation": {
                    "priority": 2,
                    "confidence": 0.75,
                    "rationale": "Based on similar resolved issues"
                }
            }
            
            # Setup Logic App mock
            mock_send_card.return_value = {
                "status": "accepted",
                "trackingId": "test-tracking-456"
            }
            
            # Call process_work_item
            result = await process_work_item(mock_config, 12345, "workitem.updated")
            
            # Verify Logic App was called exactly once
            mock_send_card.assert_called_once()
            call_args = mock_send_card.call_args
            
            # Verify call arguments
            assert call_args[1]["to_email"] == "<EMAIL>"  # Top suggested assignee
            assert call_args[1]["subject"] == "[#12345] Test Bug - Login not working"
            assert call_args[1]["work_item_id"] == 12345
            assert "Recommended Priority: 2" in call_args[1]["body"]
            assert "Similar Items Found: 2" in call_args[1]["body"]
            
            # Verify result
            assert result["logic_app_response"]["status"] == "accepted"
            assert result["logic_app_response"]["trackingId"] == "test-tracking-456"
            assert result["pipeline_steps"]["send_notification"] == "completed"
    
    @pytest.mark.asyncio
    async def test_process_work_item_fallback_to_current_assignee(self, mock_config, sample_work_item_data):
        """Test fallback to current assignee when no suggestions exist."""
        
        # Mock dependencies
        with patch('__app__.workitem_event.handler.AdoClient') as mock_ado_class, \
             patch('__app__.workitem_event.handler.HistoryService') as mock_history_class, \
             patch('__app__.workitem_event.handler.TriageService') as mock_triage_class, \
             patch('__app__.workitem_event.handler.send_personal_card') as mock_send_card:
            
            # Setup mocks
            mock_ado = AsyncMock()
            mock_ado_class.return_value = mock_ado
            mock_ado.get_work_item.return_value = sample_work_item_data
            
            mock_history = AsyncMock()
            mock_history_class.return_value = mock_history
            mock_history.find_similar_items.return_value = []
            
            mock_triage = AsyncMock()
            mock_triage_class.return_value = mock_triage
            mock_triage.get_triage_recommendations.return_value = {
                "suggested_assignees": [],  # No suggestions
                "priority_recommendation": None
            }
            
            mock_send_card.return_value = {
                "status": "accepted",
                "trackingId": "test-tracking-789"
            }
            
            # Call process_work_item
            result = await process_work_item(mock_config, 12345, "workitem.updated")
            
            # Verify Logic App was called with current assignee
            mock_send_card.assert_called_once()
            call_args = mock_send_card.call_args
            assert call_args[1]["to_email"] == "<EMAIL>"  # Current assignee
            assert result["notification_target"] == "current_assignee"
    
    @pytest.mark.asyncio
    async def test_process_work_item_bypass_logic_app(self, mock_config, sample_work_item_data):
        """Test bypass mode for Logic App."""
        
        # Enable bypass mode
        mock_config.BYPASS_LOGICAPP_AND_UPDATE_ADO = True
        
        # Mock dependencies
        with patch('__app__.workitem_event.handler.AdoClient') as mock_ado_class, \
             patch('__app__.workitem_event.handler.HistoryService') as mock_history_class, \
             patch('__app__.workitem_event.handler.TriageService') as mock_triage_class, \
             patch('__app__.workitem_event.handler.send_personal_card') as mock_send_card:
            
            # Setup mocks
            mock_ado = AsyncMock()
            mock_ado_class.return_value = mock_ado
            mock_ado.get_work_item.return_value = sample_work_item_data
            
            mock_history = AsyncMock()
            mock_history_class.return_value = mock_history
            mock_history.find_similar_items.return_value = []
            
            mock_triage = AsyncMock()
            mock_triage_class.return_value = mock_triage
            mock_triage.get_triage_recommendations.return_value = {
                "suggested_assignees": [
                    {"name": "Jane Smith", "email": "<EMAIL>", "score": 0.85}
                ]
            }
            
            # Call process_work_item
            result = await process_work_item(mock_config, 12345, "workitem.updated")
            
            # Verify Logic App was NOT called
            mock_send_card.assert_not_called()
            assert result["pipeline_steps"]["send_notification"] == "bypassed"
    
    @pytest.mark.asyncio
    async def test_process_work_item_no_target_email(self, mock_config, sample_work_item_data):
        """Test when no target email is available."""
        
        # Remove assignee from work item
        sample_work_item_data["fields"]["System.AssignedTo"] = None
        
        # Mock dependencies
        with patch('__app__.workitem_event.handler.AdoClient') as mock_ado_class, \
             patch('__app__.workitem_event.handler.HistoryService') as mock_history_class, \
             patch('__app__.workitem_event.handler.TriageService') as mock_triage_class, \
             patch('__app__.workitem_event.handler.send_personal_card') as mock_send_card:
            
            # Setup mocks
            mock_ado = AsyncMock()
            mock_ado_class.return_value = mock_ado
            mock_ado.get_work_item.return_value = sample_work_item_data
            
            mock_history = AsyncMock()
            mock_history_class.return_value = mock_history
            mock_history.find_similar_items.return_value = []
            
            mock_triage = AsyncMock()
            mock_triage_class.return_value = mock_triage
            mock_triage.get_triage_recommendations.return_value = {
                "suggested_assignees": [],  # No suggestions and no current assignee
            }
            
            # Call process_work_item
            result = await process_work_item(mock_config, 12345, "workitem.updated")
            
            # Verify Logic App was NOT called
            mock_send_card.assert_not_called()
            assert result["pipeline_steps"]["send_notification"] == "skipped"
            assert result["notification_skip_reason"] == "no_target_email"


if __name__ == "__main__":
    pytest.main([__file__])
