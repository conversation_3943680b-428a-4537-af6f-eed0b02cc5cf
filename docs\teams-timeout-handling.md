# Teams Logic App Timeout Handling

## Problem Statement

The Teams Logic App was using the `Post_adaptive_card_and_wait_for_a_response` action, which waits synchronously for user responses. This approach has several issues:

1. **Logic App Timeout**: Logic Apps have default timeout limits (typically 30 minutes for HTTP actions)
2. **Resource Blocking**: The Logic App workflow remains active while waiting for user response
3. **No Fallback**: If the user doesn't respond within the timeout period, the workflow fails
4. **Poor User Experience**: Users may not respond immediately, causing system failures

## Solution Overview

We've implemented an **asynchronous response pattern** that decouples notification sending from response processing:

### 1. Asynchronous Notification Sending
- Logic App sends Teams message without waiting for response
- Notification tracking is stored immediately after sending
- Logic App completes successfully regardless of user response timing

### 2. Separate Response Handling
- User responses are handled by dedicated Azure Functions
- Responses are processed independently of the original Logic App workflow
- No timeout constraints on user response time

### 3. Timeout Management
- Automatic detection of notifications that haven't received responses
- Configurable timeout thresholds (default: 30 minutes)
- Multiple timeout handling strategies

## Implementation Details

### Modified Logic App Workflow

```json
{
  "Post_adaptive_card_async": {
    "type": "ApiConnection",
    "inputs": {
      "method": "post",
      "path": "/v1.0/teams/.../messages",
      "body": {
        "body": {
          "contentType": "html",
          "content": "@triggerBody()?['adaptive_card']"
        }
      }
    }
  },
  "Store_notification_tracking": {
    "type": "Http",
    "inputs": {
      "method": "POST",
      "uri": "@{parameters('function_app_url')}/api/store_notification_tracking",
      "body": {
        "work_item_id": "@variables('work_item_id')",
        "notification_id": "@triggerBody()?['notification_id']",
        "teams_message_id": "@body('Post_adaptive_card_async')?['id']",
        "sent_timestamp": "@utcNow()",
        "status": "sent"
      }
    }
  }
}
```

### New Azure Functions

#### 1. `store_notification_tracking`
- **Purpose**: Store notification tracking information
- **Endpoint**: `/api/store_notification_tracking`
- **Method**: POST
- **Payload**:
```json
{
  "work_item_id": 12345,
  "notification_id": "teams-12345-abc123",
  "teams_message_id": "teams-msg-id",
  "sent_timestamp": "2024-01-01T12:00:00Z",
  "status": "sent"
}
```

#### 2. `check_notification_timeouts`
- **Purpose**: Check for and handle timed-out notifications
- **Endpoint**: `/api/check_notification_timeouts`
- **Method**: GET
- **Trigger**: Timer (every 15 minutes) + Manual

#### 3. `notification_timeout_timer`
- **Purpose**: Automatic timer trigger for timeout checking
- **Schedule**: Every 15 minutes (`0 */15 * * * *`)

### Timeout Handling Strategies

When a notification times out, the system can take multiple actions:

#### 1. Send Follow-up Notification
```typescript
// Sends a simplified follow-up message to Teams
await this._send_timeout_follow_up(work_item_id, notification_record);
```

#### 2. Apply Default Assignment
```typescript
// Auto-assigns to default team member
await this._apply_default_timeout_action(work_item_id, notification_record);
```

#### 3. Log for Manual Review
```typescript
// Creates audit log for manual intervention
await this._log_for_manual_review(work_item_id, notification_record);
```

## Configuration

### Environment Variables

Add these to your `local.settings.json` or Azure Function App settings:

```json
{
  "Values": {
    "NOTIFICATION_TIMEOUT_MINUTES": "30",
    "DEFAULT_TIMEOUT_ASSIGNEE": "<EMAIL>",
    "TIMEOUT_FOLLOW_UP_ENABLED": "true",
    "TIMEOUT_AUTO_ASSIGN_ENABLED": "true"
  }
}
```

### Configuration Options

| Setting | Default | Description |
|---------|---------|-------------|
| `NOTIFICATION_TIMEOUT_MINUTES` | 30 | Minutes before notification is considered timed out |
| `DEFAULT_TIMEOUT_ASSIGNEE` | <EMAIL> | Default assignee for timed-out items |
| `TIMEOUT_FOLLOW_UP_ENABLED` | true | Whether to send follow-up notifications |
| `TIMEOUT_AUTO_ASSIGN_ENABLED` | true | Whether to auto-assign timed-out items |

## Benefits

### 1. **Reliability**
- Logic App workflows complete successfully regardless of user response timing
- No more workflow failures due to user response delays
- Robust error handling and fallback mechanisms

### 2. **Scalability**
- Logic App resources are not blocked waiting for responses
- Can handle multiple concurrent notifications efficiently
- Reduced resource consumption

### 3. **Flexibility**
- Configurable timeout thresholds
- Multiple timeout handling strategies
- Easy to extend with additional actions

### 4. **User Experience**
- Users can respond at their convenience
- No pressure from system timeouts
- Clear follow-up communications when needed

### 5. **Monitoring & Auditing**
- Complete tracking of notification lifecycle
- Audit logs for timeout events
- Metrics for response rates and timing

## Deployment Steps

### 1. Update Logic App
```powershell
# Deploy the updated Logic App template
.\infrastructure\logic-apps\deploy-logic-app-teams.ps1
```

### 2. Deploy Azure Functions
```bash
# Deploy the updated function app
func azure functionapp publish your-function-app-name
```

### 3. Configure Environment Variables
```bash
# Set timeout configuration
az functionapp config appsettings set \
  --name your-function-app-name \
  --resource-group your-resource-group \
  --settings NOTIFICATION_TIMEOUT_MINUTES=30
```

### 4. Test the Implementation
```bash
# Test notification tracking
curl -X POST "https://your-function-app.azurewebsites.net/api/store_notification_tracking" \
  -H "Content-Type: application/json" \
  -d '{"work_item_id": 12345, "notification_id": "test-123", "status": "sent"}'

# Test timeout checking
curl "https://your-function-app.azurewebsites.net/api/check_notification_timeouts"
```

## Monitoring

### Application Insights Queries

#### Track Notification Timeouts
```kusto
traces
| where message contains "Notification timeout"
| project timestamp, message, customDimensions
| order by timestamp desc
```

#### Monitor Response Rates
```kusto
traces
| where message contains "notification" and message contains "sent"
| summarize SentCount = count() by bin(timestamp, 1h)
```

## Troubleshooting

### Common Issues

1. **Notifications not being tracked**
   - Check Logic App execution logs
   - Verify `store_notification_tracking` function is accessible
   - Ensure notification IDs are being generated correctly

2. **Timeouts not being processed**
   - Check timer trigger is enabled
   - Verify timeout configuration values
   - Review function execution logs

3. **Follow-up notifications not sent**
   - Check Teams webhook configuration
   - Verify `TIMEOUT_FOLLOW_UP_ENABLED` setting
   - Review Teams client logs

### Debug Commands

```bash
# Check function app logs
az functionapp log tail --name your-function-app-name --resource-group your-resource-group

# Test timeout processing manually
curl "https://your-function-app.azurewebsites.net/api/check_notification_timeouts"
```

## Future Enhancements

1. **Persistent Storage**: Replace in-memory storage with Azure Table Storage or Cosmos DB
2. **Advanced Analytics**: Add detailed metrics and reporting
3. **Smart Timeouts**: Dynamic timeout based on work item priority
4. **Escalation Chains**: Multi-level timeout handling with escalation
5. **User Preferences**: Allow users to set their preferred response timeframes
